package com.cheche365.bc.handler.robot;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.bouncycastle.pqc.jcajce.provider.BouncyCastlePQCProvider;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class DaJiaRobotHandler implements BaseHandler {

    private static final String KEY_END = "DJCX";
    @Override
    public String getMethodType() {
        return ProcessTypeEnum.ROBOT.getKey() + InsCompanyEnum.DAIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        String key = getKey();
        requestBody = encrypt(requestBody, key);
        log.info("requestBody: {}", requestBody);
        JSONObject requestObj = new JSONObject();
        requestObj.put("data", requestBody);
        requestBody = JSONObject.toJSONString(requestObj);
        log.info("requestBody: {}", requestBody);
        String response =  HttpSender.doPostWithRetry(1, closeableHttpClient, true, url, requestBody, Objects.nonNull(autoTask.getArrayParams())? autoTask.getArrayParams() : autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey(), autoTask.getRepHeaders());
        log.info("response: {}", response);
        JSONObject responseObj = JSONObject.parseObject(response);
        if ("200".equals(responseObj.get("code"))) {
            String result = decrypt(responseObj.getString("result"), key);
            responseObj.put("result", result);
        }
        log.info("responseObj: {}", responseObj.toJSONString());
        return responseObj.toJSONString();
    }

    private String getKey() {
        Date date = new Date();
        date.setMonth(date.getMonth() + date.getDate());
        LocalDate localDate = date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        String key = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) + KEY_END;
        return Base64.getEncoder().encodeToString(key.getBytes());
    }

    private String encrypt(String str, String key) throws Exception {
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes());
        SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = secretKeyFactory.generateSecret(desKeySpec);;
        Security.addProvider(new BouncyCastlePQCProvider());
        Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding", "BC");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] bytes = cipher.doFinal(str.getBytes(StandardCharsets.UTF_8));
        return Hex.toHexString(bytes);
    }

    private String decrypt(String str, String key) throws Exception {
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes());
        SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = secretKeyFactory.generateSecret(desKeySpec);
        Security.addProvider(new BouncyCastlePQCProvider());
        Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding", "BC");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] bytes = cipher.doFinal(Hex.decode(str));
        return new String(bytes, StandardCharsets.UTF_8);
    }
}
