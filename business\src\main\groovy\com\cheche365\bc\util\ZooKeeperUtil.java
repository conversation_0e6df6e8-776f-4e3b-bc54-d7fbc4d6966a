package com.cheche365.bc.util;

import com.cheche365.bc.config.SpringUtil;
import com.cheche365.bc.config.ZooKeeperConfigProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Map;

@Getter
@Setter
@Slf4j
public class ZooKeeperUtil {

    private static ZooKeeperConfigProperties zooKeeperConfigProperties;

    static {
        try {
            zooKeeperConfigProperties = SpringUtil.getApplicationContext().getBean(ZooKeeperConfigProperties.class);
        } catch (Exception e) {
            log.error("zookeeperConfig get bean 异常：{}", ExceptionUtils.getStackTrace(e));
        }
    }

    @SuppressWarnings("rawtypes")
    public static String getUrl(String source) {
        if (null == zooKeeperConfigProperties.getHost()) {
            return null;
        }

        Map<String, Map<String, Object>> host = zooKeeperConfigProperties.getHost();
        log.info("ZookeeperUtil getUrl host:{}", host);
        Map<String, Object> cheTimes = host.get("chetimes");
        Map subHost = (Map) cheTimes.get("subHost");
        Map sourceMap = (Map) subHost.get(source);
        if (null == sourceMap) {
            log.error("zookeeperConfig 未找到该source值：{}", source);
            return null;
        }

        return cheTimes.get("protocol") + "://" + sourceMap.get("prefix") + "." + cheTimes.get("domain");
    }

}
