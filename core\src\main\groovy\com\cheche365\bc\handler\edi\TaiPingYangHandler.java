package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import com.cheche365.bc.utils.tai_bao.MessageConstants;
import com.cheche365.bc.utils.tai_bao.SignatureUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

@Component
public class TaiPingYangHandler implements BaseHandler {

    private static final String POLICY_DOWNLOAD = "policyDownload";

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.CPIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url,
                          CloseableHttpClient closeableHttpClient) throws Exception {

        //太保是否需要加密  true-需要 false-不需要  默认需要
        Boolean needEncryption = MapUtils.getBoolean(autoTask.getTempValues(), "needEncryption", true);

        // 加密处理
        String encryptedRequest = needEncryption ?
                encryptRequest(requestBody, autoTask) : requestBody;

        // 发送请求
        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url,
                encryptedRequest, autoTask.getParams(), autoTask.getReqHeaders(), charSet,
                TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());

        // 解密处理
        return needEncryption ?
                decryptResponse(responseBody, autoTask) : responseBody;
    }

    /**
     * 加密请求数据
     */
    private String encryptRequest(String requestBody, AutoTask autoTask) throws Exception {
        if (autoTask.getTaskType().contains(POLICY_DOWNLOAD)) {
            return SignatureUtils.encryptByBody(requestBody,
                    (String) autoTask.getConfigs().get("policyDownloadPublicKey"));
        }

        JSONObject requestObject = JSONObject.parseObject(requestBody);
        String bizContent = requestObject.getString(MessageConstants.BIZ_CONTENT);
        return SignatureUtils.encryptAndSign(bizContent,
                (String) autoTask.getConfigs().get("publicKey"),
                (String) autoTask.getConfigs().get("thirdPrivateKey"),
                requestObject.toJSONString(), true, true);
    }

    /**
     * 解密响应数据
     */
    private String decryptResponse(String responseBody, AutoTask autoTask) throws Exception {
        if (StringUtils.isBlank(responseBody) ||
                autoTask.getTaskType().contains(POLICY_DOWNLOAD)) {
            return responseBody;
        }

        return SignatureUtils.checkSignAndDecrypt(responseBody,
                (String) autoTask.getConfigs().get("publicKey"),
                (String) autoTask.getConfigs().get("thirdPrivateKey"),
                true, true);
    }
}
