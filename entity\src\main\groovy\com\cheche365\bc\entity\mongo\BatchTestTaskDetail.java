package com.cheche365.bc.entity.mongo;


import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection = "batch_test_task_detail")
@Builder
@Data
public class BatchTestTaskDetail {

    @Id
    private String id;

    private Long batchTestTaskId;

    private String enquiryId;

    private InsCompany insCompany;

    private String taskStatus;

    private String errorMessage;

    private Object autoTaskData;

    @CreatedDate
    private Date createTime;

    @LastModifiedDate
    private Date updateTime;

    @Data
    @Builder
    public static class InsCompany {
        private String insCompanyId;
        private String insCompanyName;
    }
}
