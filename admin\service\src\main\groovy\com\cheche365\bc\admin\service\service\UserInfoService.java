package com.cheche365.bc.admin.service.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.entity.mysql.UserInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.model.RestResponse;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 根据邮箱获取用户信息
     *
     * @param email
     * @param status
     * @return
     */
    UserInfo getUserInfoByEmail(String email, int status);

    /**
     * 更新或保存用户信息
     *
     * @param userInfo
     */
    RestResponse saveOrUpdateUser(UserInfo userInfo, UserInfo operateUser);

    /**
     * 更新用户状态
     *
     * @param id
     */
    void changeStatus(Long id);

    RestResponse updatePassword(JSONObject object, String userName);

    List<UserInfo> pageList(QueryWrapper<UserInfo> wrapper, Map<String, String> map, Page<UserInfo> page);

    UserInfo getUserInfoById(Long id);
}
