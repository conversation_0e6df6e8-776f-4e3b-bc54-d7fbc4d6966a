package com.cheche365.bc.admin.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.dto.AutoTaskData;
import com.cheche365.bc.admin.service.dto.callback.QuoteCallback;
import com.cheche365.bc.admin.service.service.BatchTestTaskService;
import com.cheche365.bc.entity.mongo.BatchTestTaskDetail;
import com.cheche365.bc.entity.mysql.BatchTestTask;
import com.cheche365.bc.mapper.BatchTestTaskMapper;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.repo.BatchTestTaskDetailRepo;
import com.cheche365.bc.service.ISerialNoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

import static cn.hutool.core.date.DatePattern.CHINESE_DATE_TIME_PATTERN;
import static com.cheche365.bc.admin.service.dto.base.AreaInfo.createByCityId;

@Slf4j
@Service
@AllArgsConstructor
public class BatchTestTaskServiceImpl extends ServiceImpl<BatchTestTaskMapper, BatchTestTask> implements BatchTestTaskService {

    private final ISerialNoService serialNoService;
    private final BatchTestTaskDetailRepo batchTestTaskDetailRepo;

    @Override
    public BatchTestTask createBatchTestTask() {
        BatchTestTask batchTestTask = BatchTestTask.builder()
            .name(defaultTaskName())
            .batchNo(serialNoService.generateTestBatchNo())
            .build();
        save(batchTestTask);
        return batchTestTask;
    }

    @Override
    public void updateBatchTestTaskDetailStatus(QuoteCallback callback) {
        BatchTestTaskDetail batchTestTaskDetail =
            batchTestTaskDetailRepo.findFirstByEnquiryIdOrderByCreateTimeDesc(callback.getEnquiryId());

        batchTestTaskDetail.setTaskStatus("quoteSuccess".equals(callback.getStatus()) ?
            TaskStatus.QUOTE_SUCCESS.getState() :
            TaskStatus.QUOTE_FAILED.getState());
        batchTestTaskDetail.setErrorMessage(callback.getMessage());

        AutoTaskData autoTaskData = (AutoTaskData) batchTestTaskDetail.getAutoTaskData();

        if (Objects.nonNull(callback.getCarInfo())) {
            BeanUtil.copyProperties(callback.getCarInfo(), autoTaskData.getCarInfo());
        }
        if (Objects.nonNull(callback.getArea())) {
            autoTaskData.setInsArea(createByCityId(callback.getArea().getCityCode()));
        }

        batchTestTaskDetailRepo.save(batchTestTaskDetail);
    }

    private static String defaultTaskName() {
        return "报量验证任务: " + DateUtil.format(new Date(), CHINESE_DATE_TIME_PATTERN);
    }
}
