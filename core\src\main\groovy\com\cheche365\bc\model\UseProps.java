package com.cheche365.bc.model;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;

/**
 * 车辆使用性质
 * Created by austinChen on 2015/9/18.
 */
@ClassDoc(remark="使用性质枚举")
public enum UseProps {

     @FieldDoc(des="不区分营业非营业")
     NotCase,// "0不区分营业非营业")
     Domestic , //"1家庭自用")
     Taxi , //"2营业出租租赁")
     Bus , //"3营业城市公交")
     Passenger , //"4营业公路客运")
     Journey ,// "5营业旅游")
     Freight , //"6营业货运")
     Business , //"7营业用")
     NonOperating, //"8非营业用")
     Personal , //"9非营业个人")
     Enterprise , //"10非营业企业")
     Organization , //"11非营业机关")
     SelfFreight , //"12非营业货运")
     DualPurpose,//"13兼用型（拖拉机专用）")
     Transportation , //"14运输型（拖拉机专用）")
     Special , //"15营业特种车")
     NotOperatingSpecial,  //"16非营业特种车")
     PreTaxi    //17预约出租客运
}
