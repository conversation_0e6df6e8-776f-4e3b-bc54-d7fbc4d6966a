package com.cheche365.bc.admin.rest.controller;


import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.mysql.UserLog;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.admin.service.service.UserLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-05
 */
@RestController
@RequestMapping("/userlog")
public class UserLogController extends BaseController<UserLogService, UserLog> {

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.LIST_USER_LOG + COMMA + PermissionCode.USERLOG_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        return super.getPage(map, authentication);
    }
}

