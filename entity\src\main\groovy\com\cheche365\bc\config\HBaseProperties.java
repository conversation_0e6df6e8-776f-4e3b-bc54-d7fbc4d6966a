package com.cheche365.bc.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "hbase.conf")
@Getter
@Setter
public class HBaseProperties {

    private String zkQuorum;

    private String zkZoneParent;

    private String username;

    private String password;

    private String namespace;

    private String retryCount;

    private String retryPause;

    private String zkSessionTimeout;

    /**
     * 是否启用登录认证
     */
    private boolean enableAuth = false;

}
