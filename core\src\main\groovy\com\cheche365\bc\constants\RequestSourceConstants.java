package com.cheche365.bc.constants;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * 请求源常量类
 *
 * <AUTHOR>
 */
public class RequestSourceConstants {

    /**
     * 默认产品码
     */
    public static final String DEFAULT_PRODUCT = "ps";

    /**
     * 默认场景码
     */
    public static final String DEFAULT_SCENARIO = "ps";

    /**
     * 车生态
     */
    public static final String PRODUCT_OF_CHE_ECO = "che_eco";

    /**
     * 车保易
     */
    public static final String PRODUCT_OF_CBY = "cby";

    /**
     * 澎湃保
     */
    public static final String PRODUCT_PPB = "ppb";

    /**
     * 磐石
     */
    public static final String PRODUCT_OF_BEDROCK = "bedrock";

    public static final Set<String> BEDROCK_PRODUCT_SET = Sets.newHashSet(PRODUCT_OF_CBY, PRODUCT_OF_BEDROCK, DEFAULT_PRODUCT);

    /**
     * 磐石 表中id
     */
    public static final int DEFAULT_BEDROCK = 1;

    /**
     * 车生态 表中id
     */
    public static final int DEFAULT_CHE_ECO = 2;


    /**
     * 源常量
     */
    public interface Keys {
        /**
         * 请求源key
         */
        String REQUEST = "requestSource";
        /**
         * 产品码key
         */
        String PRODUCT = "sourceProduct";
        /**
         * 场景码key
         */
        String SCENARIO = "sourceScenario";
        /**
         * 渠道码key
         */
        String CHANNEL = "sourceChannel";
        /**
         * 描述key
         */
        String DESC = "desc";
    }
}
