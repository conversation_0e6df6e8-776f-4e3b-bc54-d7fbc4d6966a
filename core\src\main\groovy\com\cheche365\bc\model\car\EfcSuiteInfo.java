package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 交强险投保信息
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "交强险险种配置")
@Getter
@Setter
@XmlRootElement
public class EfcSuiteInfo implements Serializable {
    /**
     * 交强险投保开始日期
     */
    @FieldDoc(des = "起保时间", remark = "报价前最好获取到")
    private String start;
    /**
     * 交强险投保结束日期
     */
    @FieldDoc(des = "结束时间", remark = "报价前最好获取到")
    private String end;
    /**
     * 交强险总保额
     */
    @FieldDoc(des = "交强险投保额")
    private BigDecimal amount;
    /**
     * 交强险原始保费
     */
    @FieldDoc(des = "原始保费", remark = "报价成功后必有")
    private BigDecimal orgCharge;
    /**
     * 交强险折后保费
     */
    @FieldDoc(des = "折后保费", remark = "报价成功后必有")
    private BigDecimal discountCharge;
    /**
     * 交强险折扣率
     */
    @FieldDoc(des = "折扣率", remark = "报价成功后必有")
    private BigDecimal discountRate;

}
