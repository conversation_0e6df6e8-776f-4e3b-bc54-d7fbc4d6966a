package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 车船税投保信息
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark="车船税信息")
@Getter
@Setter
@XmlRootElement
public class TaxSuiteInfo extends SuitePriceInfo implements Serializable {
    /**车船税投保开始时间*/
    @FieldDoc(des="起保时间",remark = "报价前最好获取到")
    private String start;
    /**车船税投保结束时间*/
    @FieldDoc(des="结束时间",remark = "报价前最好获取到")
    private String end;

    /**车船税滞纳金*/
    @FieldDoc(des="车船税滞纳金",remark = "")
    private BigDecimal delayCharge;

}
