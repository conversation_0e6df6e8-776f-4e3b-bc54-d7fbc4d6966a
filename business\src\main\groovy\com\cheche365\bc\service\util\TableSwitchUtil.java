package com.cheche365.bc.service.util;

import com.cheche365.bc.sharding.OldTableSwitch;
import com.cheche365.bc.sharding.QueryTableSwitch;
import lombok.extern.slf4j.Slf4j;

/**
 * 表切换工具类
 * 提供统一的表切换控制和状态查询方法
 * 
 * <AUTHOR>
 */
@Slf4j
public class TableSwitchUtil {

    /**
     * 获取当前的表切换状态描述
     * 
     * @return 状态描述字符串
     */
    public static String getCurrentSwitchStatus() {
        boolean oldTableSwitch = OldTableSwitch.isUseOldTable();
        boolean queryTableSwitch = QueryTableSwitch.isUseOldTableForQuery();
        
        StringBuilder status = new StringBuilder();
        status.append("表切换状态: ");
        status.append("双写开关=").append(oldTableSwitch ? "开启" : "关闭");
        status.append(", 查询开关=").append(queryTableSwitch ? "旧表" : "新表");
        
        // 分析当前的数据流向
        status.append(" | 数据流向: ");
        if (oldTableSwitch) {
            status.append("写入(旧表+新表), ");
        } else {
            status.append("写入(新表), ");
        }
        
        if (queryTableSwitch) {
            status.append("查询(旧表)");
        } else {
            status.append("查询(新表+HBase)");
        }
        
        return status.toString();
    }

    /**
     * 检查是否处于完全迁移状态
     * 即：双写关闭，查询使用新表
     * 
     * @return true表示完全迁移，false表示仍在迁移过程中
     */
    public static boolean isFullyMigrated() {
        return !OldTableSwitch.isUseOldTable() && QueryTableSwitch.shouldQueryFromNewTable();
    }

    /**
     * 检查是否处于双写但查询新表的状态
     * 这是用户需求的目标状态：仍然双写，但查询只从新表获取
     * 
     * @return true表示符合用户需求状态
     */
    public static boolean isTargetMigrationState() {
        return OldTableSwitch.isUseOldTable() && QueryTableSwitch.shouldQueryFromNewTable();
    }

    /**
     * 设置为目标迁移状态
     * 双写开启，查询使用新表
     */
    public static void setTargetMigrationState() {
        log.info("设置为目标迁移状态：双写开启，查询使用新表");
        OldTableSwitch.setUseOldTable(true);
        QueryTableSwitch.setUseOldTableForQuery(false);
        log.info("目标迁移状态设置完成: {}", getCurrentSwitchStatus());
    }

    /**
     * 设置为完全迁移状态
     * 双写关闭，查询使用新表
     */
    public static void setFullyMigratedState() {
        log.info("设置为完全迁移状态：双写关闭，查询使用新表");
        OldTableSwitch.setUseOldTable(false);
        QueryTableSwitch.setUseOldTableForQuery(false);
        log.info("完全迁移状态设置完成: {}", getCurrentSwitchStatus());
    }

    /**
     * 回滚到初始状态
     * 双写开启，查询使用旧表
     */
    public static void rollbackToInitialState() {
        log.info("回滚到初始状态：双写开启，查询使用旧表");
        OldTableSwitch.setUseOldTable(true);
        QueryTableSwitch.setUseOldTableForQuery(true);
        log.info("初始状态设置完成: {}", getCurrentSwitchStatus());
    }

    /**
     * 获取迁移阶段描述
     * 
     * @return 迁移阶段描述
     */
    public static String getMigrationPhase() {
        boolean oldTableSwitch = OldTableSwitch.isUseOldTable();
        boolean queryTableSwitch = QueryTableSwitch.isUseOldTableForQuery();
        
        if (oldTableSwitch && queryTableSwitch) {
            return "阶段1: 初始状态 - 双写开启，查询旧表";
        } else if (oldTableSwitch && !queryTableSwitch) {
            return "阶段2: 目标状态 - 双写开启，查询新表";
        } else if (!oldTableSwitch && !queryTableSwitch) {
            return "阶段3: 完全迁移 - 双写关闭，查询新表";
        } else {
            return "阶段?: 异常状态 - 双写关闭，查询旧表（不推荐）";
        }
    }

    /**
     * 打印当前状态信息
     */
    public static void printCurrentStatus() {
        log.info("=== 表切换状态信息 ===");
        log.info(getCurrentSwitchStatus());
        log.info(getMigrationPhase());
        log.info("===================");
    }
}
