package com.cheche365.bc.utils;

import org.junit.Test;
import static org.junit.Assert.assertEquals;

/**
 * 测试PlatformUtil中getBwCompulsoryClaimTimes方法的Map实现
 */
public class PlatformUtilMapTest {

    @Test
    public void testGetBwCompulsoryClaimTimesWithValidRates() {
        // 测试所有有效的理赔率
        assertEquals("连续三年没有理赔", invokeGetBwCompulsoryClaimTimes(0.7));
        assertEquals("连续两年没有理赔", invokeGetBwCompulsoryClaimTimes(0.8));
        assertEquals("上年没有理赔", invokeGetBwCompulsoryClaimTimes(0.9));
        assertEquals("新保或上年发生一次有责任不涉及死亡理赔", invokeGetBwCompulsoryClaimTimes(1.0));
        assertEquals("上年有两次及以上理赔", invokeGetBwCompulsoryClaimTimes(1.1));
        assertEquals("上年有涉及死亡理赔", invokeGetBwCompulsoryClaimTimes(1.3));
    }

    @Test
    public void testGetBwCompulsoryClaimTimesWithInvalidRates() {
        // 测试无效的理赔率
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(0.5));
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(1.5));
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(2.0));
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(-1.0));
    }

    @Test
    public void testGetBwCompulsoryClaimTimesWithEdgeCases() {
        // 测试边界情况
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(0.0));
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(Double.NaN));
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(Double.POSITIVE_INFINITY));
        assertEquals("未匹配到理赔次数", invokeGetBwCompulsoryClaimTimes(Double.NEGATIVE_INFINITY));
    }

    /**
     * 通过反射调用私有方法getBwCompulsoryClaimTimes
     */
    private String invokeGetBwCompulsoryClaimTimes(double compulsoryClaimRate) {
        try {
            java.lang.reflect.Method method = PlatformUtil.class.getDeclaredMethod("getBwCompulsoryClaimTimes", double.class);
            method.setAccessible(true);
            return (String) method.invoke(null, compulsoryClaimRate);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke getBwCompulsoryClaimTimes method", e);
        }
    }
}