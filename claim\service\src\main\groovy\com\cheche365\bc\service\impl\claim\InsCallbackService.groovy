package com.cheche365.bc.service.impl.claim

import com.cheche365.bc.entity.mongo.CallBackLog
import com.cheche365.bc.enums.InsCompanyEnum
import com.cheche365.bc.service.dto.ClaimsCallbackReq
import com.cheche365.bc.service.impl.claim.base.FlowRunner
import com.cheche365.bc.util.ZooKeeperUtil
import groovy.transform.TupleConstructor
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service

import java.nio.file.Path

@Service
@TupleConstructor(defaults = false, includeFields = true)
class InsCallbackService implements FlowRunner {

    Map<String, Object> execute(ClaimsCallbackReq request) {
        def handlerCallbackTemplatePath = callbackTemplatePath(request)
        def ret = engine.run(
            handlerCallbackTemplatePath,
            new Binding(
                [
                    'callbackInfo': request.callbackInfo,
                    'companyId'   : request.companyId,
                    'claimsNo'    : request.claimsNo,
                    'config'      : [
                            'platformUrl': ZooKeeperUtil.getUrl('platform')
                    ]
                ]
            )
        )
        def callback = mongoTemplate.findOne(new Query(Criteria.where('claimsNo').is(request.claimsNo)), CallBackLog.class)
        saveCallbackInfo(callback ? callback?._id : null, request.claimsNo, request.callbackInfo, ret)
        ret as Map<String, Object>
    }

    static def callbackTemplatePath(ClaimsCallbackReq request) {
        if (request.companyId in []) {
            return Path.of(
                basePathTemplate(),
                    InsCompanyEnum.get(request.companyId) as String,
                "edi_${request.companyId}_${request.taskType}_callback.groovy"
            ).toString()
        }
        return Path.of(basePathTemplate(), "global_${request.taskType}_callback.groovy").toString()
    }

    /**
     * 回调存入到mongodb
     * @param step
     */
    void saveCallbackInfo(_id, claimsNo, callbackInfo, callbackResult) {
        def callBackLog = new CallBackLog()
        callBackLog.set_id(_id)
        callBackLog.setClaimsNo(claimsNo as String)
        callBackLog.setCallbackInfo(jsonGenerator.toJson(callbackInfo))
        callBackLog.setCallbackResult(jsonGenerator.toJson(callbackResult))
        mongoTemplate.save(callBackLog)
    }
}
