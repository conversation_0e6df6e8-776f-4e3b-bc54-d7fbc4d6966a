package com.cheche365.bc.sharding;

/**
 * ThreadLocal工具类，用于在线程内传递操作类型
 * 替代AutoTask实体中的opType字段
 */
public class ShardingContextHolder {

    /**
     * 存储操作类型的ThreadLocal变量
     */
    private static final ThreadLocal<String> OP_TYPE_HOLDER = new ThreadLocal<>();

    private static final ThreadLocal<Boolean> USE_OLD_TABLE = new ThreadLocal<>();

    /**
     * 设置操作类型
     *
     * @param opType 操作类型（INSERT、UPDATE等）
     */
    public static void setOpType(String opType) {
        OP_TYPE_HOLDER.set(opType);
    }

    /**
     * 获取操作类型
     *
     * @return 操作类型
     */
    public static String getOpType() {
        return OP_TYPE_HOLDER.get();
    }

    /**
     * 清除操作类型
     * 建议在操作完成后调用，避免内存泄漏
     */
    public static void clearOpType() {
        OP_TYPE_HOLDER.remove();
    }

    /**
     *
     */
    public static void setUseOldTable(Boolean useOldTable) {
        USE_OLD_TABLE.set(useOldTable);
    }

    public static Boolean getUseOldTable() {
        return USE_OLD_TABLE.get();
    }

    /**
     *
     */
    public static void clearUseOldTable() {
        USE_OLD_TABLE.remove();
    }
}
