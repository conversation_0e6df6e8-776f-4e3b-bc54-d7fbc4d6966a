package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.http.util.Args;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_template_new")
public class Template extends Model<Template> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("backTemplate")
    private String backTemplate;

    @TableField("charSet")
    private String charSet;

    @TableField("createTime")
    private LocalDateTime createTime;

    private String name;

    @TableField("proUrl")
    private String proUrl;

    private String remark;

    @TableField("requestChannel")
    private String requestChannel;

    @TableField("requestTemplate")
    private String requestTemplate;

    private Boolean state;

    @TableField("templateCategory")
    private String templateCategory;

    @TableField("templateEngine")
    private String templateEngine;

    @TableField("testUrl")
    private String testUrl;

    @TableField("uatUrl")
    private String uatUrl;

    @TableField("updateTime")
    private LocalDateTime updateTime;

    private Integer version;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public String getRealUrl(String env) {
        Args.notEmpty(env, "环境参数env");
        String url;
        switch (env) {
            case Interface.Env_Pro:
                url = this.proUrl;
                break;
            case Interface.Env_Uat:
                url = this.uatUrl;
                break;
            case Interface.Env_Test:
                url = this.testUrl;
                break;
            default:
                return "";
        }
        return url;
    }
}
