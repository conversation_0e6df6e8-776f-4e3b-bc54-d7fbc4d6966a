package com.cheche365.bc.admin.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.RolePermission;

import java.util.List;

/**
 * <p>
 * 角色权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-15
 */
public interface RolePermissionService extends IService<RolePermission> {

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId  角色id
     * @param isAdmin 是否为“超级管理员”角色
     * @return
     */
    List<RolePermission> listRolePermissionByRoleId(Long roleId, Boolean isAdmin);
}
