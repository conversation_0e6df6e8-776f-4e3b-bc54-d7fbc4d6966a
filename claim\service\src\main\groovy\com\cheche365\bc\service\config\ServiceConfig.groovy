package com.cheche365.bc.service.config

import groovy.json.JsonGenerator
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ServiceConfig {

    @Value('${router.script.dir}')
    private String scriptDir

    @Bean
    GroovyScriptEngine groovyScriptEngine() {
        return new GroovyScriptEngine(scriptDir)
    }

    @Bean
    JsonGenerator jsonGenerator() {
        return new JsonGenerator.Options().disableUnicodeEscaping().excludeFieldsByName('client', 'response').build()
    }
}
