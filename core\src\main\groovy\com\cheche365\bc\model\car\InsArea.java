package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;

/**
 * 投保区域
 * Created by Administrator on 2015-10-14.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "投保地区")
@Getter
@Setter
@XmlRootElement
public class InsArea {

    /**
     * 省份代码
     */
    @FieldDoc(des = "省份代码", need = true, remark = "")
    private String province;
    /**
     * 省份名称
     */
    @FieldDoc(des = "省份名称", need = true, remark = "")
    private String provinceName;
    /**
     * 城市代码
     */
    @FieldDoc(des = "城市代码", need = true, remark = "")
    private String city;
    /**
     * 城市名称
     */
    @FieldDoc(des = "城市名称", need = true, remark = "")
    private String cityName;

}
