server:
  port: 8080
  servlet:
    context-path: /
  undertow:
    io-threads: 16
    worker-threads: 256

spring:
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:sharding-jdbc.yml?placeholder-type=environment
#  datasource:
#    dynamic:
#      primary: bcs
#      druid:
#        initial-size: 2
#        max-active: 10
#        min-idle: 1
#        max-wait: 60000
#        test-on-borrow: false
#        test-on-return: false
#        test-while-idle: true
#        time-between-eviction-runs-millis: 30000
#        min-evictable-idle-time-millis: 60000
#        validation-query: SELECT 1
#        filters: wall,stat,slf4j,config
#        init-connection-sqls: set NAMES utf8mb4;
#      datasource:
#        bcs:
#          type: com.alibaba.druid.pool.DruidDataSource
#          driver-class-name: com.mysql.cj.jdbc.Driver
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mail:
    #配置服务器邮件账号信息
    #服务器 smtp.cheche365.com
    host: smtp.mxhichina.com
    # 登录账户
    username: <EMAIL>
    # 登录密码
    password: cheche365.com
    # 端口
    port: 465
    # 默认编码
    default-encoding: UTF-8
    # 其他的属性
    properties:
      "mail.smtp.timeout": 25000
      "mail.smtp.auth": true
      "mail.smtp.starttls.enable": true
      "mail.smtp.socketFactory.port": 465
      "mail.smtp.socketFactory.class": "javax.net.ssl.SSLSocketFactory"

# Actuator
management:
  endpoints:
    web:
      base-path: /app-manage
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  health:
    mongo:
      enabled: false
#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  typeEnumsPackage: com.cheche365.bc.**.entity.**.enums
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

security:
  login-url: /index.html

cookie-name: bc

#api-interface
api:
  sign-key: BC_SIGN

web:
  groovy-files: ../river_templates/src/main/groovy

#sftp服务配置
sftp:
  client:
    protocol: sftp
    host: ************
    port: 31367
    username: cheche_prod
    password: xgBnC2n6YOUv
    privateKey:                # 密钥文件路径
    passphrase:                # 密钥的密码
    sessionConnectTimeout: 15000    # session连接超时时间
    channelConnectedTimeout: 15000  #channel连接超时时间

logging:
  level:
    com.xxl.rpc.remoting.net.impl.netty_http.server: OFF

hbase:
  conf:
    zk-quorum: ${HBASE_ZK_QUORUM:***********:2181}
    zk-zone-parent: ${HBASE_ZK_ZONE_PARENT:/hbase}
    namespace: ${HBASE_NAMESPACE:}
    username: ${HBASE_USERNAME:}
    password: ${HBASE_PASSWORD:}
    retry-count: ${HBASE_RETRY_COUNT:5}
    retry-pause: ${HBASE_RETRY_PAUSE:1000}
    zk-session-timeout: ${HBASE_ZK_SESSION_TIMEOUT:10000}

clue:
  guoshou:
    aesKey: 1234567812345678
