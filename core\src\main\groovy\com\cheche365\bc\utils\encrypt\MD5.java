package com.cheche365.bc.utils.encrypt;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

public class MD5 {

    private static final Logger LOG = LoggerFactory.getLogger("MD5");

    public static String toBase64(String src) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest(src.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(digest);

        } catch (Exception e) {
            LOG.error("string to Base64MD5 error, source str:{}，error:{}", src, ExceptionUtils.getStackTrace(e));
        }
        return "";
    }

    public static String toHex(String src) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest(src.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeToString(digest);

        } catch (Exception e) {
            LOG.error("string to HexMD5 error, source str:{}，error:{}", src, ExceptionUtils.getStackTrace(e));
        }
        return "";
    }

}
