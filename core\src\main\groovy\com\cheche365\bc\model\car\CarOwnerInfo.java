package com.cheche365.bc.model.car; /**
 * 
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * 车主信息
 * <AUTHOR>
 * created at 2015年6月15日 下午1:36:18
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "车主信息")
@Getter
@Setter
@XmlRootElement
public class CarOwnerInfo extends PersonInfo implements Serializable {

	/**
	 * 序列化ID
	 */
	private static final long serialVersionUID = 2690683218668503606L;

}
