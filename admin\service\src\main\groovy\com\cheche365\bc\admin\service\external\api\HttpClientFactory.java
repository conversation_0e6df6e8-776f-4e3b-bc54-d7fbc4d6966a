package com.cheche365.bc.admin.service.external.api;

import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.config.ClientConfig;
import com.sun.jersey.api.json.JSONConfiguration;
import com.sun.jersey.client.apache4.ApacheHttpClient4Handler;
import com.sun.jersey.client.apache4.config.DefaultApacheHttpClient4Config;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

/**
 * connectionRequestTimeout 从连接池获取连接的超时时间
 * connectTimeout TCP连接超时时间
 * socketTimeout TCP读取超时时间, 此配置表示连续10s无数据到达则认为是read timeout
 *
 * <AUTHOR> shanxf
 * @desc
 * @Date 2021/5/11 11:34
 */
public class HttpClientFactory {

    private static final ClientConfig CLIENT_CONFIG;
    private static final HttpClientBuilder HTTP_CLIENT_BUILDER;

    static {
        CLIENT_CONFIG = new DefaultApacheHttpClient4Config();
        CLIENT_CONFIG.getFeatures().put(JSONConfiguration.FEATURE_POJO_MAPPING, Boolean.TRUE);

        SSLContext sslContext = null;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(new KeyManager[0], new TrustManager[]{new DefaultTrustManager()}, new SecureRandom());
        } catch (Exception e) {
            e.printStackTrace();
        }
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
            .register("http", PlainConnectionSocketFactory.INSTANCE)
            .register("https", new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER))
            .build();

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(100);

        HTTP_CLIENT_BUILDER = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .setDefaultRequestConfig(
                RequestConfig.custom()
                    .setConnectionRequestTimeout(15 * 1000)
                    .setConnectTimeout(15 * 1000)
                    .setSocketTimeout(30 * 1000)
                    .build())
            .setRetryHandler(createHttpRequestRetryHandler())
            .evictIdleConnections(15, TimeUnit.SECONDS)
            .evictExpiredConnections()
            .disableRedirectHandling();

    }

    public static Client createClient() {
        CloseableHttpClient closeableHttpClient = HTTP_CLIENT_BUILDER.build();
        return new Client(new ApacheHttpClient4Handler(closeableHttpClient, new BasicCookieStore(), true), CLIENT_CONFIG);
    }

    private static HttpRequestRetryHandler createHttpRequestRetryHandler() {
        return (exception, executionCount, context) -> executionCount <= 3 && exception instanceof NoHttpResponseException;
    }

    private static class DefaultTrustManager implements X509TrustManager {
        private DefaultTrustManager() {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }

        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {

        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {

        }
    }
}
