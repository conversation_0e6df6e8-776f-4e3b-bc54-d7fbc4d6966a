package com.cheche365.bc.model;


import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;

/**
 * 增值税发票类型
 * Created by austin on 16/5/5.
 */
@ClassDoc(remark = "发票类型")
public enum InvoiceType {

    /**
     * 普通发票
     */
    @FieldDoc(des="普通发票")
    Normal(0,"普通发票"),

    /**
     * 增值税专用发票
     */
    @FieldDoc(des="增值税专用发票")
    IncSpecial(1,"增值税专用发票"),
    /**
     *
     */
    @FieldDoc(des="增值税普通发票")
    IncNormal(2,"增值税普通发票"),
    /**
     *
     */
    @FieldDoc(des="增值税电子发票")
    IncElectronic(3,"增值税电子发票");

    InvoiceType(int code,String value)
    {
        this.code=code;
        this.value=value;
    }
    private int code;
    private String value;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
