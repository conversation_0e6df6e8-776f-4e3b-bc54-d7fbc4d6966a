package com.cheche365.bc.service.impl.process;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.TaskProcessService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.MapUtil;
import com.cheche365.bc.utils.ErrorInfoUtil;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 承保查询
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskProcessServiceImplByApprovedQuery implements TaskProcessService {

    private final InterfaceService interfaceService;

    private final AutoTaskService autoTaskService;

    @Override
    public boolean processTask(Interface itf, AutoTask autoTask) throws Exception {
        Interface ediInterface = interfaceService.getInterface(autoTask, autoTask.getTaskType() + "_callback");

        String intType = "edi";
        String comId = autoTask.getCompanyId();
        if (ObjUtil.isNotNull(ediInterface)) {
            intType = ediInterface.getIntType();
            comId = ediInterface.getComId();
        }
        List<com.cheche365.bc.entity.AutoTask> callbackTasks = autoTaskService.getTaskByTaskIdComTaskType(autoTask.getTraceKey(), autoTask.getCompanyId(), String.format("%s-%s-approved_callback", intType, comId));
        if (CollUtil.isEmpty(callbackTasks)) {
            autoTask.setTaskStatus(TaskStatus.APPROVED_QUERY_FAILED.getState());
            autoTask.setConcatResultStr("承保查询失败,因为还没收到保险公司回调!");
            autoTask.getErrorInfo().putAll(ErrorInfoUtil.build(6, "还没回调或回调处理失败了！"));
            return false;
        }

        autoTask.setEndFlag(true);
        autoTask.setEndTime(LocalDateTime.now());
        autoTask.setConcatResultStr(Strings.nullToEmpty(autoTask.getResultStr()) + "本地数据库查询到该单回调记录!");
        var callBackTask = callbackTasks.stream().filter(callback -> TaskStatus.APPROVED_QUERY_SUCCESS.getState().equals(callback.getTaskStatus()))
            .findFirst().orElse(null);
        if (ObjUtil.isNull(callBackTask)) {
            autoTask.setTaskStatus(TaskStatus.APPROVED_QUERY_FAILED.getState());
        } else {
            Map dataSource = JSONObject.parseObject(autoTask.getApplyJson(), Map.class);

            if (autoTask.getCompanyId().contains("4002")) {
                //查看此任务是否是 回写，即是否 需要合并两次承保回调查询结果
                JSONObject sqJson = JSONObject.parseObject(callBackTask.getFeedbackJson()).getJSONObject("enquiry").getJSONObject("sq");
                //合并
                for (com.cheche365.bc.entity.AutoTask it : callbackTasks) {
                    //it有，sqJson没有，则需要补充
                    JSONObject itSqJson = JSONObject.parseObject(it.getFeedbackJson()).getJSONObject("enquiry").getJSONObject("sq");
                    if (StringUtils.isNotBlank(itSqJson.getString("bizPolicyCode")) && StringUtils.isBlank(sqJson.getString("bizPolicyCode"))) {
                        sqJson.put("bizPolicyCode", itSqJson.getString("bizPolicyCode"));
                        callBackTask.setBizPolicyNo(itSqJson.getString("bizPolicyCode"));
                    }
                    if (StringUtils.isNotBlank(itSqJson.getString("efcPolicyCode")) && StringUtils.isBlank(sqJson.getString("efcPolicyCode"))) {
                        sqJson.put("efcPolicyCode", itSqJson.getString("efcPolicyCode"));
                        callBackTask.setEfcPolicyNo(itSqJson.getString("efcPolicyCode"));
                    }
                    //
                    JSONObject miscJson = sqJson.getJSONObject("misc");
                    JSONObject itMiscJson = itSqJson.getJSONObject("misc");
                    if (StringUtils.isNotBlank(itMiscJson.getString("policyDownloadNo")) && StringUtils.isBlank(miscJson.getString("policyDownloadNo"))) {
                        miscJson.put("policyDownloadNo", itMiscJson.getString("policyDownloadNo"));
                    }
                    if (StringUtils.isNotBlank(itMiscJson.getString("orderNo")) && StringUtils.isBlank(miscJson.getString("orderNo"))) {
                        miscJson.put("orderNo", itMiscJson.getString("orderNo"));
                    }
                    sqJson.put("misc", miscJson);
                    JSONObject nonMotorJson = sqJson.getJSONObject("nonMotor");
                    JSONObject itNonMotorJson = itSqJson.getJSONObject("nonMotor");
                    if (StringUtils.isNotBlank(itNonMotorJson.getString("accidentPolicyCode")) && StringUtils.isBlank(nonMotorJson.getString("accidentPolicyCode"))) {
                        nonMotorJson.put("accidentPolicyCode", itNonMotorJson.getString("accidentPolicyCode"));
                        sqJson.put("nonMotor", nonMotorJson);
                    }
                }
                //通过sq判断是否全部回写
                JSONObject feedbackJson = JSONObject.parseObject(callBackTask.getFeedbackJson());
                JSONObject enquiryJson = feedbackJson.getJSONObject("enquiry");
                enquiryJson.put("sq", sqJson);
                feedbackJson.put("enquiry", enquiryJson);
                callBackTask.setFeedbackJson(JSONObject.toJSONString(feedbackJson));
            }
            //商业交强保单号
            if (StringUtils.isNotBlank(callBackTask.getBizPolicyNo())) {
                autoTask.setBizPolicyNo(callBackTask.getBizPolicyNo());
                MapUtil.putMap(dataSource, "sq/bizPolicyCode", autoTask.getBizPolicyNo());
            }
            if (StringUtils.isNotBlank(callBackTask.getEfcPolicyNo())) {
                autoTask.setEfcPolicyNo(callBackTask.getEfcPolicyNo());
                MapUtil.putMap(dataSource, "sq/efcPolicyCode", autoTask.getEfcPolicyNo());
            }
            //非车及misc节点
            JSONObject sqObject = JSONObject.parseObject(callBackTask.getFeedbackJson()).getJSONObject("enquiry").getJSONObject("sq");
            if (Objects.nonNull(sqObject)) {
                //任务原始sq
                Map sqDataSource = (Map) dataSource.get("sq");
                //非车险
                if (Objects.nonNull(sqObject.getJSONObject("nonMotor")) && !sqObject.getJSONObject("nonMotor").isEmpty()) {
                    Map nonMotor = sqObject.getJSONObject("nonMotor");
                    Map nonMotorDataSource = (Map) sqDataSource.get("nonMotor");
                    if (cn.hutool.core.map.MapUtil.isNotEmpty(nonMotorDataSource)) {
                        nonMotorDataSource.putAll(nonMotor);
                    } else {
                        MapUtil.putMap(dataSource, "sq/nonMotor", nonMotor);
                    }
                }

                //misc节点回写中放入各种字段 供取值
                if (Objects.nonNull(sqObject.getJSONObject("misc")) && !sqObject.getJSONObject("misc").isEmpty()) {
                    Map misc = sqObject.getJSONObject("misc");
                    Map dataSourceMisc = (Map) sqDataSource.get("misc");
                    if (Objects.nonNull(dataSourceMisc) && !dataSourceMisc.isEmpty())
                        misc.putAll(dataSourceMisc);
                    MapUtil.putMap(dataSource, "sq/misc", misc);
                    //强制更新 磐石（其余上游待观察）的回写结果
                    MapUtil.putMap(dataSource, "sq/forceUpdateMisc", true);
                }
            }

            autoTask.setTaskStatus(TaskStatus.APPROVED_QUERY_SUCCESS.getState());

            if (autoTask.getCompanyId().contains("4002")) {
                //查询最新一条的核保记录 可能使用任意一个类型核保
                String[] typeList = {"edi-4002-insure", "edi-4002-autoinsure"};
                Map sqDataSource = (Map) dataSource.get("sq");
                com.cheche365.bc.entity.AutoTask task = autoTaskService.getOneTask(null, "4002", typeList, sqDataSource.get("bizProposeNum"), sqDataSource.get("efcProposeNum"));
                //获取核保的applyJson，判断是否属于非车单，应有的任意一个保单号没回写都返回承保查询失败
                boolean flag = true;
                JSONObject applyJsonObject = JSONObject.parseObject(task.getApplyJson());
                if (null != applyJsonObject.getJSONObject("baseSuiteInfo").getJSONObject("bizSuiteInfo") && null == sqDataSource.get("bizPolicyCode")) {
                    flag = false;
                }
                if (null != applyJsonObject.getJSONObject("baseSuiteInfo").getJSONObject("efcSuiteInfo") && null == sqDataSource.get("efcPolicyCode")) {
                    flag = false;
                }
                if (null != applyJsonObject.getJSONObject("sq").getJSONObject("nonMotor")) {
                    if (null == sqDataSource.get("nonMotor")) {
                        flag = false;
                    }
                    if (null != sqDataSource.get("nonMotor") && null == ((Map) sqDataSource.get("nonMotor")).get("accidentPolicyCode")) {
                        flag = false;
                    }
                }
                if (!flag) {
                    autoTask.setTaskStatus(TaskStatus.APPROVED_QUERY_FAILED.getState());
                    autoTask.getErrorInfo().putAll(ErrorInfoUtil.build(6, "4002-保司信息回掉写不完整"));
                }
            }

            autoTask.setFeedbackJson(JSON.toJSONString(dataSource));
        }
        return true;
    }

    @Override
    public boolean isProcessTask(AutoTask autoTask) {
        String taskType = autoTask.getTaskType();
        Boolean callbackDataBaseQuery = MapUtils.getBoolean(autoTask.getConfigs(), "callbackDataBaseQuery", true);
        var ediInterface = interfaceService.getInterface(autoTask, autoTask.getTaskType() + "_callback");
        return taskType.startsWith("edi")
            && taskType.contains("-" + TaskType.APPROVED_QUERY.code)
            && (Objects.nonNull(ediInterface) && callbackDataBaseQuery || isPiccOnline(autoTask));
    }

    private boolean isPiccOnline(AutoTask autoTask) {
        boolean isOnline = MapUtils.getBoolean(autoTask.getConfigs(), "isOnline", false);
        boolean isPicc = autoTask.getCompanyId().startsWith("2005");
        return isOnline && isPicc;
    }
}
