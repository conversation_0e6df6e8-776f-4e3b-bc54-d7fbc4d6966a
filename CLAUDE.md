# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with the river_templates repository.

## Project Overview

The `river_templates` project is a Groovy-based template library for insurance company integrations. It provides standardized templates for automated interactions with 30+ insurance companies, supporting both EDI (Electronic Data Interchange) and robot automation approaches. This project serves as the template engine for the `ins-auto-service` system.

### Key Features
- **Multi-Company Support**: Templates for 30+ insurance companies
- **Dual Integration Types**: EDI (API-based) and Robot (browser automation)
- **Hot Reload**: Real-time template updates via Jenkins CI/CD
- **Standardized Interface**: Consistent template structure and execution context
- **Comprehensive Testing**: Unit tests for each company integration

## Quick Start Commands

### Build & Run
```bash
# Build entire project
./gradlew clean build

# Run specific services
./gradlew :admin:admin-rest:bootRunDev    # Admin service (port 8080)
./gradlew :claim:claim-rest:bootRunDev    # Claims service
./gradlew :river:river-rest:bootRunDev    # Task processing service

# Development shortcuts
./gradlew bootRunAdmin                    # Admin service with dev profile
./gradlew :admin:rest:bootRunDev          # Individual module run
```

### Testing
```bash
# Run all tests
./gradlew test

# Run specific module tests
./gradlew :core:test
./gradlew :entity:test
./gradlew :business:test
```

### Build & Package
```bash
# Build JARs for all modules
./gradlew bootJar

# Build Docker images
./gradlew :admin:rest:bootBuildImage
./gradlew :claim:rest:bootBuildImage
./gradlew :river:rest:bootBuildImage
```

## Architecture Overview

### Module Structure
```
core/           # Foundation utilities, configs, insurance handlers
entity/         # Data models (MySQL, HBase, MongoDB)
business/       # Core business logic
river/          # Task processing & workflow orchestration
├── river-rest     # REST APIs
├── river-akka     # Actor-based processing
└── river-service  # Business logic
admin/          # Administrative interface
├── admin-rest     # REST APIs (8080)
├── admin-service  # Business logic
└── admin-schedule # Scheduled tasks
claim/          # Insurance claims processing
├── claim-rest     # REST APIs
└── claim-service  # Business logic
```

### Technology Stack
- **Backend**: Java 17 + Groovy 3.0.9 + Spring Boot 3.2.8
- **Data**: MySQL + ShardingSphere JDBC, HBase, MongoDB, Redis
- **Processing**: Akka 2.6.6 (actors), Kafka (event streaming)
- **Integration**: 20+ insurance company APIs (SOAP/REST)
- **Security**: Spring Security, rate limiting, API key management

### Main Application Entry Points
- **Admin**: `admin/rest/src/main/groovy/com/cheche365/bc/admin/rest/AdminApplication.java`
- **Claims**: `claim/rest/src/main/groovy/com/cheche365/bc/rest/ClaimApplication.groovy`
- **River**: `river/rest/src/main/groovy/com/cheche365/bc/river/rest/RiverApplication.java`

## Development Environment

### Configuration Files
- **Application configs**: `src/main/resources/application-{dev|qa|production}.yml`
- **Kafka configs**: `src/main/resources/kafka-{dev|qa|production}.properties`
- **Database sharding**: `src/main/resources/sharding-jdbc.yml`

### Environment Setup
1. **Database**: MySQL 8.0+, HBase 2.6+, MongoDB, Redis
2. **Kafka**: Required for task synchronization
3. **Zookeeper**: For distributed coordination
4. **Ports**: 
   - Admin: 8080
   - Claims: (configurable)
   - River: (configurable)

### Common Development Tasks

#### Adding New Insurance Company Integration
1. Create handler in `core/src/main/groovy/com/cheche365/bc/handler/edi/`
2. Add company enum in `core/src/main/groovy/com/cheche365/bc/enums/InsCompanyEnum.java`
3. Add configuration in appropriate config files
4. Implement company-specific logic in business module

#### Database Changes
1. **MySQL**: Use MyBatis mappers in `entity/src/main/groovy/com/cheche365/bc/mapper/`
2. **HBase**: Use repositories in `entity/src/main/groovy/com/cheche365/bc/hbase/`
3. **MongoDB**: Use repositories in `entity/src/main/groovy/com/cheche365/bc/repo/`

#### Adding New REST Endpoints
1. Add controller in appropriate module's `rest` subproject
2. Add DTOs in corresponding `service` module
3. Add business logic in `service` module
4. Ensure proper security configuration

## Module Dependencies
```
core → entity → business → river/admin/claim
```

## Key Business Domains
- **Insurance Quotation**: Multi-company price comparison and processing
- **Claims Processing**: End-to-end claims lifecycle management
- **Task Orchestration**: Complex workflow management with Akka actors
- **Data Integration**: EDI with 20+ insurance companies
- **Robot Processing**: Automated web scraping and form filling

## River Templates Integration

### Template System Architecture
The `river_templates` project provides Groovy-based templates for insurance company integrations. It works as a separate repository that gets deployed as a file directory to enable real-time code updates without service restarts.

#### Template Organization
```
river_templates/src/main/groovy/
├── common/                    # Shared utilities and base scripts
│   ├── scripts/              # Base script classes (BaseScript_Http_Enq, etc.)
│   ├── enums/               # Common enumerations
│   └── CookieUtil.groovy    # Utility classes
├── [company_name]/          # Per-insurance company directories
│   ├── edi/                 # EDI (Electronic Data Interchange) templates
│   │   ├── edi-[code]-[function]_req.groovy  # Request templates
│   │   ├── edi-[code]-[function]_rep.groovy  # Response templates
│   │   └── edi_[code]_dict.groovy            # Data mapping dictionaries
│   └── robot/               # Robot process automation templates
│       ├── robot_[code]_[function].groovy
│       └── robot_[code]_util.groovy
```

#### Template Types and Naming Conventions
1. **EDI Templates**: Electronic data interchange for real-time API calls
   - `xxx_req.groovy`: Request parameter processing
   - `xxx_rep.groovy`: Response data processing and mapping
   - `xxx_dict.groovy`: Data mapping and transformation utilities

2. **Robot Templates**: Browser automation for legacy systems
   - `robot_xxx.groovy`: Main automation logic
   - `robot_xxx_util.groovy`: Utility functions

#### Template Execution Context
Templates have access to the following variables:
```groovy
// Core context variables available in all templates
enquiry         // Main business entity (CarInfo, PersonInfo, etc.)
config          // Environment and company-specific configurations
params          // Request parameters
tempValues      // Temporary values shared between templates
reqHeaders      // HTTP request headers
taskType        // Current task type (quotation, policy, etc.)
script          // Script execution context
```

### Template Engine Integration

#### GroovyScriptEngine Usage
```java
// Template execution in TaskUtil.process()
Object result = scriptEngine.run(templateName, new Binding(inActionData));

// Template loading and caching
String templatePath = String.format("%s/%s/%s.groovy",
    itf.getComCode(), itf.getIntType(), templateName);
```

#### Template Lifecycle
1. **Loading**: Templates loaded via `TemplateService.loadTemplate()`
2. **Execution**: Sequential execution in `TaskUtil.process()`
3. **Caching**: GroovyScriptEngine provides automatic caching
4. **Hot Reload**: Jenkins git hooks enable real-time updates

### Akka Actor Integration

#### Actor Hierarchy for Template Execution
```
TaskActor
├── InsActor (Insurance Company)
│   └── AccountActor (Account Management)
│       └── WinActor (Window/Session Management)
│           └── Template Execution
```

#### Actor Configuration
- **InsActor**: One per insurance company
- **AccountActor**: Multiple accounts per company (unlimited)
- **WinActor**: Multiple windows per account (default: 10, configurable)
- **Total Actors**: Maximum 1,000,000 (hardcoded limit)

### Template Development Workflow

#### Request Processing Flow
1. **AutoController.auto()** receives request → creates AutoTask
2. **TaskActor** processes via three branches:
   - `RequestTaskAndTransform`: Parameter retrieval and transformation
   - `ProcessTask/keepSession`: Business logic execution
   - `CallbackAndTransform`: Result callback processing

3. **Template Execution Sequence**:
   ```java
   // Load interface configuration
   Interface itf = getInterface(task, task.getTaskType());

   // Load configuration parameters
   loadConfig(itf, task);

   // Load templates for execution
   loadTemplate(itf, task);

   // Execute templates sequentially
   TaskUtil.process(task, templates);
   ```

#### Template Error Handling
Templates can control execution flow using exceptions:
- `TempSkipException`: Skip current template
- `InsReturnException.AllowRepeat`: Retry template with backtrack

### Data Flow and Storage

#### MongoDB Integration
Each template execution saves:
- Request/response bodies
- Execution logs and timing
- Error information and stack traces
- Template execution context

```java
// Save template execution data
TaskUtil.saveMongoObject(autoTask, actionLog);
```

#### Template Data Mapping
Templates handle data transformation between:
- Internal business entities → Insurance company formats
- Insurance company responses → Standardized internal format
- Error codes → User-friendly messages

## Development Guidelines

### Template Development Best Practices

#### Template Structure Standards
1. **Inheritance**: Extend appropriate base classes
   ```groovy
   // For HTTP-based templates
   @BaseScript BaseScript_Http_Enq baseScript

   // For code-only templates
   @BaseScript BaseScript_Code_Enq baseScript
   ```

2. **Error Handling**: Use standardized exception handling
   ```groovy
   // Skip template execution
   throw new TempSkipException(1, 'Reason for skipping')

   // Allow template retry with backtrack
   throw new InsReturnException.AllowRepeat(stepCount, 'Retry reason')
   ```

3. **Data Output**: Use consistent output methods
   ```groovy
   // Output data to tempValues
   output('templateKey', responseData)

   // Get template values
   Map data = getTemplateValues('templateKey')
   ```

#### Template Naming and Organization
- **Company Codes**: Use standardized company abbreviations
- **Function Codes**: Clear, descriptive function identifiers
- **Version Control**: Include version numbers for major changes
- **Documentation**: Each template should have header comments

#### Data Type Considerations
- **Enquiry Type**: Recommended for new templates (full object access)
- **Map Type**: Legacy support for existing integrations
- **Parameter Handling**: Be careful with numeric types (production vs local differences)

### Code Quality Standards

#### Testing Requirements
```bash
# Test specific company templates
./gradlew test --tests com.cheche365.bc.instest.[company].edi.*

# Run single test class
./gradlew test --tests com.cheche365.bc.instest.[company].[TestClass]
```

#### Memory Management
- **Template Compilation**: Remove unused company templates to prevent OutOfMemoryError
- **Actor Limits**: Monitor actor count (max 1,000,000)
- **Cache Management**: Be aware of GroovyScriptEngine caching behavior

### Integration Patterns

#### HTTP Request Patterns
```groovy
// Standard HTTP request in templates
responseBody = HttpSender.doPostWithRetryAndLoadHeader(
    5,                          // retry count
    closeableHttpClient,        // HTTP client
    true,                      // load headers
    realUrl,                   // target URL
    requestBody,               // request payload
    autoTask.getArrayParams(), // array parameters
    autoTask.getReqHeaders(),  // request headers
    temp.getCharSet(),         // character set
    buildReqConfig(autoTask),  // request config
    autoTask.keepSessionKey(), // session key
    autoTask.getRepHeaders()   // response headers
);
```

#### Session Management
- **Keep Session**: For robot/browser automation
- **Stateless**: For EDI/API integrations
- **Session Keys**: Proper session key management for multi-step processes

## Deployment and Operations

### CI/CD Pipeline

#### Jenkins Integration
- **Git Hooks**: Automatic deployment on template commits
- **Hot Reload**: Real-time template updates without service restart
- **Deployment Path**: Templates deployed to configurable directory
- **Environment Variables**: `RIVER_TEMPLATES_PATH` for template location

#### Docker Deployment
```dockerfile
# River service Docker configuration
FROM registry.cn-beijing.aliyuncs.com/cheche365/openjdk:17.0-buster-tools
WORKDIR /bc
COPY river/rest/build/libs/*river-rest-*.jar /bc
COPY libs/transmittable-thread-local-2.14.5.jar /bc

# JVM optimization for production
ENV JAVA_OPTS="-Xms1g -Xmx4g -XX:+UseG1GC -XX:G1HeapRegionSize=4m"
```

### Environment Configuration

#### Development Environment
```yaml
# application-dev.yml
web:
  groovy-files: ${RIVER_TEMPLATES_PATH:D:\workspace2024\river_templates\src\main\groovy}

spring:
  data:
    mongodb:
      database: river_zzb
      host: *************
      port: 27017
    redis:
      host: *************
      port: 6379
```

#### Production Environment
```yaml
# k8s-publish.yml
web:
  groovy-files: ${web_groovy_files}

spring:
  data:
    mongodb:
      uri: ${mongo_uri}
    redis:
      host: ${redis_host}
      port: ${redis_port}
```

### Monitoring and Logging

#### Log Configuration
- **Structured Logging**: JSON format with trace IDs
- **Log Levels**: INFO for production, DEBUG for development
- **Log Rotation**: Size and time-based rotation (100MB, 50 days)
- **Distributed Tracing**: SkyWalking integration

#### Performance Monitoring
- **GC Logging**: G1GC with detailed logging
- **Memory Dumps**: Automatic heap dumps on OOM
- **Metrics**: Custom metrics for template execution times
- **Health Checks**: Service health endpoints

## Troubleshooting Guide

### Common Template Issues

#### 1. Template Updates Not Taking Effect
**Problem**: Template changes don't reflect in production
**Cause**: GroovyScriptEngine caching with complex template dependencies
**Solution**:
- When template dependency chain A → B → C exists, update both B and C templates
- For immediate effect, restart the service or clear script cache
- Ensure Jenkins git hooks are properly configured

#### 2. Local vs Production Behavior Differences
**Problem**: Templates work locally but fail in production
**Common Causes**:
- **Data Type Differences**: Production converts integers to decimals (2 → 2.0)
- **Environment Configurations**: Different proxy settings or database connections
- **Template Path Differences**: Local uses test classes, production uses deployed templates

**Solutions**:
- Use String types for numeric values when possible
- Verify environment-specific configurations
- Test with production-like data formats

#### 3. AutoTask Class Not Found Errors
**Problem**: `AutoTask` class not found in template execution
**Cause**: Incorrect `dataType` configuration (Map vs Enquiry)
**Solution**:
- **Robot/Browser tasks**: Use `Enquiry` type (recommended)
- **EDI/API tasks**: Can use either `Map` or `Enquiry` type
- Prefer `Enquiry` type for new implementations

#### 4. Proxy Configuration Issues (407 Errors)
**Problem**: Local testing returns 407 Proxy Authentication Required
**Cause**: Mismatch between configuration proxy settings and hardcoded test proxy
**Solution**:
- Verify proxy configuration in test classes
- Ensure proxy settings match between config and test environment
- Check network connectivity and proxy credentials

#### 5. OutOfMemoryError During Development
**Problem**: IDE runs out of memory when compiling templates
**Cause**: IDE compiling all company templates simultaneously
**Solution**:
- Remove unused company template directories from project
- Increase IDE memory allocation
- Use selective compilation for specific companies

### Performance Optimization

#### Template Execution Optimization
- **Minimize HTTP Calls**: Batch requests when possible
- **Efficient Data Mapping**: Use direct field mapping instead of complex transformations
- **Session Reuse**: Implement proper session management for multi-step processes
- **Error Handling**: Fail fast for unrecoverable errors

#### Actor System Optimization
- **Actor Pool Management**: Monitor actor count and lifecycle
- **Session Management**: Proper cleanup of inactive sessions
- **Resource Limits**: Configure appropriate timeouts and retry limits

### Debugging Techniques

#### Template Debugging
```groovy
// Add debug logging in templates
log.info("Template execution data: {}", tempValues)
log.debug("Request parameters: {}", params)

// Output intermediate results
output('debug_data', [step: 'validation', data: processedData])
```

#### MongoDB Query for Template Execution
```javascript
// Find template execution logs
db.auto_task_log.find({
    "traceKey": "your-trace-key",
    "templateName": "template-name"
}).sort({"createTime": -1})
```

#### Actor System Debugging
- Monitor actor hierarchy and message flow
- Check actor mailbox sizes and processing times
- Verify proper actor lifecycle management

### Error Message Translation
The system provides error message translation for operational teams:
- **Configuration**: `tb_error_category` table
- **Implementation**: `TaskActor.CallbackAndTransform` class
- **Customization**: Modify callback format in `CallbackAndTransform`

### Security Considerations

#### Template Security
- **Input Validation**: Validate all external inputs in templates
- **SQL Injection**: Use parameterized queries in database operations
- **XSS Prevention**: Sanitize data when generating web content
- **Credential Management**: Never hardcode credentials in templates

#### API Security
- **Rate Limiting**: Implement appropriate rate limits for insurance company APIs
- **Authentication**: Secure API key and token management
- **Encryption**: Use HTTPS for all external communications
- **Audit Logging**: Log all security-relevant events

## Configuration Management
- **Environment-specific**: dev/qa/production profiles
- **External config**: Environment variables and external properties
- **Feature flags**: Dynamic configuration updates
- **Database sharding**: Monthly partitioning for auto_task table
- **Template Path**: Configurable via `web.groovy-files` property
- **Nexus Repository**: Internal Maven repository at `*************:8081`

## Quick Reference

### Key File Locations
- **Templates**: `${RIVER_TEMPLATES_PATH}/src/main/groovy/[company]/[type]/`
- **Configurations**: `src/main/resources/application-{env}.yml`
- **Docker Files**: `river/rest/Dockerfile`, `admin/rest/Dockerfile`
- **Build Scripts**: `build.gradle`, `gradle/publish.gradle`

### Important Classes and Methods
- **Template Loading**: `TemplateServiceImpl.loadTemplate()`
- **Template Execution**: `TaskUtil.process()`
- **Script Engine**: `ScriptUtil.engine()`, `ScriptUtil.engineWithTemplateName()`
- **HTTP Utilities**: `HttpSender.doPostWithRetryAndLoadHeader()`
- **Base Scripts**: `BaseScript_Http_Enq`, `BaseScript_Code_Enq`

### Environment Variables
- `RIVER_TEMPLATES_PATH`: Template directory location
- `mongo_uri`: MongoDB connection string
- `redis_host`, `redis_port`: Redis configuration
- `mysql_username`, `mysql_password`: Database credentials
- `web_groovy_files`: Production template path
