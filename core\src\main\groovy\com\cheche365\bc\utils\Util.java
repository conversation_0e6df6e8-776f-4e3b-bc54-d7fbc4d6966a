package com.cheche365.bc.utils;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * 通用工具类
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class Util {

    /**
     * 计算两个日期相差天数
     *
     * @param start 起始日期
     * @param end   终止日期
     * @return
     */
    public static long diffDays(Date start, Date end) {
        return (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000);
    }

    /**
     * 计算两个日期相差月份
     *
     * @param start 起始日期
     * @param end   终止日期
     * @return
     */
    public static long diffMonths(Date start, Date end) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        int sYear = calendar.get(Calendar.YEAR);
        int sMonth = calendar.get(Calendar.MONTH);
        calendar.setTime(end);
        int eYear = calendar.get(Calendar.YEAR);
        int eMonth = calendar.get(Calendar.MONTH);
        return (long) ((eYear - sYear) * 12 + (eMonth - sMonth));
    }

    /***
    * @Description: 需求 11810
    * @Date: 2025/3/12
    * @Author: wangj
    * @Param: [carModelName, plateNum]
    * @Return: boolean
    */
    public static boolean isNewEnergy(String carModelName, String plateNum) {
        if (StringUtils.isBlank(carModelName) || StringUtils.isBlank(plateNum)) {
            return false;
        }
        if (carModelName.contains("纯电") || carModelName.contains("插电")) {
            return true;
        }
        if (plateNum.length() == 8) {
            String plateSecond = plateNum.substring(2, 3);
            String[] codes = {"A", "B", "C", "D", "E", "F", "G", "H", "J", "K"};
            return Arrays.asList(codes).contains(plateSecond);
        }
        return false;
    }

    /***
     * @Description: 检查身份证合法
     * @Date: 2025/4/15
     * @Author: wangj
     * @Param: [idCard]
     * @Return: boolean
     */
    public static boolean checkIdCard(String idCard) {
        if(StringUtils.isBlank(idCard)) {
            return false;
        }
        Pattern ID_PATTERN = Pattern.compile("^\\d{6}(19|20)\\d{9}[\\dXx]$");
        return ID_PATTERN.matcher(idCard).matches();
    }
}

