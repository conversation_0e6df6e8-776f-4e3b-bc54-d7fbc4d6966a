package com.cheche365.bc.rest.filter

import com.cheche365.bc.rest.exception.RestException
import jakarta.servlet.Filter
import jakarta.servlet.FilterChain
import jakarta.servlet.ServletException
import jakarta.servlet.ServletRequest
import jakarta.servlet.ServletResponse
import jakarta.servlet.annotation.WebFilter
import jakarta.servlet.http.HttpServletRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerExceptionResolver

import java.util.concurrent.atomic.AtomicInteger

@WebFilter
@Component
class SecurityFilter implements Filter {

    private static int concurrentCount = 88

    private AtomicInteger inc = new AtomicInteger()

    @Autowired
    @Qualifier('handlerExceptionResolver')
    private HandlerExceptionResolver resolver

    @Override
    void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        def req = (HttpServletRequest) request
        if (invalidHealthToken(request, req)) {
            new RestException(RestException.TokenValidateFailed, 'token校验失败！')
        }
        int currentCount = inc.getAndIncrement()
        if (currentCount > concurrentCount) {
            new RestException(RestException.RateLimit, '服务器线上最多同时处理88个任务，请稍后重试！')
        }
        try {
            chain.doFilter(req, response)
        } finally {
            if (currentCount > 0) {
                inc.getAndDecrement()
            }
        }
    }

    def static invalidHealthToken(ServletRequest request, HttpServletRequest req) {
        if (req.getRequestURI().contains('app-manage') && req.getRequestURI() != '/app-manage/health') {
            return request.getParameter('token') != '6B097102-D5A7-0A2E-1298-271848AFA8B5'
        }
    }
}
