package com.cheche365.bc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 百川异常配置列表响应实体
 *
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-12-16
 */
@Data
public class AutoTaskExceptionConfigPageResDto {

    private Integer id;

    private String companyId;

    private String processType;

    private String taskType;

    private String exceptionKeywords;

    private String exceptionConversion;

    private Integer operatorId;

    private String operatorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
