package com.cheche365.bc.tools;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class DateCalcUtil {


    public static String getFormatDate(Date date, String format) {

      return Objects.nonNull(date) ? LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern(format)) : null;
    }

    /**
     *
     * @param start
     * @param end
     * @return
     */
    public static int getMonths(Date start, Date end) {

        LocalDateTime startTime = LocalDateTime.ofInstant(start.toInstant(), ZoneId.systemDefault());
        LocalDateTime endTime = LocalDateTime.ofInstant(end.toInstant(), ZoneId.systemDefault());
        return (int) ChronoUnit.MONTHS.between(startTime, endTime);
    }

    /**
     * 根据传过来的数据计算相差天数
     * @param start
     * @param end
     * @return
     */

    public static int getTimeDifference(String start, String end) {
        return (int) ChronoUnit.DAYS.between(LocalDate.parse(start), LocalDate.parse(end));
    }

}
