package com.cheche365.bc.actor.msg;

/**
 * 重试消息的包装，每次失败再次重试时间=重试次数*重试次数因子（秒）+失败休眠时间（秒）
 * <AUTHOR>
 * @Created by austinChen on 2015/12/12 15:35.
 */
public class RetryMsg {
    /**
     * 消息主题
     */
    private Object body;
    /**
     * 重试次数
     */
    private int retryTimes;
    /**
     * 重试次数因子
     */
    private int retryFactorSecond;

    /**
     * 每次失败休眠时间
     */
    private int failSleepSecond;


    /**
     * 最大失败次数后停止
     */
    private int maxRetryTimes;

    public int getFailSleepSecond() {
        return failSleepSecond;
    }

    public void setFailSleepSecond(int failSleepSecond) {
        this.failSleepSecond = failSleepSecond;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }

    public int getMaxRetryTimes() {
        return maxRetryTimes;
    }

    public void setMaxRetryTimes(int maxRetryTimes) {
        this.maxRetryTimes = maxRetryTimes;
    }

    public int getRetryFactorSecond() {
        return retryFactorSecond;
    }

    public void setRetryFactorSecond(int retryFactorSecond) {
        this.retryFactorSecond = retryFactorSecond;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(int retryTimes) {
        this.retryTimes = retryTimes;
    }


    /**
     * @param body              消息内容
     * @param retryTimes        当前重试次数
     * @param failSleepSecond   重试次数休眠间隔
     * @param retryFactorSecond 重试次数因子(秒)
     * @param maxRetryTimes     最大重试次数
     */
    public RetryMsg(Object body, int retryTimes, int failSleepSecond, int retryFactorSecond, int maxRetryTimes) {
        //todo 所有值必须非空大于0
        this.body = body;
        this.retryTimes = retryTimes;
        this.failSleepSecond = failSleepSecond;
        this.retryFactorSecond = retryFactorSecond;
        this.maxRetryTimes = maxRetryTimes;
    }

    /**
     * 失败休眠时间一致。不进行递增
     *
     * @param body            消息内容
     * @param failSleepSecond 重试次数休眠间隔
     * @param maxRetryTimes   最大重试次数
     */
    public RetryMsg(Object body, int failSleepSecond, int maxRetryTimes) {
        this(body, 0, failSleepSecond, 0, maxRetryTimes);
    }

    /**
     * @param body              消息内容
     * @param failSleepSecond   重试次数休眠间隔
     * @param retryFactorSecond 重试次数因子(秒)
     * @param maxRetryTimes     最大重试次数
     */
    public RetryMsg(Object body, int failSleepSecond, int retryFactorSecond, int maxRetryTimes) {
        this(body, 0, failSleepSecond, retryFactorSecond, maxRetryTimes);
    }

    /**
     * @return 判断是否达到最大重试次数 是返回true，否则false
     */
    public boolean isMaxRetry() {
        return this.maxRetryTimes < retryTimes;
    }

    /**
     * @return 增加失败次数
     */
    public RetryMsg increment() {
        return new RetryMsg(body, ++this.retryTimes, failSleepSecond, retryFactorSecond, maxRetryTimes);
    }

    /**
     * 每次失败再次重试时间=重试次数*重试次数因子（秒）+失败休眠时间（秒）
     *
     * @return 计算失败后休眠的时间
     */
    public int getAfterSecond() {
        return retryTimes * retryFactorSecond + failSleepSecond;
    }


}
