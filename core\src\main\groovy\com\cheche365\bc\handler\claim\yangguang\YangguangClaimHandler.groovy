package com.cheche365.bc.handler.claim.yangguang


import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.handler.claim.BaseClaimHandler
import groovy.json.JsonOutput
import groovy.json.StringEscapeUtils
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Component

import static com.cheche365.bc.utils.HttpClientUtils.TEXT_RESPONSE_PARSE

/**
 * 阳光理赔加解密处理
 */
@Component
@Slf4j
class YangguangClaimHandler extends BaseClaimHandler {


    static final List<String> NEED_ENCRYPT_TEMPLATES = ['edi_2019_reportCase']

    static final List<String> NEED_ENCRYPT_AND_SIGN_TEMPLATES = ['edi_2019_uploadImage']

    static final Closure YG_DECODE_RESPONSE_PARSE = {
        key, text ->
            {
                YangguangEncryptionUtil.authcodeDecode(text as String, key as String)
            }
    }

    @Override
    Boolean support(String companyId) {
        return "2019" == companyId
    }

    @Override
    def execute(Map<String, Object> context, Map<String, Object> step, String reqArgs) throws Exception {
        if (NEED_ENCRYPT_TEMPLATES.contains(step.name))
            context.response = encrypt(reqArgs, context, step)
        else if (NEED_ENCRYPT_AND_SIGN_TEMPLATES.contains(step.name))
            context.response = encryptAndSign(reqArgs, context, step)
        else {
            context.parseResp = TEXT_RESPONSE_PARSE
            super.execute(context, step, reqArgs)
        }
    }

    private String encryptAndSign(String reqArgs, Map<String, Object> context, Map<String, Object> step) {
        def req = JSONObject.toJSONString(reqArgs)
        reqArgs = [
            signature  : YangguangEncryptionUtil.sign(reqArgs, context.key.toString()),
            requestBody: req.substring(1, req.length() - 1)
        ].with {
            StringEscapeUtils.unescapeJava(JsonOutput.toJson(it))
        }
        context.response = super.execute(context, step, reqArgs)
    }

    private String encrypt(String reqArgs, Map<String, Object> context, Map<String, Object> step) {
        reqArgs = YangguangEncryptionUtil.authcodeEncode(reqArgs, context.key.toString())
        //调用接口
        context.parseResp = YG_DECODE_RESPONSE_PARSE.clone().curry(context.key.toString())

        context.response = super.execute(context, step, reqArgs)
    }
}
