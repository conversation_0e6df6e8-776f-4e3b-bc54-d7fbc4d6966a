package com.cheche365.bc.admin.service.external.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import com.cheche365.bc.admin.service.dto.AutoTaskData;
import com.cheche365.bc.admin.service.dto.base.BaseSuiteInfo;
import com.cheche365.bc.admin.service.dto.base.CarInfo;
import com.cheche365.bc.admin.service.dto.base.NonAutoProduct;
import com.cheche365.bc.admin.service.dto.base.PersonInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> shanxf
 */
@Data
public class QuoteRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    private String enquiryId;
    private Long cityCode;
    private CarInfo auto;
    private PersonsMap personsMap;
    private BaseSuiteInfo baseSuiteInfo;
    private String configId;
    private NonAutoProduct nonAutoProduct;
    private SupplementInfo supplementInfo;

    @Data
    @Builder
    private static class PersonsMap implements Serializable {

        @Serial
        private static final long serialVersionUID = -1L;

        private PersonInfo carOwner;
        private PersonInfo insured;
        private PersonInfo applicant;
    }

    @Data
    @AllArgsConstructor
    private static class SupplementInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = -1L;

        private List<Pair<String, String>> texts;
    }

    public static QuoteRequest createByQuoteRequest(AutoTaskData autoTaskData) {
        QuoteRequest quoteRequest = BeanUtil.copyProperties(autoTaskData, QuoteRequest.class);
        if (Objects.nonNull(autoTaskData.getInsArea())) {
            quoteRequest.setCityCode(Long.valueOf(autoTaskData.getInsArea().getCity()));
        }
        quoteRequest.setAuto(autoTaskData.getCarInfo());
        quoteRequest.setPersonsMap(
            PersonsMap.builder()
                .carOwner(autoTaskData.getCarOwnerInfo())
                .applicant(autoTaskData.getApplicantPersonInfo())
                .insured(autoTaskData.getInsuredPersonInfo())
                .build()
        );
        quoteRequest.setSupplementInfo(createSupplementInfo(autoTaskData));
        return quoteRequest;
    }

    private static SupplementInfo createSupplementInfo(AutoTaskData autoTaskData) {
        List<Pair<String, String>> supplements = new ArrayList<>();
      /*  if (StrUtil.isNotBlank(autoTaskData.getCarOwnerInfo().getMobile())) {
            supplements.add(new Pair<>("ownerMobile", autoTaskData.getCarOwnerInfo().getMobile()));
        }
        if (StrUtil.isNotBlank(autoTaskData.getApplicantPersonInfo().getMobile())) {
            supplements.add(new Pair<>("applicantMobile", autoTaskData.getApplicantPersonInfo().getMobile()));
        }
        if (StrUtil.isNotBlank(autoTaskData.getInsuredPersonInfo().getMobile())) {
            supplements.add(new Pair<>("insuredMobile", autoTaskData.getInsuredPersonInfo().getMobile()));
        }*/
        return new SupplementInfo(supplements);
    }
}
