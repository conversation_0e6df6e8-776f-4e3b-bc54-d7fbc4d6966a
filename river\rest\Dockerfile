FROM registry.cn-beijing.aliyuncs.com/cheche365/openjdk:17.0-buster-tools
RUN mkdir -p /gclog && chmod -R 777 /gclog
WORKDIR /bc
COPY river/rest/build/libs/*river-rest-*.jar /bc
COPY libs/transmittable-thread-local-2.14.5.jar /bc
COPY libs/java.security /usr/local/openjdk-17/conf/security

CMD  [ "/bin/bash", "-c", "java \
    --add-opens=java.base/java.lang=ALL-UNNAMED \
    --add-opens=java.base/java.math=ALL-UNNAMED \
    --add-opens=java.base/java.util=ALL-UNNAMED \
    --add-opens=java.base/java.util.concurrent=ALL-UNNAMED \
    --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED \
    --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED \
    --add-opens=java.base/java.net=ALL-UNNAMED \
    --add-opens=java.base/java.text=ALL-UNNAMED \
    --add-opens=java.base/jdk.internal.misc=ALL-UNNAMED \
    --add-opens=java.base/java.nio=ALL-UNNAMED \
    --add-opens=java.base/java.io=ALL-UNNAMED \
    --add-opens=java.base/java.lang.ref=ALL-UNNAMED \
    --add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
    --add-opens=java.base/java.time=ALL-UNNAMED \
    -XX:+ClassUnloading \
    -Dio.netty.tryReflectionSetAccessible=true \
    -Duser.timezone=GMT+08 \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/dumpmem/ \
    -XX:+PrintCommandLineFlags \
    -XX:G1MixedGCCountTarget=8 \
    -XX:ParallelGCThreads=8 \
    -XX:G1HeapRegionSize=4m \
    -XX:InitiatingHeapOccupancyPercent=30 \
    -XX:NativeMemoryTracking=summary \
    -Xms1g \
    -Xlog:gc*,gc+heap=info:file=/gclog/gc-info-$POD_NAME.log:time,uptime,level,tags \
    -javaagent:./transmittable-thread-local-2.14.5.jar \
    -jar *river-rest-*.jar"]
