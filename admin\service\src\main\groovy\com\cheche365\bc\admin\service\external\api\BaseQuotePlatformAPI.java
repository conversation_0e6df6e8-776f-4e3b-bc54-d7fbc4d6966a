package com.cheche365.bc.admin.service.external.api;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import com.cheche365.bc.admin.service.external.dto.BaseResp;
import com.cheche365.bc.config.SpringUtil;
import com.cheche365.bc.utils.RuntimeUtil;
import com.cheche365.cheche.signature.Parameters;
import com.cheche365.cheche.signature.Secrets;
import com.cheche365.cheche.signature.api.HMAC_SHA1;
import com.cheche365.cheche.signature.client.ClientSignatureFilter;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.GenericType;
import com.sun.jersey.api.client.WebResource;
import com.sun.jersey.api.client.filter.LoggingFilter;

import javax.ws.rs.HttpMethod;
import javax.ws.rs.core.MediaType;
import java.util.Map;
import java.util.logging.Logger;

import static com.cheche365.bc.admin.service.external.api.HttpClientFactory.createClient;


public abstract class BaseQuotePlatformAPI<T> {

    private static final String APP_ID = "b27c9954";
    private static final String SECRET = "3c4e-4506-97cc-032bfb8da84e";
    private static final Client client;

    static {
        client = createClient();

        client.addFilter(new LoggingFilter(Logger.getLogger(BaseQuotePlatformAPI.class.getName())));

        client.addFilter(new ClientSignatureFilter(client.getProviders(),
            new Parameters().appId(APP_ID).version(Parameters.Version.VERSION_2_0).signatureMethod(HMAC_SHA1.NAME),
            new Secrets().appSecret(SECRET)));

    }

    protected T call(String body) {
        return call(body, null);
    }

    @SuppressWarnings("unchecked")
    protected T call(String body, Map<String, Object> paramMap) {
        WebResource resource = client.resource(basePath()).path(path());
        if(MapUtil.isNotEmpty(paramMap)){
            for (Map.Entry<String, Object> qs : paramMap.entrySet()) {
                resource = resource.queryParam(qs.getKey(), qs.getValue().toString());
            }
        }
        WebResource.Builder builder = resource.getRequestBuilder();
        if (needToken()){
            String token = SpringUtil.getApplicationContext().getBean(TokenAPI.class).getToken();
            builder.header("ACCESS-TOKEN",token);
        }

        ClientResponse response = null;
        if (HttpMethod.GET.equals(method())) {
            response = builder.method(method(), ClientResponse.class);
        } else if (HttpMethod.POST.equals(method())) {
            response = builder.type(contentType()).method(method(), ClientResponse.class, body);
        }

        BaseResp<JSONObject> respObject = response.getEntity(new GenericType<>() {
        });

        Assert.isTrue(respObject.getCode() == 200, () -> new RuntimeException(respObject.getMessage()));

        return (T) respObject.getData().toBean(responseType());
    }

    private String basePath() {
        if (RuntimeUtil.isProductionEnv()){
            return "https://auto.api.chetimes.com/api/public/v2";
        }
        return "https://auto.stg.api.chetimes.com/api/public/v2";
    }

    public Boolean needToken() {
        return true;
    }

    public MediaType contentType() {
        return MediaType.APPLICATION_JSON_TYPE;
    }

    public abstract Class<?> responseType();

    public abstract String path();

    public abstract String method();
}
