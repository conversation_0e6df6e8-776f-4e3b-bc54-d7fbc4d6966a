package com.cheche365.bc.common;

import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

/**
 * 带有版本的数据对象
 * Created by austin on 16/5/10.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class VersionObject {

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @FieldDoc(des = "对象版本号", remark = "")
    private int version;


    @FieldDoc(des = "对象Id", remark = "")
    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
