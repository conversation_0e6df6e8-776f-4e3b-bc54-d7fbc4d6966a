package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.PolicySource;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
public interface PolicySourceService extends IService<PolicySource> {


    void savePolicySource(PolicySource source);

    PolicySource getByPolicyNo(String policyNo);


    /**
     * 根据保司 id 和保单号查询数字资产信息
     *
     * @param policyNos 保单号
     * @param insId     保司 ID
     * @return list[PolicySource]
     */
    List<PolicySource> listByPolicyNos(Collection<String> policyNos);

}

