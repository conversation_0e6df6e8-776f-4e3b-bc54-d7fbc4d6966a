package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.service.AutoTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时任务，创建表
 */
@Component
@Slf4j
@RequiredArgsConstructor
@JobHandler(value = "createTableJobHandler")
public class CreateTableJobHandler extends I<PERSON><PERSON><PERSON>andler {

    private final AutoTaskService autoTaskService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        String suffixName = null;
        if (StrUtil.isNotBlank(s)) {
            suffixName = s;
        }
        autoTaskService.createTable(suffixName);
        log.info("创建表 auto_task 完成");
        return ReturnT.SUCCESS;
    }

}
