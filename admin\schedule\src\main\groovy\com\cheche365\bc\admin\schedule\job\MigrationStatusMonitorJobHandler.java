package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.service.util.TableSwitchUtil;
import com.cheche365.bc.sharding.OldTableSwitch;
import com.cheche365.bc.sharding.QueryTableSwitch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 迁移状态监控Job处理器
 * 用于监控和报告表迁移的状态，支持自动化迁移流程
 * 
 * 参数格式：
 * - "monitor": 监控当前状态
 * - "auto-migrate": 自动执行迁移流程（阶段1→阶段2→阶段3）
 * - "validate": 验证当前状态的合理性
 * - "report": 生成详细状态报告
 * 
 * <AUTHOR>
 */
@Component
@JobHandler(value = "migrationStatusMonitorJobHandler")
@AllArgsConstructor
@Slf4j
public class MigrationStatusMonitorJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            log.info("开始执行迁移状态监控Job，参数: {}", param);
            
            if (StrUtil.isBlank(param)) {
                param = "monitor"; // 默认为监控模式
            }
            
            param = param.trim().toLowerCase();
            String result = executeMonitor(param);
            
            log.info("迁移状态监控Job执行完成，结果: {}", result);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, result);
            
        } catch (Exception e) {
            log.error("迁移状态监控Job执行失败，参数: {}", param, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行监控操作
     */
    private String executeMonitor(String action) {
        switch (action) {
            case "monitor":
                return monitorCurrentStatus();
                
            case "auto-migrate":
                return executeAutoMigration();
                
            case "validate":
                return validateCurrentStatus();
                
            case "report":
                return generateDetailedReport();
                
            default:
                throw new IllegalArgumentException("不支持的操作: " + action + 
                    "，支持的操作: monitor, auto-migrate, validate, report");
        }
    }

    /**
     * 监控当前状态
     */
    private String monitorCurrentStatus() {
        StringBuilder report = new StringBuilder();
        report.append("=== 迁移状态监控报告 ===\n");
        report.append("监控时间: ").append(DateUtil.now()).append("\n");
        report.append(TableSwitchUtil.getCurrentSwitchStatus()).append("\n");
        report.append(TableSwitchUtil.getMigrationPhase()).append("\n");
        
        // 状态建议
        String suggestion = getStatusSuggestion();
        if (StrUtil.isNotBlank(suggestion)) {
            report.append("建议: ").append(suggestion).append("\n");
        }
        
        report.append("======================");
        return report.toString();
    }

    /**
     * 执行自动迁移（谨慎使用）
     */
    private String executeAutoMigration() {
        log.warn("开始执行自动迁移流程，请确保已经充分测试！");
        
        boolean oldTableSwitch = OldTableSwitch.isUseOldTable();
        boolean queryTableSwitch = QueryTableSwitch.isUseOldTableForQuery();
        
        StringBuilder result = new StringBuilder();
        result.append("=== 自动迁移执行报告 ===\n");
        result.append("开始状态: ").append(TableSwitchUtil.getCurrentSwitchStatus()).append("\n");
        
        if (oldTableSwitch && queryTableSwitch) {
            // 阶段1 → 阶段2：开启查询新表
            log.info("执行阶段1→阶段2迁移：开启查询新表");
            TableSwitchUtil.setTargetMigrationState();
            result.append("已执行: 阶段1→阶段2，开启查询新表\n");
            
        } else if (oldTableSwitch && !queryTableSwitch) {
            // 阶段2 → 阶段3：关闭双写
            log.warn("执行阶段2→阶段3迁移：关闭双写（高风险操作）");
            TableSwitchUtil.setFullyMigratedState();
            result.append("已执行: 阶段2→阶段3，关闭双写\n");
            
        } else if (!oldTableSwitch && !queryTableSwitch) {
            result.append("已处于完全迁移状态，无需操作\n");
            
        } else {
            result.append("当前状态异常，建议手动检查\n");
        }
        
        result.append("结束状态: ").append(TableSwitchUtil.getCurrentSwitchStatus()).append("\n");
        result.append("======================");
        
        return result.toString();
    }

    /**
     * 验证当前状态
     */
    private String validateCurrentStatus() {
        StringBuilder validation = new StringBuilder();
        validation.append("=== 状态验证报告 ===\n");
        
        boolean oldTableSwitch = OldTableSwitch.isUseOldTable();
        boolean queryTableSwitch = QueryTableSwitch.isUseOldTableForQuery();
        
        // 检查状态合理性
        if (oldTableSwitch && queryTableSwitch) {
            validation.append("✓ 阶段1状态正常：双写开启，查询旧表\n");
        } else if (oldTableSwitch && !queryTableSwitch) {
            validation.append("✓ 阶段2状态正常：双写开启，查询新表（目标状态）\n");
        } else if (!oldTableSwitch && !queryTableSwitch) {
            validation.append("✓ 阶段3状态正常：双写关闭，查询新表（完全迁移）\n");
        } else {
            validation.append("⚠ 异常状态：双写关闭但查询旧表（不推荐）\n");
        }
        
        // 检查Redis连接
        try {
            OldTableSwitch.isUseOldTable();
            QueryTableSwitch.isUseOldTableForQuery();
            validation.append("✓ Redis连接正常\n");
        } catch (Exception e) {
            validation.append("✗ Redis连接异常: ").append(e.getMessage()).append("\n");
        }
        
        validation.append("==================");
        return validation.toString();
    }

    /**
     * 生成详细报告
     */
    private String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 详细迁移状态报告 ===\n");
        report.append("生成时间: ").append(DateUtil.now()).append("\n\n");
        
        // 当前状态
        report.append("【当前状态】\n");
        report.append(TableSwitchUtil.getCurrentSwitchStatus()).append("\n");
        report.append(TableSwitchUtil.getMigrationPhase()).append("\n\n");
        
        // 开关详情
        report.append("【开关详情】\n");
        report.append("双写开关(OldTableSwitch): ").append(OldTableSwitch.isUseOldTable() ? "开启" : "关闭").append("\n");
        report.append("查询开关(QueryTableSwitch): ").append(QueryTableSwitch.isUseOldTableForQuery() ? "旧表" : "新表").append("\n\n");
        
        // 迁移建议
        report.append("【迁移建议】\n");
        report.append(getStatusSuggestion()).append("\n\n");
        
        // 风险提示
        report.append("【风险提示】\n");
        report.append(getRiskWarning()).append("\n");
        
        report.append("========================");
        return report.toString();
    }

    /**
     * 获取状态建议
     */
    private String getStatusSuggestion() {
        boolean oldTableSwitch = OldTableSwitch.isUseOldTable();
        boolean queryTableSwitch = QueryTableSwitch.isUseOldTableForQuery();
        
        if (oldTableSwitch && queryTableSwitch) {
            return "建议：可以考虑切换到阶段2（查询新表），验证新表数据完整性";
        } else if (oldTableSwitch && !queryTableSwitch) {
            return "建议：观察一段时间确保新表查询稳定后，可考虑关闭双写";
        } else if (!oldTableSwitch && !queryTableSwitch) {
            return "建议：已完成迁移，可考虑清理旧表数据";
        } else {
            return "建议：立即检查配置，当前状态不合理";
        }
    }

    /**
     * 获取风险警告
     */
    private String getRiskWarning() {
        boolean oldTableSwitch = OldTableSwitch.isUseOldTable();
        boolean queryTableSwitch = QueryTableSwitch.isUseOldTableForQuery();
        
        if (!oldTableSwitch && queryTableSwitch) {
            return "⚠ 高风险：双写已关闭但仍查询旧表，可能导致数据不一致";
        } else if (!oldTableSwitch && !queryTableSwitch) {
            return "⚠ 中风险：已完全迁移，确保旧表数据已备份";
        } else {
            return "✓ 风险较低：当前配置相对安全";
        }
    }
}
