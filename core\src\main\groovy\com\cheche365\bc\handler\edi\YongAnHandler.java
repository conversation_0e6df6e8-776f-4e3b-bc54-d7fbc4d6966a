package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.Hmac;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

@Component
public class YongAnHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.YAIC.getCode();
    }


    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        Hmac build = Hmac.builder()
                .encryptFormat(EncryptEnum.KeyFormatEnum.Base64)
                .key((String) autoTask.getConfigs().get("secretKey"))
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .algorithm(EncryptEnum.HmacAlgorithmEnum.HMAC_SHA256)
                .build();

        String consumerAccessKey = build.encode(requestBody.substring(requestBody.indexOf("<requestBody>") + 13, requestBody.indexOf("</requestBody>")));
        requestBody = requestBody.replace("<consumerAccessKey>", "<consumerAccessKey>" + consumerAccessKey);
        return HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getRepHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
    }
}
