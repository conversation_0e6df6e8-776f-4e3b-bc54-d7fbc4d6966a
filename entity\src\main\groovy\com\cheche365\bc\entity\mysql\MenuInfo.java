package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.cheche365.bc.entity.enums.MenuInfoStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bc_menu_info")
public class MenuInfo extends Model<MenuInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单的顶级级别
     */
    public static final int ROOT_LEVEL = 0;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单层级
     */
    private Integer level;

    /**
     * 父菜单id
     */
    private Long parentId;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 图标地址
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单状态（0：启用  不启用）
     */
    private MenuInfoStatus status;

    /**
     * 子级菜单
     */
    @TableField(exist = false)
    private List<MenuInfo> childList;

    /**
     * code
     */
    private String menuCode;

    /**
     * 国际化
     */
    private String locale;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
