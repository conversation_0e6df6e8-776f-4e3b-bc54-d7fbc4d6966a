package com.cheche365.bc.utils;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSON;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class IMUtil {

    private static final String URL = System.getenv("feishu.url");
    private static final String SECRET = System.getenv("feishu.secret");

    public static void sendFeiShuMessage(String text) {
        if (URL == null || SECRET == null) {
            System.out.println("Feishu URL or Secret not configured, skipping message send.");
            return;
        }
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String sign = genSign(timestamp, SECRET);
            Map<String, Object> body = createBody(text, timestamp, sign);
            String message = JSON.toJSONString(body);
            HttpSender.doPost(URL, message);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("飞书消息发送异常", e);
        }
    }

    private static Map<String, Object> createBody(String text, long timestamp, String sign) {
        Map<String, String> content = Maps.newHashMap();
        content.put("text", text);

        Map<String, Object> body = Maps.newHashMap();
        body.put("timestamp", timestamp);
        body.put("sign", sign);
        body.put("msg_type", "text");
        body.put("content", content);
        return body;
    }

    private static String genSign(long timestamp, String secret) {
        String stringToSign = timestamp + "\n" + secret;
        HMac hmac = new HMac(HmacAlgorithm.HmacSHA256);
        return hmac.digestBase64(stringToSign, StandardCharsets.UTF_8, false);
    }
}
