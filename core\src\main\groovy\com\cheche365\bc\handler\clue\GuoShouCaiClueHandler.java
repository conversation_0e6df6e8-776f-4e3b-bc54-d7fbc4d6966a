package com.cheche365.bc.handler.clue;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.handler.BaseClueHandler;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * <AUTHOR>
 */
@Component
public class GuoShouCaiClueHandler extends BaseClueHandler {

    @Value("${clue.guoshou.aesKey:''}")
    private String aesKey;

    @Override
    public String companyId() {
        return String.valueOf(InsCompanyEnum.GPIC.getCode());
    }
    
    @Override
    protected ClueCallbackDTO execute(String responseBody) throws Exception {

        AesDesEncryption build = AesDesEncryption
            .builder()
            .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
            .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
            .key(aesKey)
            .resultType(EncryptEnum.ResultTypeEnum.Hex)
            .build();

        String data = JSONObject.parseObject(responseBody).getString("data");
        String decrypt = build.decrypt(Hex.decode(data));
        JSONObject responseObj = JSONObject.parseObject(new String(Base64.getDecoder().decode(decrypt)));
        String enquiryId = responseObj.getJSONObject("request").getJSONObject("order").getString("customerId");
        return new ClueCallbackDTO(enquiryId, responseObj.toString());
    }
}
