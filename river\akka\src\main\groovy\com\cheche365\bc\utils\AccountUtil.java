package com.cheche365.bc.utils;

import com.cheche365.bc.actor.LoginConfig;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.task.KeepSessionConfig;
import com.google.common.base.Strings;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class AccountUtil {


    /**
     * 账号选举
     */
    public static Boolean multiAccountProcess(final TransTaskMsg transTaskMsg, final Map<String, Map> multiAccountMap) throws Exception {
        final Map<String, Object> config = transTaskMsg.getAutoTask().getConfigs();
        LoginConfig loginConfig = transTaskMsg.getLoginConfig();
        final String multiAccountSymbol = (String) config.get(KeepSessionConfig.MULTI_ACCOUNT_SYMBOL);
        final String originLogin = (String) config.get(KeepSessionConfig.LOGIN);
        if (!Strings.isNullOrEmpty(multiAccountSymbol)) {
            if (originLogin.contains(multiAccountSymbol)) {
                //配置信息裂变:变成一组配置
                Pair<String, Map<String, Map<String, String>>> fusionGroup = fusionConfig(config, multiAccountSymbol);
                Map.Entry<String, Pair<Integer, Map>> targetConfig;
                if (multiAccountMap.containsKey(fusionGroup.getLeft())) {
                    Map<String, Pair<Integer, Map>> configGroup = multiAccountMap.get(fusionGroup.getLeft());
                    //有新账号加入时,旧账号次数统一变成1次
                    boolean flag = fusionGroup.getRight().entrySet()
                            .stream()
                            .anyMatch(entry -> !(configGroup.containsKey(entry.getKey())));
                    Iterator<Map.Entry<String, Pair<Integer, Map>>> iterator = configGroup.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, Pair<Integer, Map>> it = iterator.next();
                        if (!fusionGroup.getRight().containsKey(it.getKey())) {
                            //将新配置中不存在的清除
                            iterator.remove();
                        } else {
                            //将新配置中存在的配置的初始次数置为1次
                            if (flag && it.getValue().getLeft() > 0) {
                                it.setValue(Pair.of(1, fusionGroup.getRight().get(it.getKey())));
                            }
                        }
                    }
                    //插入增加的新配置
                    fusionGroup.getRight().entrySet()
                            .stream()
                            .filter(entry -> !(configGroup.containsKey(entry.getKey())))
                            .forEach(entry -> configGroup.put(entry.getKey(), Pair.of(0, entry.getValue())));
                    //排序取出本次执行的账号
                    targetConfig = configGroup.entrySet()
                            .stream()
                            .sorted(Comparator.comparingInt(value -> value.getValue().getLeft()))
                            .findFirst()
                            .get();
                } else {
                    Map<String, Pair<Integer, Map>> accountGroup = new HashMap();
                    fusionGroup.getRight().forEach((key, value) -> accountGroup.put(key, Pair.of(0, value)));
                    multiAccountMap.put(fusionGroup.getLeft(), accountGroup);
                    //并行操作随机返回一个结果,避免集群部署时第一次调用都执行同一个账号
                    targetConfig = accountGroup.entrySet()
                            .stream()
                            .parallel()
                            .findAny()
                            .get();
                }
                targetConfig.setValue(Pair.of(targetConfig.getValue().getLeft() + 1, fusionGroup.getRight().get(targetConfig.getKey())));
                String login = targetConfig.getKey();
                transTaskMsg.setAccount(login);
                loginConfig.setLogin(login);
                loginConfig.setExpectSuccessFlag(((String) (targetConfig.getValue().getRight().get(KeepSessionConfig.EXPECT_SUCCESS_FLAG))));
                transTaskMsg.getAutoTask().setConfigs(targetConfig.getValue().getValue());
            } else {
                return false;
            }
        }
        return true;
    }


    /**
     * 多帐号配置分组
     */
    public static Pair<String, Map<String, Map<String, String>>> fusionConfig(final Map<String, Object> config, final String multiAccountSymbol) {
        //assistantConfig=辅助配置集合
        Map<String, Map<String, String>> fusionGroup = new HashMap();
        final String login = (String) config.get(KeepSessionConfig.LOGIN);
        String[] loginGroup = login.split(multiAccountSymbol);
        config.entrySet().stream().filter(entry -> entry.getValue().toString().contains(multiAccountSymbol) && !entry.getKey().equals(KeepSessionConfig.MULTI_ACCOUNT_SYMBOL)).forEach(entry -> {
            String[] values = entry.getValue().toString().split(multiAccountSymbol);
            for (int i = 0; i < loginGroup.length; i++) {
                String key = loginGroup[i];
                Map<String, String> fusionConfig;
                if (fusionGroup.containsKey(key)) {
                    fusionConfig = fusionGroup.get(key);
                } else {
                    fusionConfig = new HashMap<>();
                    fusionGroup.put(key, fusionConfig);
                }
                String value;
                if (i >= values.length) {
                    value = values[values.length - 1];
                } else {
                    value = values[i];
                }
                fusionConfig.put(entry.getKey(), value);
            }
        });
        Map<String, String> commonConfig = new HashMap();
        config.entrySet().stream().filter(entry -> !entry.getValue().toString().contains(multiAccountSymbol)).forEach(entry -> commonConfig.put(entry.getKey(), entry.getValue().toString()));
        fusionGroup.values().forEach(map -> map.putAll(commonConfig));
        return Pair.of(loginGroup[0], fusionGroup);
    }
}
