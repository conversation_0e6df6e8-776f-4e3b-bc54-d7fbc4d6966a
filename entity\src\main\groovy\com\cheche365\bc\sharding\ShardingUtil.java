package com.cheche365.bc.sharding;

import com.cheche365.bc.config.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;


/**
 * <AUTHOR>
 */
@Slf4j
public class ShardingUtil {

    public static boolean execute(String sql) {
        Environment env = SpringUtil.getApplicationContext().getEnvironment();
        String host = env.getProperty("mysql_host");
        String port = env.getProperty("mysql_port");
        String db = env.getProperty("mysql_db");

        String username = env.getProperty("mysql_username");
        String password = env.getProperty("mysql_password");
        String url = "jdbc:mysql://" + host + ":" + port + "/" + db + "?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8";

        try (Connection connection = DriverManager.getConnection(url, username, password); Statement statement = connection.createStatement()) {
            return statement.execute(sql);
        } catch (Exception e) {
            log.error("获取数据库连接失败", e);
        }
        return false;
    }


}
