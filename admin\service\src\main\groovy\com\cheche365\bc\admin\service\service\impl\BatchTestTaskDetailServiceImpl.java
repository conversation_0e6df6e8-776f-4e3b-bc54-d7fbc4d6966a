package com.cheche365.bc.admin.service.service.impl;

import com.cheche365.bc.admin.service.dto.AutoTaskData;
import com.cheche365.bc.admin.service.service.BatchTestTaskDetailService;
import com.cheche365.bc.entity.mongo.BatchTestTaskDetail;
import com.cheche365.bc.repo.BatchTestTaskDetailRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class BatchTestTaskDetailServiceImpl implements BatchTestTaskDetailService {

    private final BatchTestTaskDetailRepo batchTestTaskDetailRepo;
    private final StringRedisTemplate stringRedisTemplate;
    private static final String _TASK_DETAIL_ID_KEY = "batchTestTaskDetailIds";

    @Override
    public void saveBatchTestTaskDetails(List<AutoTaskData> autoTaskDataList, Long batchTestTaskId) {
        List<BatchTestTaskDetail> batchTestTaskDetails = autoTaskDataList
            .stream()
            .map(autoTaskData ->
                BatchTestTaskDetail.builder()
                    .batchTestTaskId(batchTestTaskId)
                    .autoTaskData(autoTaskData)
                    .build()
            )
            .toList();
        List<BatchTestTaskDetail> testTaskDetails = batchTestTaskDetailRepo.saveAll(batchTestTaskDetails);

        pushIdsIntoCache(testTaskDetails);
    }

    @Override
    public BatchTestTaskDetail popTestTaskDetail() {
        String batchTestTaskDetailId = stringRedisTemplate.opsForList().rightPop(_TASK_DETAIL_ID_KEY);
        if (StringUtils.isBlank(batchTestTaskDetailId)) {
            return null;
        }
        log.info("popTestTaskDetail : {}", batchTestTaskDetailId);
        return batchTestTaskDetailRepo.findById(batchTestTaskDetailId).orElse(null);
    }

    private void pushIdsIntoCache(List<BatchTestTaskDetail> batchTestTaskDetails) {
        List<String> batchTestTaskDetailIds = batchTestTaskDetails
            .stream()
            .map(BatchTestTaskDetail::getId)
            .toList();

        stringRedisTemplate.opsForList().leftPushAll(_TASK_DETAIL_ID_KEY, batchTestTaskDetailIds);
    }
}
