package com.cheche365.bc.admin.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.RoleInfo;
import com.cheche365.bc.entity.mysql.UserInfo;
import com.cheche365.bc.model.RestResponse;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
public interface RoleInfoService extends IService<RoleInfo> {

    /**
     * 检查权限
     *
     * @param roleInfo
     * @return
     */
    RestResponse checkRole(RoleInfo roleInfo);

    /**
     * 添加或修改角色
     *
     * @param roleInfo
     * @return
     */
    boolean addOrUpdate(RoleInfo roleInfo, UserInfo loginUser);
}
