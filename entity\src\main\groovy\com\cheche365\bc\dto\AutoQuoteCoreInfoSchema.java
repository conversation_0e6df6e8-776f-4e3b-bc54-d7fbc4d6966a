package com.cheche365.bc.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.beanutils.BeanUtils;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */


@EqualsAndHashCode(callSuper = true)
@Data
public class AutoQuoteCoreInfoSchema extends AutoTaskBiLogSchema {

    /**
     * 车主类型 0-个人 1-企业
     */
    private Integer carOwnerType;

    /**
     * 投保人类型 0-个人 1-企业
     */
    private Integer applicantType;

    /**
     * 被保人类型 0-个人 1-企业
     */
    private Integer insuredType;

    /**
     * 被保人性别
     */
    private Integer insuredSex;

    /**
     * 被保人年龄
     */
    private Integer insuredAge;

    /**
     * 座位数
     */
    private Integer seatCnt;


    /**
     * 初登日期
     */
    private LocalDate firstRegDate;

    /**
     * 过户日期
     */
    private LocalDate transferDate;

    /**
     * 新车开具发票日期
     */
    private LocalDate carOriginProofDate;

    /**
     * 新车是否4S销售
     */
    private Boolean isSaleBy4S = false;

    /**
     * 是否本地使用
     */
    private Boolean isLocalDriving = true;

    /**
     * 车辆是否本地上牌
     */
    private Boolean isLocalRegistration = true;

    /**
     * 商业险连续投保年数
     */
    private Integer bizContinuityInsureYears;

    /**
     * 总评分
     */
    private Float totalScore;

    /**
     * 商业险评分
     */
    private Float bizScore;

    /**
     *  交强险评分
     */
    private Float trafficScore;

    /**
     * 商业险评级
     */
    private String bizRate;

    /**
     *  交强险评级
     */
    private String trafficRate;

    /**
     * NCD(无赔款优待系数)
     */
    private Float noClaimDiscountCoefficient;

    /**
     * 自主定价系数
     */
    private Float selfRate;

    /**
     * 交通违法系数
     */
    private Float trafficOffenceDiscount;

    /**
     * 连续承保期间商业险理赔次数
     */
    private Integer bwCommercialClaimTimes;

    /**
     * 上年交强险理赔次数
     */
    private Integer bwCompulsoryClaimTimes;

    /**
     * 商业险手续费比例
     */
    private Float commercialBrokerageRate;

    /**
     * 交强险手续费比例
     */
    private Float compulsoryBrokerageRate;

    public AutoQuoteCoreInfoSchema(AutoTaskBiLogSchema parent) throws Exception {
        BeanUtils.copyProperties(this, parent);
    }

}
