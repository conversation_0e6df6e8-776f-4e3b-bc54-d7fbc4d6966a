package com.cheche365.bc.admin.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.RoleInfoService;
import com.cheche365.bc.admin.service.service.RolePermissionService;
import com.cheche365.bc.admin.service.service.UserLogService;
import com.cheche365.bc.entity.mysql.RoleInfo;
import com.cheche365.bc.entity.mysql.RolePermission;
import com.cheche365.bc.entity.mysql.UserInfo;
import com.cheche365.bc.entity.enums.OperateContent;
import com.cheche365.bc.mapper.RoleInfoMapper;
import com.cheche365.bc.model.RestResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@Service
@AllArgsConstructor
public class RoleInfoServiceImpl extends ServiceImpl<RoleInfoMapper, RoleInfo> implements RoleInfoService {

    private final Validator validator;
    private final RolePermissionService rolePermissionService;
    private final UserLogService userLogService;

    @Override
    public RestResponse checkRole(final RoleInfo roleInfo) {
        if (roleInfo == null) {
            return RestResponse.failedMessage("请填写角色相关信息");
        }

        Set<ConstraintViolation<RoleInfo>> validateResult = validator.validate(roleInfo);
        StringBuilder errorMessage = new StringBuilder();
        if (CollectionUtils.isNotEmpty(validateResult)) {
            validateResult.forEach(it -> errorMessage.append(it.getMessage()).append(" "));
        }

        // roleId == null, 判断角色名称是否唯一; roleId != null, 判断角色名称是否更改
        if (roleInfo.getId() == null) {
            long count = this.count(new QueryWrapper<RoleInfo>()
                    .lambda()
                    .eq(RoleInfo::getName, roleInfo.getName())
                    .last("limit 1"));
            if (count > 0) {
                errorMessage.append("角色名称已存在 ");
            }
        } else {
            RoleInfo roleById = this.getById(roleInfo.getId());
            if (roleById == null) {
                errorMessage.append("角色信息不存在 ");
            } else if (!StringUtils.equals(roleById.getName(), roleInfo.getName())) {
                errorMessage.append("角色名称不可修改 ");
            }
        }

        if (errorMessage.length() > 0) {
            return RestResponse.failedMessage(errorMessage.toString());
        }
        return RestResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrUpdate(RoleInfo roleInfo, UserInfo loginUser) {
        if (roleInfo == null) {
            return false;
        }
        // 最新更新时间
        roleInfo.setUpdateTime(LocalDateTime.now());

        // 处理角色基础信息
        boolean flag;
        OperateContent operateContent;
        if (roleInfo.getId() == null) {
            // 新增
            roleInfo.setCreateTime(LocalDateTime.now());
            roleInfo.setCreateUser(loginUser.getEmail());
            flag = roleInfo.insert();
            operateContent = OperateContent.ADD;
        } else {
            // 修改
            flag = roleInfo.updateById();
            operateContent = OperateContent.EDIT;
        }
        if (!flag) {
            return false;
        }

        final Long roleId = roleInfo.getId();
        // 删除更新权限信息
        rolePermissionService.remove(new QueryWrapper<RolePermission>().lambda().eq(RolePermission::getRoleId, roleId));

        // new permission
        List<Long> permissionList = roleInfo.getPermissionList();
        if (CollectionUtils.isNotEmpty(permissionList)) {
            permissionList.forEach(it -> {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setRoleId(roleId);
                rolePermission.setPermissionId(it);
                rolePermissionService.save(rolePermission);
            });
        }

        // 保存日志
        userLogService.saveRoleLog(roleInfo, loginUser, operateContent, JSONObject.toJSONString(roleInfo));
        return true;
    }
}
