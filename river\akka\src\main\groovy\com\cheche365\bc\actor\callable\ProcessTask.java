package com.cheche365.bc.actor.callable;

import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.exception.TaskException;
import com.cheche365.bc.mock.MockClient;
import com.cheche365.bc.service.TaskProcessFactory;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.KeepSessionConfig;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.impl.client.CloseableHttpClient;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;

import static com.cheche365.bc.utils.RuntimeUtil.isProductionEnv;
import static java.lang.Boolean.FALSE;

/**
 * 执行业务流程
 */
@Slf4j
public class ProcessTask implements Callable<AutoTask> {

    private final AutoTask task;

    private Interface itf;

    private final TaskProcessFactory taskProcessFactory;

    public ProcessTask(AutoTask task, Interface itf, TaskProcessFactory taskProcessFactory) {
        this.task = task;
        this.itf = itf;
        this.taskProcessFactory = taskProcessFactory;
    }

    @Override
    public AutoTask call() throws Exception {
        try {
            //mock server 路由判定
            AutoTask mockCallback = callbackByMockService();
            if (mockCallback != null) return mockCallback;

            //
            if (task.getTaskType().startsWith("edi")) {
                //初始化连接池
                task.setHttpClient(initHttpClient());
            }

            //执行流程
            taskProcessFactory.processTask(itf, task);
            //关闭连接池
            if (task.getTaskType().startsWith("edi")) {
                if (task.getHttpClient() != null && task.getHttpClient() instanceof CloseableHttpClient httpClient) {
                    try {
                        httpClient.close();
                    } catch (Exception e) {
                        log.error("Error closing HttpClient", e);
                    }
                }
            }

            return task;
        } catch (Exception e) {
            log.error("任务执行异常：{}", ExceptionUtils.getStackTrace(e));
            throw new TaskException(task, e);
        }
    }

    private CloseableHttpClient initHttpClient() throws Exception {
        CloseableHttpClient closeableHttpClient;
        Map configMap = task.getConfigs();
        //log.info("EDI-配置的代理信息是："+JSON.toJSONString(configMap));
        boolean useSSL = Objects.nonNull(configMap.get("useNewSSL")) && "true".equals(configMap.get("useNewSSL").toString());
        if (configMap.containsKey(HttpSender.proxyHost)
            && configMap.containsKey(HttpSender.proxyPort)
            && configMap.containsKey(HttpSender.proxyName)
            && configMap.containsKey(HttpSender.proxyPass)) {

            closeableHttpClient = HttpSender.buildHttpClient(true, configMap.get(HttpSender.proxyHost).toString(), Integer.parseInt(configMap.get(HttpSender.proxyPort).toString()), configMap.get(HttpSender.proxyName).toString(), configMap.get(HttpSender.proxyPass).toString(), useSSL ? HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}) : null);
        } else {
            //刷新失败也要关闭旧链接再次登陆。免得保险公司策略认为多次同链接登陆。没有注销，有异常行为
            boolean usePool = Boolean.parseBoolean(configMap.getOrDefault(KeepSessionConfig.USE_POOL_CONNECT, "true").toString());
            if (useSSL) {
                try {
                    closeableHttpClient = HttpSender.buildHttpClient(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}));
                } catch (Exception e) {
                    log.error("EDI初始化TLSv1.2类型HttpClient失败：{}", ExceptionUtils.getStackTrace(e));
                    closeableHttpClient = HttpSender.buildHttpClient(usePool);
                }
            } else {
                closeableHttpClient = HttpSender.buildHttpClient(usePool);
            }
        }
        return closeableHttpClient;
    }

    private AutoTask callbackByMockService() throws TaskException {
        if (FALSE.equals(task.getConfigs().getOrDefault("mockRequest", false))) {
            return null;
        }
        if (isProductionEnv()) {
            throw new TaskException(task, "不支持此任务");
        }
        MockClient.call(task);
        if (task.getFeedbackJson().contains("errorInfo")) {
            throw new TaskException(task, "mock执行失败");
        }
        task.setEndFlag(true);
        task.setEndTime(LocalDateTime.now());
        task.setConcatResultStr("mock success");
        return task;
    }

}
