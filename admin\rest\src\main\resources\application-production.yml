logging:
  file:
    path: /data/nfs0/logs/river_service

api:
  sign-val: 8f0e85d2-b128-6aa3-a377-dcf0f879078c

sftp:
  client:
    username: cheche_prod
    password: xgBnC2n6YOUv

app:
  key: f87b8ba2
cjy:
  password: zzb2018#
  soft_id: 895193
  username: zhangzhongbao
cos:
  appID: ********
  bucketName: newzzbdev
  secretID: AKIDhvjglwX43Fokcrwf2Tcbxn0qrFP086xt
  secretKey: z09FxlCheYxirVlW90pgMG6QKnFrlwi1
ff:
  app_id: 303046
  app_key: j0KUUhetOuV4sskHG45C3YbbhTH4VAkc
  pd_id: 102846
  pd_key: vyzqgJu9iFV19yohAYeE5okbWhv2MGJG
pull:
  accounts:
    url: https://bedrock.chetimes.com
sdas:
  app:
    key: ********-c59c-4e3d-940a-79b8fbdd0d37
  secret:
    key: ce051956-a839-451d-ab9c-532735023d1b
  url: http://sdas.bedrock.chetimes.com/api/assets
secret:
  key: 790e-43da-9078-af838d0fd3c0
system:
  akkaPath: /opt/deps/tomcat/webapps/ins-auto-service/WEB-INF/classes/akka.conf
  dataTransFormPath: /msgParseRule.json
  ip: *********
  trainsImgDir: /opt/deps/tomcat/webapps/ins-auto-service/dev1-5/
  validImgDir: /opt/deps/tomcat/webapps/ins-auto-service/dev1-5/
hbase:
  conf:
    enable-auth: true
