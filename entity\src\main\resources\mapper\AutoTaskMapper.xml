<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cheche365.bc.mapper.AutoTaskMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        autoTraceId, actionLogs, applyJson, endTime, company_id, plate_no, resultStr, startTime, task_status, taskType, traceKey, feedbackJson, biz_propose_no, efc_propose_no, biz_policy_no, efc_policy_no, failureCause, failureCauseCategory
    </sql>
</mapper>
