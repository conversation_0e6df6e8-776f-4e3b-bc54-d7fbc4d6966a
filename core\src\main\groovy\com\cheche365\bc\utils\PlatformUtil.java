package com.cheche365.bc.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.model.PlatformKey;
import com.cheche365.bc.model.RuleInfoKey;
import com.cheche365.bc.model.car.CarInfo;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.model.car.InsurePerson;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Stream;

/**
 * Created by Co on 2017/3/10.
 */
public class PlatformUtil {
    private static final Logger LOG = LoggerFactory.getLogger("PlatformUtil");

    public static final Map<Integer, String> CLAIM_TIMES_MAP = new HashMap<>();

    static {
        CLAIM_TIMES_MAP.put(0, "连续承保期间没有出险");
        CLAIM_TIMES_MAP.put(1, "连续承保期间出险一次");
        CLAIM_TIMES_MAP.put(2, "连续承保期间出险两次");
        CLAIM_TIMES_MAP.put(3, "连续承保期间出险三次");
        CLAIM_TIMES_MAP.put(4, "连续承保期间出险四次");
        CLAIM_TIMES_MAP.put(5, "连续承保期间出险五次");
        CLAIM_TIMES_MAP.put(6, "连续承保期间出险六次");
        CLAIM_TIMES_MAP.put(7, "连续承保期间出险七次");
        CLAIM_TIMES_MAP.put(8, "连续承保期间出险八次");
        CLAIM_TIMES_MAP.put(9, "连续承保期间出险九次");
        CLAIM_TIMES_MAP.put(10, "连续承保期间出险十次");
        CLAIM_TIMES_MAP.put(11, "连续承保期间出险十次以上");
    }

    /**
     * 获取各保险公司开发返回的平台信息
     *
     * @param autoTask
     */
    public static void initPlatformInfo(AutoTask autoTask) {

        if (autoTask.getTempValues().containsKey(PlatformKey.platformBack)) {
            //获取中间变量中各开发存储的平台信息.
            Object platformInfo = autoTask.getTempValues().get(PlatformKey.platformBack);

            try {

                if (platformInfo != null) {
                    final Map temp = (Map) platformInfo;
                    final HashMap<String, Object> task = new HashMap<>(24);
                    final HashMap<String, String> def = new HashMap<>(4);
                    Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
                    if (enquiry.getOrder().getPlatformInfo() == null) {
                        enquiry.getOrder().setPlatformInfo(new HashMap<String, Object>(40));
                    }
                    Map<String, Object> platform = enquiry.getOrder().getPlatformInfo();

                    //字段含义参考 PlatformKey
                    temp.forEach((k, v) -> {
                        if (Objects.isNull(v))
                            v = "";
                        switch ((String) k) {
                            case PlatformKey.carBrandName:
                                task.put(PlatformKey.carBrandName, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.price:
                                task.put(PlatformKey.price, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.taxPrice:
                                task.put(PlatformKey.taxPrice, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.analogyPrice:
                                task.put(PlatformKey.analogyPrice, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.analogyTaxPrice:
                                task.put(PlatformKey.analogyTaxPrice, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.carModelDate:
                                task.put(PlatformKey.carModelDate, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.licenseNo:
                                task.put(PlatformKey.licenseNo, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.engineNo:
                                task.put(PlatformKey.engineNo, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.vinNo:
                                task.put(PlatformKey.vinNo, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.seatCnt:
                                task.put(PlatformKey.seatCnt, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.modelLoad:
                                task.put(PlatformKey.modelLoad, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.fullLoad:
                                task.put(PlatformKey.fullLoad, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.displacement:
                                task.put(PlatformKey.displacement, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.modelCode:
                                task.put(PlatformKey.modelCode, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.vehicleInfo_tradeModelCode:
                                task.put(PlatformKey.vehicleInfo_tradeModelCode, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.car_specific_selfInsureRate:
                                task.put(PlatformKey.car_specific_selfInsureRate, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.car_specific_selfChannelRate:
                                task.put(PlatformKey.car_specific_selfChannelRate, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.car_specific_NcdRate:
                                task.put(PlatformKey.car_specific_NcdRate, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.vehicleInfo_vehicleType:
                                task.put(PlatformKey.vehicleInfo_vehicleType, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.quoteItems_ruleItem_basicRiskPremium:
                                task.put(PlatformKey.quoteItems_ruleItem_basicRiskPremium, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.plateform_InsureCo:
                                task.put(PlatformKey.plateform_InsureCo, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            case PlatformKey.application_lastInsureCo:
                                task.put(PlatformKey.application_lastInsureCo, StringUtil.isNoEmpty(v.toString()) ? v : "");
                                break;
                            default:
                        }
                    });

                    platform.putAll(temp);
                    CarInfo carInfo = enquiry.getOrder().getCarInfo();
                    //非续保
                    if (!enquiry.isRenewal()) {
                        //从数据源获取车俩基本信息
                        task.put(PlatformKey.licenseNo, carInfo.getPlateNum());
                        task.put(PlatformKey.engineNo, carInfo.getEngineNum());
                        task.put(PlatformKey.vinNo, carInfo.getVin());
                        task.put(PlatformKey.seatCnt, carInfo.getSeatCnt());
                        task.put(PlatformKey.modelLoad, carInfo.getModelLoad());
                    }
                    final String bizScore = RuleUtil.checkValue(platform, PlatformKey.bizScore);
                    if (StringUtil.isNoEmpty(bizScore)) {
                        def.put(PlatformKey.bizScore, bizScore);
                    }
                    final String bizRate = RuleUtil.checkValue(platform, RuleInfoKey.BIZ_RATE);
                    if (StringUtil.isNoEmpty(bizRate)) {
                        def.put(RuleInfoKey.BIZ_RATE, bizRate);
                    }
                    final String trafficRate = RuleUtil.checkValue(platform, RuleInfoKey.TRAFFIC_RATE);
                    if (StringUtil.isNoEmpty(trafficRate)) {
                        def.put(RuleInfoKey.TRAFFIC_RATE, trafficRate);
                    }
                    final String trafficScore = RuleUtil.checkValue(platform, PlatformKey.TRAFFIC_SCORE);
                    if (StringUtil.isNoEmpty(trafficScore)) {
                        def.put(PlatformKey.TRAFFIC_SCORE, trafficScore);
                    }
                    final String serviceCode = RuleUtil.checkValue(platform, PlatformKey.SERVICE_CODE);
                    if (StringUtil.isNoEmpty(serviceCode)) {
                        def.put(PlatformKey.SERVICE_CODE, serviceCode);
                    }
                    final String taxDerateType = RuleUtil.checkValue(platform, RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE);
                    if (StringUtil.isNoEmpty(taxDerateType)) {
                        def.put(RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE, taxDerateType);
                    }
                    final String channelDiscount = RuleUtil.checkValue(platform, RuleInfoKey.geniusItem_channelDiscount);
                    if (StringUtil.isNoEmpty(channelDiscount)) {
                        def.put(RuleInfoKey.geniusItem_channelDiscount, channelDiscount);
                    }
                    final String insureDiscount = RuleUtil.checkValue(platform, RuleInfoKey.geniusItem_insureDiscount);
                    if (StringUtil.isNoEmpty(insureDiscount)) {
                        def.put(RuleInfoKey.geniusItem_insureDiscount, insureDiscount);
                    }
                    final String totalDiscount = RuleUtil.checkValue(platform, RuleInfoKey.geniusItem_totalDiscount);
                    if (StringUtil.isNoEmpty(totalDiscount)) {
                        def.put(RuleInfoKey.geniusItem_totalDiscount, totalDiscount);
                    }
                    final String deptChineseName = RuleUtil.checkValue(platform, PlatformKey.deptChineseName);
                    if (StringUtil.isNoEmpty(deptChineseName)) {
                        def.put(PlatformKey.deptChineseName, deptChineseName);
                    }
                    final String deptJQChineseName = RuleUtil.checkValue(platform, PlatformKey.DEPT_JQ_CHINESE_NAME);
                    if (StringUtil.isNoEmpty(deptJQChineseName)) {
                        def.put(PlatformKey.DEPT_JQ_CHINESE_NAME, deptJQChineseName);
                    }
                    final String applicationExpectLossRatio = RuleUtil.checkValue(platform, PlatformKey.application_expectLossRatio);
                    if (StringUtil.isNoEmpty(applicationExpectLossRatio)) {
                        def.put(PlatformKey.application_expectLossRatio, applicationExpectLossRatio);
                    }
                    final String applicationExpectLossRatioTag = RuleUtil.checkValue(platform, PlatformKey.application_expectLossRatioTag);
                    if (StringUtil.isNoEmpty(applicationExpectLossRatioTag)) {
                        def.put(PlatformKey.application_expectLossRatioTag, applicationExpectLossRatioTag);
                    }
                    Object definition = platform.get("definition");
                    if (definition != null && definition instanceof Map) {
                        def.putAll((Map) definition);
                    }
                    platform.put("definition", def);
                    platform.put("task", task);
                    platform.put("taskId", autoTask.getTraceKey());
                    platform.put("monitorid", autoTask.getTempValues().get("monitorid"));


                    //需求 4048 回写平台信息的firstInsureType字段，公共方法逻辑的变更 BY WangJie
                    boolean firstInsureTypeFlag = org.springframework.util.StringUtils.isEmpty(temp.get(PlatformKey.bizContinuityInsureYears));
                    if (!firstInsureTypeFlag) {
                        if (Double.parseDouble(temp.get(PlatformKey.bizContinuityInsureYears).toString()) == 0) {
                            platform.put(PlatformKey.firstInsureType, checkNew(carInfo.getFirstRegDate()));
                        } else {
                            platform.put(PlatformKey.firstInsureType, "非首次投保");
                        }
                    } else if (Objects.nonNull(temp.get(PlatformKey.noClaimDiscountCoefficient)) && NumberUtil.isNumber(temp.get(PlatformKey.noClaimDiscountCoefficient).toString())) {
                        // 根据ncd判断投保类型
                        double noClaimDiscountCoefficient = Double.parseDouble(temp.get(PlatformKey.noClaimDiscountCoefficient).toString());
                        if (noClaimDiscountCoefficient != 1.0) {
                            platform.put("firstInsureType", "非首次投保");
                        }
                    }

                    fillCommercialClaimTimes(temp, platform);

                    //上年交强险理赔次数和交强险浮动原因
                    if (temp.containsKey(PlatformKey.compulsoryClaimRate)) {
                        double compulsoryClaimRate = 0.0;
                        if (StringUtil.isNoEmpty(temp.get(PlatformKey.compulsoryClaimRate).toString())) {
                            compulsoryClaimRate = Double.valueOf(temp.get(PlatformKey.compulsoryClaimRate).toString());
                        }
                        String compulsoryClaimRateReasons = getBwCompulsoryClaimTimes(compulsoryClaimRate);
                        if (StringUtil.isNoEmpty(compulsoryClaimRateReasons)) {
                            platform.put(PlatformKey.compulsoryClaimRateReasons, compulsoryClaimRateReasons);
                        }
                    }

                    if (!task.isEmpty() && !platform.isEmpty()) {
                        platform.put(PlatformKey.succeed, "success");
                    }
                    JSONObject jsonObject = new JSONObject(platform);
                    LOG.info("生成的平台信息为  " + jsonObject.toJSONString());

                } else {
                    LOG.error("平台信息为空");
                }

            } catch (Exception e) {
                LOG.error("任务：{}，初始化平台信息参数出错：{}", autoTask.getTempValues().get("enquiryId"), ExceptionUtils.getStackTrace(e));
            }

        } else {
            LOG.info("TempValues未获取到平台信息，请确认是否返回平台信息");
        }

    }

    private static String checkNew(Date firstRegDate) throws Exception {
        LocalDate regDate = LocalDateTime.ofInstant(firstRegDate.toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate now = LocalDate.now();
        long monthDiff = ChronoUnit.MONTHS.between(regDate, now);
        if (monthDiff > 9) {
            return "旧车首次投保";
        } else {
            return "新车首次投保";
        }
    }


    /**
     * 返回平台信息给cm
     *
     * @param autoTask
     * @return
     */
    public static boolean doBackPlatformInfo(AutoTask autoTask) {
        return doBackPlatformInfo(autoTask, false);
    }


    public static boolean doBackPlatformInfo(AutoTask autoTask, boolean notDoInitMethod) {

        if (StringUtils.isBlank(autoTask.getPlatFormSaveUrl())) {
            return false;
        }
        try {
            if (!notDoInitMethod) {
                initPlatformInfo(autoTask);
            }
            String url = autoTask.getPlatFormSaveUrl();
            Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
            if (Objects.nonNull(enquiry) && Objects.nonNull(enquiry.getOrder().getPlatformInfo())) {
                LOG.info("回写平台信息报文为：{}", JSONObject.toJSONString(enquiry.getOrder().getPlatformInfo()));
                String result = HttpSender.doPost(url, JSONObject.toJSONString(enquiry.getOrder().getPlatformInfo()));
                autoTask.getTempValues().put("isReservedRes", result);
                return true;
            }
        } catch (Exception e) {
            LOG.error("回写平台信息异常: {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    /**
     * 上年商业险理赔次数,需求2962
     *
     * @param claimTimes
     * @return
     */

    private static String getClaimTimes(int claimTimes) {
        return CLAIM_TIMES_MAP.get(claimTimes);
    }


    /**
     * 上年交强险理赔次数和交强险浮动原因
     *
     * @param compulsoryClaimRate
     * @return
     */
    private static String getBwCompulsoryClaimTimes(double compulsoryClaimRate) {
        String result = "";
        if (compulsoryClaimRate == 0.7) {
            result = "连续三年没有理赔";
        } else if (compulsoryClaimRate == 0.8) {
            result = "连续两年没有理赔";
        } else if (compulsoryClaimRate == 0.9) {
            result = "上年没有理赔";
        } else if (compulsoryClaimRate == 1.0) {
            result = "新保或上年发生一次有责任不涉及死亡理赔";
        } else if (compulsoryClaimRate == 1.1) {
            result = "上年有两次及以上理赔";
        } else if (compulsoryClaimRate == 1.3) {
            result = "上年有涉及死亡理赔";
        } else {
            result = "未匹配到理赔次数";
        }
        return result;
    }

    //设置 重复投保日期
    public static String setRepeatDate(String bizDate, String trDate) {
        StringBuffer repeatDate = new StringBuffer("");
        if (StringUtil.isNoEmpty(bizDate) && StringUtil.isNoEmpty(trDate)) {
            repeatDate.append("商业险终保日期：" + bizDate);
            repeatDate.append(" 交强险终保日期：" + trDate);

        } else {
            if (StringUtil.isNoEmpty(bizDate)) {
                repeatDate.append("商业险终保日期：" + bizDate);

            }
            if (StringUtil.isNoEmpty(trDate)) {
                repeatDate.append("交强险终保日期：" + trDate);
            }

        }
        return repeatDate.toString();
    }


    public static void callBackErrorMsg(AutoTask autoTask) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorcode", autoTask.getErrorInfo().get("errorcode"));
            jsonObject.put("errordesc", autoTask.getErrorInfo().get("errordesc"));
            jsonObject.put("taskId", autoTask.getTraceKey());
            String result = HttpSender.doPost(autoTask.getPlatFormSaveUrl() != null ? autoTask.getPlatFormSaveUrl() : "", jsonObject.toJSONString());
            autoTask.getTempValues().put("isReservedRes", result);
            LOG.info("重复投保回写平台信息报文为：" + jsonObject + "\nresult:" + result);
        } catch (Exception e) {
            LOG.info("重复投保平台信息回写失败");
        }
    }

    public static boolean doBackPlatformInfo(AutoTask autoTask, Map<String, Object> enquiry, Map<String, Object> tempValues) {
        Enquiry transform = TaskUtil.transform(JSON.toJSONString(enquiry), autoTask.getTaskType());
        transform.getOrder().setPlatformInfo((Map<String, Object>) tempValues.get("platformInfo"));
        transform.getOrder().setInsurePerson(JSON.parseObject(enquiry.get("insurePerson").toString(), InsurePerson.class));
        String insuredPersonListStr = enquiry.get("insuredPersonList").toString();
        List<InsurePerson> insuredPersonList = JSON.parseArray(insuredPersonListStr, InsurePerson.class);
        transform.getOrder().setInsuredPersons(insuredPersonList);
        autoTask.setTaskEntity(transform);
        autoTask.setTempValues(tempValues);

        boolean flag = doBackPlatformInfo(autoTask, false);
        tempValues.put("platformInfo", ((Enquiry) autoTask.getTaskEntity()).getOrder().getPlatformInfo());

        Map<String, Object> taskEntity = new HashMap<>();
        taskEntity.put("enquiry", enquiry);
        autoTask.setTaskEntity(taskEntity);
        return flag;
    }

    @SuppressWarnings("unchecked")
    private static void fillCommercialClaimTimes(Map platformInfos, Map platfromInfoMap) {
        Stream.of(RuleInfoKey.application_commercialClaimTimes, PlatformKey.bwCommercialClaimTimes)
            .map((String key) -> String.valueOf(platformInfos.getOrDefault(key, "")))
            .filter(StrUtil::isNotBlank)
            .findFirst()
            .ifPresent((String claimTimes) -> {
                if (NumberUtil.isNumber(claimTimes)) {
                    try {
                        int iCommercialClaimTimesClaimTimes = Double.valueOf(claimTimes).intValue();
                        String result = getClaimTimes(iCommercialClaimTimesClaimTimes);
                        platfromInfoMap.put(PlatformKey.bwCommercialClaimTimes, result);
                    } catch (Exception e) {
                        LOG.error("连续承保期间出险次数抓取非整数: {}", claimTimes);
                    }
                } else {
                    LOG.error("无法解析商业险理赔次数: {}", claimTimes);
                }
            });
    }

}
