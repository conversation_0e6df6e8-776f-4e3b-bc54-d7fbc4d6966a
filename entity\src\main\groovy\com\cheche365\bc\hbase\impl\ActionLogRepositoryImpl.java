package com.cheche365.bc.hbase.impl;

import com.cheche365.bc.entity.hbase.ActionLog;
import com.cheche365.bc.hbase.ActionLogRepository;
import com.cheche365.bc.hbase.BaseHBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ActionLog HBase服务实现类
 */
@Slf4j
@Repository
public class ActionLogRepositoryImpl extends BaseHBaseRepository<ActionLog> implements ActionLogRepository {

    @Override
    protected String getTableNameString() {
        return ActionLog.TABLE_NAME;
    }

    @Override
    protected Class<ActionLog> getEntityClass() {
        return ActionLog.class;
    }

    @Override
    protected Map<String, String> getColumnFamilyMapping() {
        Map<String, String> mapping = new HashMap<>();
        // 基本信息字段存储在info列族
        mapping.put("actionId", ActionLog.CF_INFO);
        mapping.put("autoTraceId", ActionLog.CF_INFO);
        mapping.put("actionName", ActionLog.CF_INFO);
        mapping.put("url", ActionLog.CF_INFO);
        mapping.put("sendTime", ActionLog.CF_INFO);
        mapping.put("receiveTime", ActionLog.CF_INFO);

        // 大文本字段存储在data列族
        mapping.put("requestBody", ActionLog.CF_DATA);
        mapping.put("responseBody", ActionLog.CF_DATA);
        mapping.put("inTaskBody", ActionLog.CF_DATA);
        mapping.put("outTaskBody", ActionLog.CF_DATA);

        // 异常信息存储在error列族
        mapping.put("exceptionInfo", ActionLog.CF_ERROR);

        return mapping;
    }

    @Override
    protected String generateRowKey(ActionLog entity) {
        return entity.generateRowKey();
    }

    @Override
    public List<ActionLog> findByAutoTraceId(String autoTraceId) throws IOException {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }

        List<ActionLog> results = new ArrayList<>();
        String scanPrefix = ActionLog.generateScanPrefix(autoTraceId);

        try (Table table = hbaseConnection.getTable(getTableName())) {
            Scan scan = new Scan();
            scan.withStartRow(Bytes.toBytes(scanPrefix));
            scan.withStopRow(Bytes.toBytes(scanPrefix + "~")); // 使用~作为结束符，确保扫描范围

            // 添加所有列族ime
            scan.addFamily(Bytes.toBytes(ActionLog.CF_INFO));
            scan.addFamily(Bytes.toBytes(ActionLog.CF_DATA));
            scan.addFamily(Bytes.toBytes(ActionLog.CF_ERROR));

            try (ResultScanner scanner = table.getScanner(scan)) {
                for (Result result : scanner) {
                    ActionLog actionLog = convertResultToEntity(result);
                    if (actionLog != null) {
                        results.add(actionLog);
                    }
                }
            }
        }

        // 按时间排序（最新的在前）
        results.sort((a, b) -> {
            if (a.getSendTime() == null && b.getSendTime() == null) return 0;
            if (a.getSendTime() == null) return 1;
            if (b.getSendTime() == null) return -1;
            return b.getSendTime().compareTo(a.getSendTime());
        });

        log.debug("根据autoTraceId: {} 查询到{}条ActionLog记录", autoTraceId, results.size());
        return results;
    }

    @Override
    public ActionLog findByAutoTraceIdAndActionId(String autoTraceId, String actionId) throws IOException {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }
        if (actionId == null || actionId.isEmpty()) {
            throw new IllegalArgumentException("actionId不能为空");
        }

        List<ActionLog> actionLogs = findByAutoTraceId(autoTraceId);
        return actionLogs.stream()
            .filter(log -> actionId.equals(log.getActionId()))
            .findFirst()
            .orElse(null);
    }

    @Override
    public void deleteByAutoTraceId(String autoTraceId) throws IOException {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }

        List<ActionLog> actionLogs = findByAutoTraceId(autoTraceId);
        if (actionLogs.isEmpty()) {
            return;
        }

        try (Table table = hbaseConnection.getTable(getTableName())) {
            List<Delete> deletes = new ArrayList<>();
            for (ActionLog actionLog : actionLogs) {
                String rowKey = actionLog.generateRowKey();
                deletes.add(new Delete(Bytes.toBytes(rowKey)));
            }

            table.delete(deletes);
            log.debug("删除autoTraceId: {} 的{}条ActionLog记录", autoTraceId, deletes.size());
        }
    }

    @Override
    public void batchSave(List<ActionLog> actionLogs) throws IOException {
        if (actionLogs == null || actionLogs.isEmpty()) {
            return;
        }

        try (Table table = hbaseConnection.getTable(getTableName())) {
            List<Put> puts = new ArrayList<>();
            Map<String, String> columnFamilyMapping = getColumnFamilyMapping();

            for (ActionLog actionLog : actionLogs) {
                if (actionLog == null) {
                    continue;
                }

                String rowKey = generateRowKey(actionLog);
                if (rowKey == null || rowKey.isEmpty()) {
                    log.warn("跳过RowKey为空的ActionLog实体");
                    continue;
                }

                Put put = new Put(Bytes.toBytes(rowKey));

                // 添加非空字段
                addColumnIfNotNull(put, columnFamilyMapping, "actionId", actionLog.getActionId());
                addColumnIfNotNull(put, columnFamilyMapping, "autoTraceId", actionLog.getAutoTraceId());
                addColumnIfNotNull(put, columnFamilyMapping, "actionName", actionLog.getActionName());
                addColumnIfNotNull(put, columnFamilyMapping, "url", actionLog.getUrl());
                addColumnIfNotNull(put, columnFamilyMapping, "sendTime", actionLog.getSendTime());
                addColumnIfNotNull(put, columnFamilyMapping, "receiveTime", actionLog.getReceiveTime());
                addColumnIfNotNull(put, columnFamilyMapping, "requestBody", actionLog.getRequestBody());
                addColumnIfNotNull(put, columnFamilyMapping, "responseBody", actionLog.getResponseBody());
                addColumnIfNotNull(put, columnFamilyMapping, "inTaskBody", actionLog.getInTaskBody());
                addColumnIfNotNull(put, columnFamilyMapping, "outTaskBody", actionLog.getOutTaskBody());
                addColumnIfNotNull(put, columnFamilyMapping, "exceptionInfo", actionLog.getExceptionInfo());

                if (!put.isEmpty()) {
                    puts.add(put);
                }
            }

            if (!puts.isEmpty()) {
                table.put(puts);
                log.debug("批量保存{}条ActionLog记录", puts.size());
            }
        }
    }

    @Override
    public ActionLog findLatestByAutoTraceId(String autoTraceId) throws IOException {
        List<ActionLog> actionLogs = findByAutoTraceIdWithLimit(autoTraceId, 1);
        return actionLogs.isEmpty() ? null : actionLogs.get(0);
    }

    @Override
    public List<ActionLog> findByAutoTraceIdWithLimit(String autoTraceId, int limit) throws IOException {
        if (autoTraceId == null || autoTraceId.isEmpty()) {
            throw new IllegalArgumentException("autoTraceId不能为空");
        }
        if (limit <= 0) {
            throw new IllegalArgumentException("limit必须大于0");
        }

        List<ActionLog> results = new ArrayList<>();
        String scanPrefix = ActionLog.generateScanPrefix(autoTraceId);

        try (Table table = hbaseConnection.getTable(getTableName())) {
            Scan scan = new Scan();
            scan.withStartRow(Bytes.toBytes(scanPrefix));
            scan.withStopRow(Bytes.toBytes(scanPrefix + "~"));
            scan.setReversed(true); // 倒序扫描，获取最新的记录

            // 添加所有列族
            scan.addFamily(Bytes.toBytes(ActionLog.CF_INFO));
            scan.addFamily(Bytes.toBytes(ActionLog.CF_DATA));
            scan.addFamily(Bytes.toBytes(ActionLog.CF_ERROR));

            try (ResultScanner scanner = table.getScanner(scan)) {
                int count = 0;
                for (Result result : scanner) {
                    if (count >= limit) {
                        break;
                    }
                    ActionLog actionLog = convertResultToEntity(result);
                    if (actionLog != null) {
                        results.add(actionLog);
                        count++;
                    }
                }
            }
        }

        log.debug("根据autoTraceId: {} 查询到{}条ActionLog记录(限制{}条)", autoTraceId, results.size(), limit);
        return results;
    }

    /**
     * 添加列到Put对象，如果值不为空
     */
    private void addColumnIfNotNull(Put put, Map<String, String> columnFamilyMapping,
                                    String fieldName, Object value) {
        if (value != null) {
            String columnFamily = columnFamilyMapping.get(fieldName);
            if (columnFamily != null) {
                byte[] valueBytes = convertToBytes(value);
                if (valueBytes != null && valueBytes.length > 0) {
                    put.addColumn(Bytes.toBytes(columnFamily), Bytes.toBytes(fieldName), valueBytes);
                }
            }
        }
    }
}
