server:
  port: ${profiles_port}
  undertow:
    io-threads: ${undertow_io_threads}
    worker-threads: ${undertow_worker_threads}

spring:
  data:
    mongodb:
      uri: ${mongo_uri}
    redis:
      host: ${redis_host}
      port: ${redis_port}
      password: ${redis_password}
      database: ${redis_database}

schedule:
  xxl:
    job:
      admin:
        addresses: ${xxl_job_admin_addresses}
      executor:
        ip: ${xxl_job_executor_ip}
        port: ${xxl_job_executor_port}

export:
  file-path: ${web_report_file}

ep:
  domain: ${ep_domain}
mysql_username: ${mysql_username}
mysql_password: ${mysql_password}
mysql_host: ${mysql_host}
mysql_port: ${mysql_port}
mysql_db: ${mysql_db}
HBASE_ZK_QUORUM: ${HBASE_ZK_QUORUM}
HBASE_ZK_ZONE_PARENT: ${HBASE_ZK_ZONE_PARENT}
HBASE_USERNAME: ${HBASE_USERNAME}
HBASE_PASSWORD: ${HBASE_PASSWORD}
