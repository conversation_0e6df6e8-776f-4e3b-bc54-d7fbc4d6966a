package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.RolePermissionService;
import com.cheche365.bc.entity.mysql.RolePermission;
import com.cheche365.bc.mapper.RolePermissionMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 角色权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-15
 */
@Service
@AllArgsConstructor
public class RolePermissionServiceImpl extends ServiceImpl<RolePermissionMapper, RolePermission> implements RolePermissionService {

    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public List<RolePermission> listRolePermissionByRoleId(Long roleId, Boolean isAdmin) {
        return rolePermissionMapper.listRolePermissionByRoleId(roleId, isAdmin);
    }
}
