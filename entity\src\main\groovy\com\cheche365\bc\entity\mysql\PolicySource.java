package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_policy")
public class PolicySource extends Model<PolicySource> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 保单类型1交强2商业3非车
     */
    @TableField("policy_type")
    private Integer policyType;

    /**
     * 资源id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 保司编号
     */
    @TableField("ins_id")
    private Integer insId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 压缩包资源id
     */
    @TableField("zip_source_id")
    private String zipSourceId;

    /**
     * 备用1
     */
    @TableField("reserved1")
    private String reserved1;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
