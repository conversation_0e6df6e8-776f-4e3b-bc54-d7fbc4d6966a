package com.cheche365.bc.tools;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class NonMotorPolicyUtil {

    private static final String  ACCIDENT_POLICY_CODE = "accidentPolicyCode";
    private static final String  ACCIDENT_PROPOSE_CODE = "accidentProposeCode";

    public static String getNonMotorPolicyOrProposeCode(JSONObject item) {
        return StringUtils.isNotBlank(item.getString(ACCIDENT_POLICY_CODE)) ? item.getString(ACCIDENT_POLICY_CODE) : item.getString(ACCIDENT_PROPOSE_CODE);
    }

    public static boolean containsPolicyOrProposeCode(JSONArray nonMotors) {
        if (CollectionUtil.isEmpty(nonMotors)) {
            return false;
        }

        return nonMotors.stream().anyMatch(item -> StringUtils.isNotBlank(((JSONObject) item).getString(ACCIDENT_POLICY_CODE)) || StringUtils.isNotBlank(((JSONObject) item).getString(ACCIDENT_PROPOSE_CODE)));
    }


    public static boolean checkNonMotorsContainsPolicyNo(JSONArray nonMotors, String policyNo) {
        if (CollectionUtil.isEmpty(nonMotors) ) {
            return false;
        }

        return nonMotors.stream().anyMatch(item -> StrUtil.reverse(policyNo).equals(getNonMotorPolicyOrProposeCode((JSONObject) item)));
    }
}
