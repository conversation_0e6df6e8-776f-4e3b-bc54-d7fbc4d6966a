package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_error_category")
public class ErrorCategory extends Model<ErrorCategory> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer code;

    @TableField("createTime")
    private LocalDateTime createTime;

    @TableField("isEffective")
    private Integer isEffective;

    private String keyword;

    private String name;

    @TableField("needStop")
    private Boolean needStop;

    private String remark;

    @TableField("errorTranslation")
    private String errorTranslation;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
