package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 百川业务异常信息配置
 *
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-12-06
 */
@Data
@TableName(value = "auto_task_exception_config")
public class AutoTaskExceptionConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 保险公司 ID
     */
    @TableField(value = "company_id")
    private String companyId;

    /**
     * 处理类型: robot; edi;
     */
    @TableField(value = "process_type")
    private String processType;

    /**
     * 任务类型
     */
    @TableField(value = "task_type")
    private String taskType;

    /**
     * 异常信息关键字
     */
    @TableField(value = "exception_keywords")
    private String exceptionKeywords;

    /**
     * 异常转换
     */
    @TableField(value = "exception_conversion")
    private String exceptionConversion;

    /**
     * 是否使用正则表达式：0: 否; 1: 是;
     */
    @TableField(value = "use_regex")
    private Integer useRegex;

    /**
     * 操作人账户名
     */
    @TableField(value = "operator_id")
    private Integer operatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
