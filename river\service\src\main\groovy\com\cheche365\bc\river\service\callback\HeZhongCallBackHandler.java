package com.cheche365.bc.river.service.callback;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.encrypt.SM4.Sm4Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class HeZhongCallBackHandler extends AbstractCallBackHandler {

    @Override
    public boolean needHandle(String companyId) {
        return String.valueOf(InsCompanyEnum.ULIC.getCode()).equals(companyId);
    }

    @Override
    public String handleCallBackBody(Map param) {
        String callbackBody = (String) param.get("callbackBody");
        Map dataSource = (Map) param.get("dataSource");
        try {
            Sm4Utils.secretKey = (String) DataUtil.get("config.secretKey", dataSource);
            return Sm4Utils.decryptDataEcb(callbackBody);
        } catch (Exception e) {
            log.error("合众回调解析保司报文异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    @Override
    public String handleResponseBody(Map param) {
        String responseContent = (String) param.get("responseContent");
        Map dataSource = (Map) param.get("dataSource");
        try {
            Sm4Utils.secretKey = (String) DataUtil.get("config.secretKey", dataSource);
            return Sm4Utils.encryptDataEcb(responseContent);
        } catch (Exception e) {
            log.error("合众回调响应保司报文渲染异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }
}
