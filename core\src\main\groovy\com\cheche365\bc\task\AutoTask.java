package com.cheche365.bc.task;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.cheche365.bc.constants.Constants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 需要EDI或者精灵处理的任务
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AutoTask extends com.cheche365.bc.entity.AutoTask {

    private String callBackUrl;
    /**
     * 取任务实体的url
     */
    private String requestUrl;

    private Object taskEntity;

    private boolean endFlag;
    /**
     * 当前步骤
     */
    private int currentStep;
    /**
     * 调用规则引擎的地址
     */
    private String ruleUrl;
    /**
     * 规则id
     */
    private String ruleId;

    /**
     * 回写平台信息的地址
     */
    private String platFormSaveUrl;
    /**
     * 主动回推url
     */
    private String writeBackUrl;
    /**
     * 请求来源
     */
    private RequestSource requestSource;
    /**
     * post对象的参数数组类型属性,使用时需自己初始化
     */
    private List arrayParams;

    private String taskTypeName;
    /**
     * 工具类脚本
     */
    private String commonScript;

    /**
     * 请求的头部信息
     */
    private Map<String, String> reqHeaders = Maps.newHashMap();
    /**
     * 请求的头部信息
     */
    private Map<String, String> repHeaders = Maps.newHashMap();
    /**
     * 临时存储传递使用的变量
     */
    private Map<String, Object> tempValues = Maps.newHashMap();
    /**
     * 错误信息
     */
    private final HashMap<String, Object> errorInfo = Maps.newHashMap();
    /**
     * 续保信息
     */
    private Map<String, Object> renewalState = Maps.newHashMap();
    /**
     * EDI,ROBOT,rule 几个报价引擎的顺序
     */
    private List<String> interfaces = Lists.newArrayList();
    /**
     * 配置的列表
     */
    private Map<String, Object> configs = Maps.newHashMap();
    /**
     * post对象的参数属性
     */
    private Map<String, Object> params = Maps.newHashMap();

    /**
     * 解析返回的数据对象
     */
    @JsonIgnore
    private Object backRoot;
    /**
     * 元数据
     */
    @JsonIgnore
    private MetaData metaData;
    /**
     * 需要保持httpclient时需要在各接口之间传递的httpclient对象
     */
    @JsonIgnore
    private Object httpClient;
    /**
     * 需要保持WebDriver对象
     */
    @JsonIgnore
    private Object driver;

    @Transient
    private boolean resultFlag;

    @Transient
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private String platformInfo = "";

    @Transient
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private String processType;

    @Transient
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private Integer insId;

    @Transient
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)

    private void initParams() {
        String taskType = getTaskType();
        if (StrUtil.isNotBlank(taskType)) {
            String[] params = taskType.split(StrPool.DASHED);
            if (params.length < 3) return;
            this.processType = params[0];
            this.insId = Integer.valueOf(params[1]);
            this.taskTypeName = params[2];
        }
    }

    @Transient
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public String getProcessType() {
        if (this.processType != null) return this.processType;
        initParams();
        return processType;
    }

    @Transient
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public Integer getInsId() {
        if (this.insId != null) return this.insId;
        initParams();
        return this.insId;
    }

    @Transient
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public String getTaskTypeName() {
        if (this.taskTypeName != null) return this.taskTypeName;
        initParams();
        return taskTypeName;
    }

    public String keepSessionKey() {
        return this.getTaskType() + "-" + getConfigs().get(Constants.WorkerId);
    }

    public void setConcatResultStr(String resultStr) {
        if (StrUtil.isBlank(this.getResultStr())) {
            super.setResultStr(resultStr);
        } else {
            if (!Strings.isNullOrEmpty(resultStr)) {
                super.setResultStr(this.getResultStr().concat("\n").concat(resultStr));
            }
        }
    }

    @Override
    public void setFailureCause(String failureCause) {
        if (!Strings.isNullOrEmpty(failureCause)) {
            if (failureCause.length() > 100) {
                super.setFailureCause(failureCause.substring(0, 100));
            } else {
                super.setFailureCause(failureCause);
            }
        }
    }

    @JsonIgnore
    @Transient
    @JSONField(serialize = false, deserialize = false)
    public String getTaskId() {
        return String.format("%s@%s", this.getTraceKey(), this.getCompanyId());
    }

    /**
     * 获取错误信息
     * 优先取errorInfo中的错误信息，其次取failureCause
     *
     * @return
     */
    @JsonIgnore
    @Transient
    @JSONField(serialize = false, deserialize = false)
    public String getErrorMsg() {
        if (Objects.nonNull(this.getErrorInfo()) && Objects.nonNull(this.getErrorInfo().get(ErrorInfoKeys.ERROR_DESC))) {
            return this.getErrorInfo().get(ErrorInfoKeys.ERROR_DESC).toString().trim();
        }
        return Strings.emptyToNull(this.getFailureCause());
    }

    @JsonIgnore
    @Transient
    @JSONField(serialize = false, deserialize = false)
    public String getIsReserved() {
        String isReserved = "";
        Object isReservedResObj = this.getTempValues().get("isReservedRes");
        if (Objects.nonNull(isReservedResObj)) {
            isReserved = isReservedResObj.toString();
        }
        return isReserved;
    }

    @Data
    public static class RequestSource {
        /**
         * 产品来源
         */
        private String sourceProduct;
        /**
         * 场景来源
         */
        private String sourceScenario;
        /**
         * 渠道来源
         */
        private String sourceChannel;
    }

    @Override
    public String toString() {
        return (taskEntity == null ? "" : (taskEntity + "-")) + this.getTaskType() + "-" + this.getAutoTraceId();
    }
}
