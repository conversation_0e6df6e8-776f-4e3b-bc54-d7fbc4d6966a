package com.cheche365.bc.admin.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.cheche365.bc.admin.service.dto.AutoTaskData;
import com.cheche365.bc.admin.service.dto.excel.BaseQuoteDto;
import com.cheche365.bc.admin.service.service.BatchTestTaskDetailService;
import com.cheche365.bc.admin.service.service.BatchTestTaskService;
import com.cheche365.bc.admin.service.service.InsAccountService;
import com.cheche365.bc.entity.mysql.BatchTestTask;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.List;


@Service
@AllArgsConstructor
public class InsAccountServiceImpl implements InsAccountService {

    private final BatchTestTaskService batchTestTaskService;
    private final BatchTestTaskDetailService batchTestTaskDetailService;

    @Override
    public List<AutoTaskData> exchange(byte[] excelFile) {
        List<BaseQuoteDto> excelData =
            EasyExcel.read(new ByteArrayInputStream(excelFile), BaseQuoteDto.class, null)
                .sheet()
                .headRowNumber(1)
                .doReadSync();
        return excelData.stream().map(AutoTaskData::create).toList();
    }

    @Override
    public BatchTestTask validation(List<AutoTaskData> autoTaskDataList) {

        BatchTestTask batchTestTask = batchTestTaskService.createBatchTestTask();

        batchTestTaskDetailService.saveBatchTestTaskDetails(autoTaskDataList, batchTestTask.getId());

        return batchTestTask;
    }
}
