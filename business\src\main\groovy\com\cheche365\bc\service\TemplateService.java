package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.entity.mysql.Template;
import com.cheche365.bc.task.AutoTask;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
public interface TemplateService extends IService<Template> {


    void loadTemplate(Interface itf, AutoTask t);

    List<Template> findTemplates(String taskType, Object autoTask);

    Template findTemplateByName(String name);
}
