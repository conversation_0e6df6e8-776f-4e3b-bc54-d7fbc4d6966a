package com.cheche365.bc.admin.service.constant;

import java.text.MessageFormat;

/**
 * author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date:2019/12/10 14:09
 */
public class UserLogConstants {

    /***角色日志***/
    public static final String ROLE_INFO_DISABLE = "{0}禁用了角色{1}";
    public static final String ROLE_INFO_ENABLE = "{0}启用了角色{1}";
    public static final String ROLE_INFO_DELETE = "{0}删除了角色{1}";


    /***用户日志***/
    public static final String USER_INFO_DISABLE = "{0}禁用了用户{1}, {2}";
    public static final String USER_INFO_ENABLE = "{0}启用了用户{1}, {2}";
    public static final String USER_INFO_DELETE = "{0}删除了用户{1}, {2}";
    public static final String USER_INFO_ADD = "{0}创建了用户{1},用户信息: {2}";
    public static final String USER_INFO_EDIT = "{0}编辑了用户{1}, 用户信息: {2}";

    /**
     * 获取用户操作日志
     *
     * @param pattern
     * @param params
     * @return
     */
    public static String getUserLogNote(String pattern, Object... params) {
        return MessageFormat.format(pattern, params);
    }
}
