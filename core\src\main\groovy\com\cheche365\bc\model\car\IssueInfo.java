package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;

/**
 * 出单信息
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "出单机构信息")
@Getter
@Setter
@XmlRootElement
public class IssueInfo {

    /**
     * 机构代码
     */
    @FieldDoc(des = "机构代码", need = true, remark = "")
    private String orgCode;
    /**
     * 机构名称
     */
    @FieldDoc(des = "机构名称", need = true, remark = "")
    private String orgName;
    /**
     * 地址
     */
    @FieldDoc(des = "机构地址", remark = "")
    private String address;
    /**
     * 电话
     */
    @FieldDoc(des = "机构联系电话", remark = "")
    private String phone;

}
