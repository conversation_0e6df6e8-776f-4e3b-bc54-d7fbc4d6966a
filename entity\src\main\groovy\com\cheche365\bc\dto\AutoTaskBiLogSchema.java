package com.cheche365.bc.dto;


import com.cheche365.bi.logger.BaseSchema;

import lombok.Getter;

import lombok.Setter;


import java.math.BigDecimal;


/**
 * <AUTHOR> href="<EMAIL>">王杨</a>
 * @description 百川每日数据统计 BI 日志传输实体
 * @since 2022-02-28
 */
@Setter
@Getter
public class AutoTaskBiLogSchema extends BaseSchema {

    /**
     * 单方 ID
     */
    private String traceKey;


    /**
     * 保险公司
     */
    private String companyId;


    /**
     * 能力类型
     */
    private String processType;


    /**
     * 任务类型
     */
    private String taskType;


    /**
     * 任务状态
     */
    private String taskStatus;


    /**
     * 原始异常
     */
    private String errorOriginal;


    /**
     * 异常类型
     * 1: 技术异常
     * 2: 非技术异常
     */
    private Integer errorType;


    /**
     * 转换异常
     */
    private String errorTransform;


    /**
     * 是否网销: 0: 否;
     1: 是;
     */
    private Integer internetSales;

    private String familyName;

    private String carBrandName;

    private String carModelName;

    private String syVehicleTypeCode;

    private String fuelType;

    private String jgVehicleType;

    private String useProps;

    private Integer carUserType;

    private String plateType;

    private String vin;

    private String plateNum;

    private String engineNum;

    private Boolean isNew;

    private Boolean isTransfer;

    private Boolean isNewEnergy;

    private Boolean isOutVehicles;

    private Float selfRate;

    private String province;

    private String city;

    private Integer carOwnerSex;

    private Integer carOwnerAge;

    private Integer applicantSex;

    private Integer applicantAge;

    private String isRenewal;

    private String sourceProduct;

    private String sourceScenario;

    private String sourceChannel;

    private BigDecimal bizCharge;

    private BigDecimal efcCharge;

    private BigDecimal nonMotorCharge;

    private BigDecimal totalCharge;

    private BigDecimal vehicleDamageCharge;

    private BigDecimal thirdPartyCharge;

    private BigDecimal driverCharge;

    private BigDecimal passengerCharge;

    private BigDecimal theftCharge;

    private BigDecimal wheelCharge;

    private BigDecimal extraDeviceCharge;

    private BigDecimal scratchCharge;

    private BigDecimal compensationDuringRepairCharge;

    private BigDecimal engineDamageExcludeCharge;

    private BigDecimal goodsOnVehicleCharge;

    private BigDecimal compensationForMentalDistressCharge;

    private BigDecimal cfmdThirdPartyCharge;

    private BigDecimal cfmdDriverCharge;

    private BigDecimal cfmdPassengerCharge;

    private BigDecimal holidayDoubleCharge;

    private BigDecimal nonInHealthCareCharge;

    private BigDecimal nihcThirdPartyCharge;

    private BigDecimal nihcDriverCharge;

    private BigDecimal nihcPassengerCharge;

    private BigDecimal roadsideServiceCharge;

    private BigDecimal vehicleInspectionCharge;

    private BigDecimal designatedDrivingCharge;

    private BigDecimal sendForInspectionCharge;

    private BigDecimal ancVehicleDamageCharge;

    private BigDecimal ancThirdPartyCharge;

    private BigDecimal ancDriverCharge;

    private BigDecimal ancPassengerCharge;

    private BigDecimal ancTheftCharge;

    private BigDecimal ancInGeneralCharge;

    private BigDecimal neGridBugDamageCharge;

    private BigDecimal neChargerDamageCharge;

    private BigDecimal neChargerDutyCharge;

    private BigDecimal neFireDoubleCharge;

    private BigDecimal neSoftwareDamageCharge;

    private BigDecimal svDamageExtCharge;

    private BigDecimal svEquipmentExtCharge;

    private BigDecimal vehicleDamageAmount;

    private BigDecimal thirdPartyAmount;

    private BigDecimal driverAmount;

    private BigDecimal passengerAmount;

    private BigDecimal theftAmount;

    private BigDecimal wheelAmount;

    private BigDecimal extraDeviceAmount;

    private BigDecimal scratchAmount;

    private BigDecimal compensationDuringRepairAmount;

    private BigDecimal engineDamageExcludeAmount;

    private BigDecimal goodsOnVehicleAmount;

    private BigDecimal compensationForMentalDistressAmount;

    private BigDecimal cfmdThirdPartyAmount;

    private BigDecimal cfmdDriverAmount;

    private BigDecimal cfmdPassengerAmount;

    private BigDecimal holidayDoubleAmount;

    private BigDecimal nonInHealthCareAmount;

    private BigDecimal nihcThirdPartyAmount;

    private BigDecimal nihcDriverAmount;

    private BigDecimal nihcPassengerAmount;

    private BigDecimal roadsideServiceAmount;

    private BigDecimal vehicleInspectionAmount;

    private BigDecimal designatedDrivingAmount;

    private BigDecimal sendForInspectionAmount;

    private BigDecimal ancVehicleDamageAmount;

    private BigDecimal ancThirdPartyAmount;

    private BigDecimal ancDriverAmount;

    private BigDecimal ancPassengerAmount;

    private BigDecimal ancTheftAmount;

    private BigDecimal ancInGeneralAmount;

    private BigDecimal neGridBugDamageAmount;

    private BigDecimal neChargerDamageAmount;

    private BigDecimal neChargerDutyAmount;

    private BigDecimal neFireDoubleAmount;

    private BigDecimal neSoftwareDamageAmount;

    private BigDecimal svDamageExtAmount;

    private BigDecimal svEquipmentExtAmount;


    @Override
    public Long getTime() {
        return this.eventEndTime - this.eventStartTime;

    }
}
