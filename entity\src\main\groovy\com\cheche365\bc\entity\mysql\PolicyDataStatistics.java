package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> zhangying
 * @date 2023-01-04
 * @descript :
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_data_statistics")
public class PolicyDataStatistics {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("com_id")
    private String comId;    //保险公司编号

    @TableField("company_abbr")
    private String companyAbbr;  //保险公司缩写

    @TableField("company_name")
    private String companyName;  //保险公司简称

    @TableField("month")
    private String month;        //月份  2022-01

    @TableField("count")
    private Integer count;        //数量

    @TableField("channel_type")
    private String channelType;    //渠道  抓单:grab 互联互通:interconnect

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("ext1")
    private String ext1;

    @TableField("ext2")
    private String ext2;
}
