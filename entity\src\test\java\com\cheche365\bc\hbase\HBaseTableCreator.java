package com.cheche365.bc.hbase;

import com.volcengine.hbase.security.AuthProviderSelector;
import com.volcengine.hbase.security.plain.internals.PlainSaslClientAuthenticationProvider;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.regionserver.BloomType;
import org.apache.hadoop.hbase.security.provider.SaslClientAuthenticationProviders;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.security.BytedanceUserProvider;

import java.io.IOException;

import static org.apache.hadoop.hbase.security.User.HBASE_SECURITY_CONF_KEY;

/**
 * HBase表创建工具类
 * 用于创建auto_task_log和action_log表
 * <p>
 * 环境切换说明：
 * 1. 修改USE_DEV_ENV常量来切换环境：
 * - true: 使用开发环境配置
 * - false: 使用测试环境配置
 * 2. 可以根据实际需求修改ZK_QUORUM_DEV和ZK_ZNODE_PARENT_DEV的值
 */
public class HBaseTableCreator {

    // HBase配置参数
    // 测试环境配置
    private static final String ZK_QUORUM_DEV = "49.4.81.237:2181";
    private static final String ZK_ZNODE_PARENT_DEV = "/hbase";

    // 开发环境配置
    private static final String ZK_QUORUM_TEST = "hb-cn013a5e6f81b28b-zk.config.cn-beijing.volces.com:2181"; // 需要替换为实际的开发环境ZK地址
    private static final String ZK_ZNODE_PARENT_TEST = "/hbase/hb-cn013a5e6f81b28b";

    private static final String USERNAME = "wangcg";
    private static final String PASSWORD = "jF#4*s#uZqZmka7mXtaT9TTF";
    private static final boolean ENABLE_AUTH = false; // 默认不启用认证

    // 环境切换开关: true为开发环境，false为测试环境
    private static final boolean USE_DEV_ENV = true;

    public static void main(String[] args) {
        Configuration conf = HBaseConfiguration.create();

        // 根据环境开关选择配置
        if (USE_DEV_ENV) {
            System.out.println("使用开发环境配置");
            conf.set(HConstants.ZOOKEEPER_QUORUM, ZK_QUORUM_DEV);
            conf.set(HConstants.ZOOKEEPER_ZNODE_PARENT, ZK_ZNODE_PARENT_DEV);
        } else {
            System.out.println("使用测试环境配置");
            conf.set(HConstants.ZOOKEEPER_QUORUM, ZK_QUORUM_TEST);
            conf.set(HConstants.ZOOKEEPER_ZNODE_PARENT, ZK_ZNODE_PARENT_TEST);
        }

        // 如果启用认证，则配置认证信息
        if (ENABLE_AUTH) {
            configureAuthentication(conf);
        }

        try (Connection connection = ConnectionFactory.createConnection(conf); Admin admin = connection.getAdmin()) {
            // 删除表
//            deleteTable(admin, "auto_task_log");
//            deleteTable(admin, "action_log");
            // 创建auto_task_log表
//            createAutoTaskLogTable(admin);
//            createAutoTaskLogTableTest(admin);

//            createAutoTaskLogTableDemo(admin);
//            String id = "84f670ad-f2d7-4bd1-9cc1-24bd7e71ee14";
//            int partition = Math.floorMod(id.hashCode(), 10);

            // 调用getData方法查询数据
//            getData(connection, "auto_task_log", partition + "_" + id);

            // 创建auto_task_sync表
            createAutoTaskSyncTable(admin);

            // 创建auto_task_sync_log表
            createAutoTaskSyncLogTable(admin);


//            System.out.println("HBase表操作完成！");

        } catch (IOException e) {
            System.err.println("创建HBase表时发生错误: " + e.getMessage());
            e.printStackTrace(System.err);
        }
    }

    private static void configureAuthentication(Configuration conf) {
        // 根据环境开关选择认证配置
        String zkZnodeParent = USE_DEV_ENV ? ZK_ZNODE_PARENT_DEV : ZK_ZNODE_PARENT_TEST;

        conf.set("zookeeper.znode.acl.parent", zkZnodeParent);
        conf.set("zookeeper.znode.metaserver", "public-meta-region-server");
        conf.set("zookeeper.znode.master", "public-master");

        conf.setStrings(SaslClientAuthenticationProviders.EXTRA_PROVIDERS_KEY, PlainSaslClientAuthenticationProvider.class.getName());
        conf.set(SaslClientAuthenticationProviders.SELECTOR_KEY, AuthProviderSelector.class.getName());
        conf.set("hbase.client.userprovider.class", BytedanceUserProvider.class.getName());
        conf.set(HBASE_SECURITY_CONF_KEY, BytedanceUserProvider.BYTE_DANCE_AUTHENTICATION);
        conf.set(BytedanceUserProvider.HBASE_CLIENT_USERNAME, USERNAME);
        conf.set(BytedanceUserProvider.HBASE_CLIENT_PASSWORD, PASSWORD);
    }

    /**
     * 创建auto_task_log表
     */
    private static void createAutoTaskLogTableTest(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("auto_task_log_test");

        if (admin.tableExists(tableName)) {
            System.out.println("表 auto_task_log 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.GZ);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(infoColumn);
        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.GZ);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(dataColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4"),
            Bytes.toBytes("5"),
            Bytes.toBytes("6"),
            Bytes.toBytes("7"),
            Bytes.toBytes("8"),
            Bytes.toBytes("9")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: auto_task_log");
    }

    /**
     * 删除HBase表
     */
    private static void deleteTable(Admin admin, String tableNameStr) throws IOException {
        TableName tableName = TableName.valueOf(tableNameStr);
        if (admin.tableExists(tableName)) {
            admin.disableTable(tableName);
            admin.deleteTable(tableName);
            System.out.println("成功删除表: " + tableNameStr);
        } else {
            System.out.println("表 " + tableNameStr + " 不存在，跳过删除");
        }
    }

    /**
     * 创建auto_task_log表
     */
    private static void createAutoTaskLogTable(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("auto_task_log");

        if (admin.tableExists(tableName)) {
            System.out.println("表 auto_task_log 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.GZ);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(infoColumn);
        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.GZ);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(dataColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4"),
            Bytes.toBytes("5"),
            Bytes.toBytes("6"),
            Bytes.toBytes("7"),
            Bytes.toBytes("8"),
            Bytes.toBytes("9")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: auto_task_log");
    }

    /**
     * 创建auto_task_log表
     */
    private static void createAutoTaskLogTableDemo(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("auto_task_log_demo");

        if (admin.tableExists(tableName)) {
            System.out.println("表 auto_task_log_demo 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.GZ);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(infoColumn);
        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.GZ);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(dataColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4"),
            Bytes.toBytes("5"),
            Bytes.toBytes("6"),
            Bytes.toBytes("7"),
            Bytes.toBytes("8"),
            Bytes.toBytes("9")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: auto_task_log_demo");
    }

    /**
     * 创建action_log表
     */
    private static void createActionLogTable(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("action_log");

        if (admin.tableExists(tableName)) {
            System.out.println("表 action_log 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.GZ);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(7776000); // TTL 7776000秒
        tableDescriptor.addFamily(infoColumn);

        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.GZ);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(7776000); // TTL 7776000秒
        tableDescriptor.addFamily(dataColumn);

        // 创建error列族
        HColumnDescriptor errorColumn = new HColumnDescriptor("error");
        errorColumn.setCompressionType(Compression.Algorithm.GZ);
        errorColumn.setBloomFilterType(BloomType.ROW);
        errorColumn.setTimeToLive(7776000); // TTL 7776000秒
        tableDescriptor.addFamily(errorColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4"),
            Bytes.toBytes("5"),
            Bytes.toBytes("6"),
            Bytes.toBytes("7"),
            Bytes.toBytes("8"),
            Bytes.toBytes("9")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: action_log");
    }

    /**
     * 创建auto_task_sync表
     */
    private static void createAutoTaskSyncTable(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("auto_task_sync");

        if (admin.tableExists(tableName)) {
            System.out.println("表 auto_task_sync 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.GZ);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(1296000); // TTL 1296000秒

        tableDescriptor.addFamily(infoColumn);

        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.GZ);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(1296000); // TTL 1296000秒

        tableDescriptor.addFamily(dataColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: auto_task_sync");
    }

    /**
     * 创建auto_task_sync_log表
     */
    private static void createAutoTaskSyncLogTable(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("auto_task_sync_log");

        if (admin.tableExists(tableName)) {
            System.out.println("表 auto_task_sync_log 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.GZ);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(1296000); // TTL 1296000秒

        tableDescriptor.addFamily(infoColumn);

        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.GZ);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(1296000); // TTL 1296000秒

        tableDescriptor.addFamily(dataColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: auto_task_sync_log");
    }

    /**
     * 根据rowKey查询HBase表数据
     */
    public static void getData(Connection connection, String tableNameStr, String rowKey) throws IOException {
        TableName tableName = TableName.valueOf(tableNameStr);
        try (Table table = connection.getTable(tableName)) {
            Get get = new Get(Bytes.toBytes(rowKey));
            Result result = table.get(get);
            if (result.isEmpty()) {
                System.out.println("RowKey: " + rowKey + " not found in table " + tableNameStr);
                return;
            }
            System.out.println("查询到 RowKey: " + rowKey + " 的数据如下：");
            for (Cell cell : result.listCells()) {
                String family = Bytes.toString(CellUtil.cloneFamily(cell));
                String qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
                String value = Bytes.toString(CellUtil.cloneValue(cell));
                long timestamp = cell.getTimestamp();
                System.out.println("  -> " + family + ":" + qualifier + " = " + value + " (timestamp=" + timestamp + ")");
            }
        }
    }
}
