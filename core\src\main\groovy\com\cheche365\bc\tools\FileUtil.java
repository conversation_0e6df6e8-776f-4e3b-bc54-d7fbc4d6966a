package com.cheche365.bc.tools;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.FileHeader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 文件工具
 *
 * <AUTHOR>
 * @Created by austinChen on 2015/12/19 14:17.
 */
@SuppressWarnings("unchecked")
@Slf4j
public final class FileUtil {

    /**
     * 解压缩
     */
    public static void deCompress(String sourceFile, String destDir) throws Exception {
        //保证文件夹路径最后是"/"或者"\"
        char lastChar = destDir.charAt(destDir.length() - 1);
        if (lastChar != '/' && lastChar != '\\') {
            destDir += File.separator;
        }
        //根据类型，进行相应的解压缩
        String type = sourceFile.substring(sourceFile.lastIndexOf(".") + 1);
        if ("zip".equals(type)) {
            //FileUtil.unzip(sourceFile, destDir);
        } else if ("rar".equals(type)) {
            // FileUtil.unrar(sourceFile, destDir);
        } else {
            throw new Exception("只支持zip和rar格式的压缩包！");
        }
    }

    /**
     * 递归创建多级文件夹
     *
     * @param file
     */
    public static void mkDir(File file) {
        if (file.getParentFile().exists()) {
            file.mkdir();
        } else {
            mkDir(file.getParentFile());
            file.mkdir();
        }
    }

    /**
     * @param dir 检查文件夹不存在则创建,可递归创建
     */
    public static void mkDir(String dir) {
        File file = new File(dir);
        mkDir(file);
    }

    public static String encodeBase64File(String path) throws Exception {

        FileInputStream inputFile = null;
        try {
            File file = new File(path);
            inputFile = new FileInputStream(file);
            byte[] buffer = new byte[(int) file.length()];
            inputFile.read(buffer);
            return Base64.getEncoder().encodeToString(buffer);
        } catch (Exception ex) {
            log.error("编码文件出现异常!", ex);
        } finally {
            if (inputFile != null) {
                inputFile.close();
            }

        }

        return "";

    }

    /**
     * 将base64字符解码保存文件
     *
     * @param base64Code
     * @param targetPath
     * @throws Exception
     */

    public static void decoderBase64File(String base64Code, String targetPath)
            throws Exception {
        FileOutputStream out = null;
        try {
            byte[] buffer = Base64.getDecoder().decode(base64Code);
            out = new FileOutputStream(targetPath);
            out.write(buffer);
        } catch (Exception ex) {
            log.error("解码文件出现异常!", ex);
        } finally {
            if (out != null) {
                out.close();
            }

        }

    }

    /**
     * @param filePath 文件路径
     * @param content  文件内容
     */
    public static void write(String filePath, String content) {
        write(filePath, content, "UTF-8");
    }

    /**
     * @param filePath 文件路径
     * @param content  文件内容
     * @param encoding 编码
     */
    public static void write(String filePath, String content, String encoding) {
        log.debug("写文件，路径{}", filePath);
        File tmp = new File(filePath);
        if (tmp.exists()) {
            tmp.delete();
        }
        try {
            FileOutputStream fos = new FileOutputStream(filePath);
            OutputStreamWriter osw = new OutputStreamWriter(fos, encoding);
            try {
                osw.write(content);
                osw.flush();
            } catch (Exception e) {
                log.error("写文件出错", e);
            } finally {
                if (osw != null) {
                    osw.close();
                }
                if (fos != null) {
                    fos.close();
                }
            }
        } catch (Exception ex) {
            log.error("写文件出现异常：", ex);
        }
    }

    /**
     * @param resName 资源路径
     * @param clazz   类名
     * @return 读取返回的字符串值
     */
    public static String readResource(String resName, Class clazz) {
        return readResource(resName, clazz, false);
    }

    /**
     * @param resName         资源路径
     * @param clazz           类名
     * @param allowChangeLine 是否保留换行符为\n
     * @return 读取返回的字符串值
     */
    public static String readResource(String resName, Class clazz, boolean allowChangeLine) {

        InputStream inputStream = null;
        BufferedReader br = null;
        try {
            inputStream = clazz.getResourceAsStream(resName);
            br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String s = "";
            StringBuffer sb = new StringBuffer();
            while ((s = br.readLine()) != null) {
                if (allowChangeLine) {
                    sb.append(s + "\n");
                } else {
                    sb.append(s);
                }
            }

            log.debug("读取类" + clazz + "下的路径" + resName + "的资源文件的配置文件结果为：{}", sb.toString());
            if (allowChangeLine) {
                return sb.toString().substring(0, sb.length() - 1);
            } else {
                return sb.toString();
            }
        } catch (Exception ex) {
            log.error("读取类" + clazz + "下的路径" + resName + "的资源文件出错", ex);
            return "";
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception ex) {
                log.error("读取类" + clazz + "下的路径" + resName + "的,关闭流出错", ex);
            }
        }

    }

    public static String read(String path) {
        return read(path, "UTF-8");
    }

    /**
     * 读取IO文件
     *
     * @param filePath
     * @return
     */
    public static String read(String filePath, String charSet) {
        try {
            String fileContent;
            final File file = new File(filePath);
            final Long size = file.length();
            byte[] buff = new byte[size.intValue()];
            final FileInputStream fs = new FileInputStream(file);
            fs.read(buff);
            fs.close();
            fileContent = new String(buff, charSet);
            return fileContent;
        } catch (Exception e) {
            log.error("文件" + filePath + "读取异常：", e);
            return "";
        }
    }

    public static byte[] getStreamAsByteArray(InputStream stream) throws IOException {
        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        int nextValue = stream.read();
        while (-1 != nextValue) {
            byteStream.write(nextValue);
            nextValue = stream.read();
        }
        return byteStream.toByteArray();
    }

    /**
     * 新建目录
     *
     * @param folderPath String 如 c:/fqf
     * @return boolean
     */
    public static void newFolder(String folderPath) {
        try {
            String filePath = folderPath;
            filePath = filePath.toString();
            File myFilePath = new File(filePath);
            if (!myFilePath.exists()) {
                myFilePath.mkdir();
            }
        } catch (Exception e) {
            log.error("新建目录操作出错", e);
        }
    }

    /**
     * 新建文件
     *
     * @param filePathAndName String 文件路径及名称 如c:/fqf.txt
     * @param fileContent     String 文件内容
     * @return boolean
     */
    public static void newFile(String filePathAndName, String fileContent) {

        try {
            String filePath = filePathAndName;
            filePath = filePath.toString();
            File myFilePath = new File(filePath);
            if (!myFilePath.exists()) {
                myFilePath.createNewFile();
            }
            FileWriter resultFile = new FileWriter(myFilePath);
            PrintWriter myFile = new PrintWriter(resultFile);
            String strContent = fileContent;
            myFile.println(strContent);
            resultFile.close();

        } catch (Exception e) {
            log.error("新建目录操作出错", e);
        }

    }

    /**
     * 删除文件
     *
     * @param filePathAndName String 文件路径及名称 如c:/fqf.txt
     * @return boolean
     */
    public static void delFile(String filePathAndName) {
        try {
            String filePath = filePathAndName;
            filePath = filePath.toString();
            File myDelFile = new File(filePath);
            myDelFile.delete();

        } catch (Exception e) {
            log.error("删除文件操作出错", e);
        }

    }

    /**
     * 删除文件夹
     *
     * @return boolean
     */
    public static void delFolder(String folderPath) {
        try {
            delAllFile(folderPath); //删除完里面所有内容
            String filePath = folderPath;
            filePath = filePath.toString();
            File myFilePath = new File(filePath);
            myFilePath.delete(); //删除空文件夹

        } catch (Exception e) {
            log.error("删除文件夹操作出错", e);
        }

    }

    /**
     * 删除文件夹里面的所有文件
     *
     * @param path String 文件夹路径 如 c:/fqf
     */
    public static void delAllFile(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return;
        }
        if (!file.isDirectory()) {
            return;
        }
        String[] tempList = file.list();
        File temp = null;
        for (int i = 0; i < tempList.length; i++) {
            if (path.endsWith(File.separator)) {
                temp = new File(path + tempList[i]);
            } else {
                temp = new File(path + File.separator + tempList[i]);
            }
            if (temp.isFile()) {
                temp.delete();
            }
            if (temp.isDirectory()) {
                delAllFile(path + "/" + tempList[i]);//先删除文件夹里面的文件
                delFolder(path + "/" + tempList[i]);//再删除空文件夹
            }
        }
    }

    /**
     * 复制单个文件
     *
     * @param oldPath String 原文件路径 如：c:/fqf.txt
     * @param newPath String 复制后路径 如：f:/fqf.txt
     * @return boolean
     */
    public static void copyFile(String oldPath, String newPath) {
        try {
            int bytesum = 0;
            int byteread = 0;
            File oldfile = new File(oldPath);
            if (oldfile.exists()) { //文件存在时
                InputStream inStream = new FileInputStream(oldPath); //读入原文件
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                int length;
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread; //字节数 文件大小
                    // System.out.println(bytesum);
                    fs.write(buffer, 0, byteread);
                }
                inStream.close();
            }
        } catch (Exception e) {
            log.error("复制单个文件操作出错", e);
        }

    }

    /**
     * 复制整个文件夹内容
     *
     * @param oldPath String 原文件路径 如：c:/fqf
     * @param newPath String 复制后路径 如：f:/fqf/ff
     * @return boolean
     */
    public static void copyFolder(String oldPath, String newPath) {

        try {
            (new File(newPath)).mkdirs(); //如果文件夹不存在 则建立新文件夹
            File a = new File(oldPath);
            String[] file = a.list();
            File temp = null;
            for (int i = 0; i < file.length; i++) {
                if (oldPath.endsWith(File.separator)) {
                    temp = new File(oldPath + file[i]);
                } else {
                    temp = new File(oldPath + File.separator + file[i]);
                }

                if (temp.isFile()) {
                    FileInputStream input = new FileInputStream(temp);
                    FileOutputStream output = new FileOutputStream(newPath + "/" +
                            (temp.getName()).toString());
                    byte[] b = new byte[1024 * 5];
                    int len;
                    while ((len = input.read(b)) != -1) {
                        output.write(b, 0, len);
                    }
                    output.flush();
                    output.close();
                    input.close();
                }
                if (temp.isDirectory()) {//如果是子文件夹
                    copyFolder(oldPath + "/" + file[i], newPath + "/" + file[i]);
                }
            }
        } catch (Exception e) {
            log.error("复制整个文件夹内容操作出错", e);
        }

    }

    /**
     * 移动文件到指定目录
     *
     * @param oldPath String 如：c:/fqf.txt
     * @param newPath String 如：d:/fqf.txt
     */
    public static void moveFile(String oldPath, String newPath) {
        copyFile(oldPath, newPath);
        delFile(oldPath);

    }

    /**
     * 移动文件到指定目录
     *
     * @param oldPath String 如：c:/fqf.txt
     * @param newPath String 如：d:/fqf.txt
     */
    public static void moveFolder(String oldPath, String newPath) {
        copyFolder(oldPath, newPath);
        delFolder(oldPath);

    }

    /**
     * 从类路径下获取文件内容
     *
     * @param file
     * @param charsetName
     * @return
     */
    public static String readFromStream(String file, String charsetName) {
        StringBuilder builder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(Objects.requireNonNull(FileUtil.class.getClassLoader().getResourceAsStream(file)), charsetName))) {
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
            return builder.toString();

        } catch (IOException e) {
            e.printStackTrace();
        }
        return builder.toString();
    }

    /**
     * 从类路径下获取文件内容
     *
     * @param fileName
     * @param charsetName
     * @return
     */
    public static String getResourceFromStream(String fileName, String charsetName) {
        try {
            log.info("load content from file ={}",fileName);
           InputStream input = FileUtil.class.getResourceAsStream(fileName);
            log.info("load stream is {}",input);
            return  IOUtils.toString(input , charsetName).trim();
        }catch (Exception e){
            throw new RuntimeException(e.getMessage(),e);
        }
    }

    /**
     *
     * 解压文件获取解压后文件字节数组
     */
    public static byte[] getStreamAsByteArray(byte[] bytes, String password, String fileNme) throws Exception {
        File tempFile = createTempFileFromByteArray(bytes, ".zip");
        try (ZipFile zf = new ZipFile(tempFile)) {
            if (zf.isEncrypted()) {
                if (StringUtils.isBlank(password))
                    return null;
                zf.setPassword(password.toCharArray());
            }
            List<FileHeader> fileHeaders = zf.getFileHeaders();
            String fileNamePrefix = fileNme.substring(0, fileNme.lastIndexOf("."));
            Optional<FileHeader> op = fileHeaders.stream().filter(fh -> fh.getFileName().contains(fileNamePrefix)).findFirst();
            if (op.isPresent()) {
                FileHeader fileHeader = op.get();
                try (InputStream inputStream = zf.getInputStream(fileHeader); ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                    byte[] buffer = new byte[16384];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        byteArrayOutputStream.write(buffer, 0, bytesRead);
                    }
                    return byteArrayOutputStream.toByteArray();
                } catch (Exception e) {
                    return null;
                }
            }
            return null;
        } finally {
           tempFile.delete();
        }
    }

    /**
     * 创建临时文件
     */
    private static File createTempFileFromByteArray(byte[] byteArray, String suffix) throws Exception {
        File tempFile = File.createTempFile("temp", suffix);
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(byteArray);
            return tempFile;
        }
    }
}
