package com.cheche365.bc.admin.rest.controller;

import com.cheche365.bc.model.RestResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工具类的controllers
 *
 * <AUTHOR>
 * Created by austinChen on 2017/9/18 14:51.
 */
@RequestMapping(value = "/tools")
@Slf4j
@RestController
@AllArgsConstructor
public class ToolsController {

    private final StringRedisTemplate redisTemplate;

    @GetMapping("deleteredis/{key}")
    public RestResponse deleteRedisVal(@PathVariable String key) {
        boolean isExistKey = redisTemplate.hasKey(key);
        if (!isExistKey) {
            return RestResponse.failedMessage("缓存中不存在所对应的key: " + key);
        }
        Boolean delete = redisTemplate.delete(key);
        return RestResponse.successMessage("删除key:【" + key + "】. status: " + delete);
    }

    @GetMapping("setredis/{key}/{value}")
    public RestResponse setRedisVal(@PathVariable String key, @PathVariable String value) {
        redisTemplate.opsForValue().set(key, value);
        return RestResponse.successMessage("成功设置key:【" + key + "】. value: " + value);
    }

}
