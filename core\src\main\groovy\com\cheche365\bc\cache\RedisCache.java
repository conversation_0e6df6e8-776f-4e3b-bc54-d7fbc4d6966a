package com.cheche365.bc.cache;

import com.cheche365.bc.config.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class RedisCache {
    private static RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        RedisCache.redisTemplate = redisTemplate;
    }

    public static RedisTemplate<String, Object> getRedis(){
        return RedisCache.redisTemplate;
    }

    public static StringRedisTemplate getStringRedis() {
        return Objects.nonNull(SpringUtil.getApplicationContext()) ? SpringUtil.getApplicationContext().getBean(StringRedisTemplate.class) : null;
    }
}
