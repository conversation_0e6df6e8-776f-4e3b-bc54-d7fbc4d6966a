FROM registry.cn-beijing.aliyuncs.com/cheche365/openjdk:17.0-buster-tools
WORKDIR /bc
COPY claim/rest/build/libs/*rest-*.jar /bc

C<PERSON> [ "/bin/bash", "-c", "java \
    --add-opens=java.base/java.lang=ALL-UNNAMED \
    --add-opens=java.base/java.math=ALL-UNNAMED \
    --add-opens=java.base/java.util=ALL-UNNAMED \
    --add-opens=java.base/java.util.concurrent=ALL-UNNAMED \
    --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED \
    --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED \
    --add-opens=java.base/java.net=ALL-UNNAMED \
    --add-opens=java.base/java.text=ALL-UNNAMED \
    --add-opens=java.base/jdk.internal.misc=ALL-UNNAMED \
    --add-opens=java.base/java.nio=ALL-UNNAMED \
    --add-opens=java.base/java.io=ALL-UNNAMED \
    --add-opens=java.base/java.lang.ref=ALL-UNNAMED \
    --add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
    --add-opens=java.base/java.time=ALL-UNNAMED \
    -Duser.timezone=GMT+08 \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/dumpmem/ \
    -XX:+PrintCommandLineFlags \
    -XX:+PrintGC \
    -javaagent:./skywalking-agent/skywalking-agent.jar=agent.service_name=claim,collector.backend_service=${skywalking_server_port} \
    -jar *claim-rest-*.jar"]
