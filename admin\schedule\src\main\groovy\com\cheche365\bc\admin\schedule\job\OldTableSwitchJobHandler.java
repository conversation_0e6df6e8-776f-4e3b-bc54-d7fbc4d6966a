package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.convert.Convert;
import com.cheche365.bc.sharding.OldTableSwitch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@JobHandler(value = "oldTableSwitchJobHandler")
@AllArgsConstructor
@Slf4j
public class OldTableSwitchJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        boolean flag = Convert.toBool(s);
        OldTableSwitch.setUseOldTable(flag);
        return ReturnT.SUCCESS;
    }
}
