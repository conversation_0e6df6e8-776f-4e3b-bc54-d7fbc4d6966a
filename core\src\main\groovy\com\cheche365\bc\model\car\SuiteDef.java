package com.cheche365.bc.model.car; /**
 *
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 险种配置
 *
 * <AUTHOR>
 *         created at 2015年6月15日 下午1:46:36
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark="险种定义")
@Getter
@Setter
@XmlRootElement
public class SuiteDef extends SuitePriceInfo implements Serializable {

    /**
     * 险种配置
     */
    private static final long serialVersionUID = -7606853479541143768L;

    /**险种代码*/
    @FieldDoc(des="险种代码",need = true)
    private String code;
    /**险种名称*/
    @FieldDoc(des="险种名称",need = true)
    private String name;
    /**保额描述*/
    @FieldDoc(des="保额描述",remark="部分公司会返回")
    private String amountDesc;
    /**保额*/
    @FieldDoc(des="保额",remark="报价后必有的字段")
    private BigDecimal amount;
    /**
     * 绝对免赔额 可自定义绝对免赔额
     */
    @FieldDoc(des="绝对免赔额",remark = "")
    private BigDecimal absoluteAmount;
    /**
     * 费率因子，保险中间平台公告的因子
     */
    @FieldDoc(des="费率因子",remark = "")
    private BigDecimal feeFactor;

    /**
     * 附加险是否共享主险保额
     */
    @FieldDoc(des="是否共享保额",remark = "")
    private boolean share;

    public SuiteDef() {
    }
    /**主险种的代码*/
    @FieldDoc(des="主险代码",remark = "")
    private String parentCode;
}
