package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

@Component
public class ChangJiangHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.CJCX.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        String bodyData = requestBody.substring(requestBody.indexOf("<Body>") + 6, requestBody.lastIndexOf("</Body>"));
        AesDesEncryption build = AesDesEncryption.builder()
                .key((String) autoTask.getConfigs().get("secretKey"))
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .ivKey((String) autoTask.getConfigs().get("secretKeyVi"))
                .ivKeyFormat(EncryptEnum.IvKeyFormatEnum.UTF_8)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_CBC_PKCS5Padding)
                .build();

        requestBody = requestBody.replace(bodyData, build.encrypt(bodyData));
        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        // 长江响应报文解密
        if (responseBody != null && !requestBody.isEmpty()) {
            responseBody = build.decrypt(responseBody.replaceAll("\n", ""));
        }
        return responseBody;
    }
}
