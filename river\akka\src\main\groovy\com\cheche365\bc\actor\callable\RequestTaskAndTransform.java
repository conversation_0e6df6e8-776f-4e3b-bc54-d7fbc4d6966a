package com.cheche365.bc.actor.callable;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.exception.TaskException;
import com.cheche365.bc.service.AutoRequestMetaDataService;
import com.cheche365.bc.service.TaskSyncService;
import com.cheche365.bc.service.TransformService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.ValidUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

import static cn.hutool.extra.spring.SpringUtil.getApplicationContext;
import static com.cheche365.bc.message.TaskType.CLUE;

/**
 * 获取任务数据并转换格式
 */
@Slf4j
public class RequestTaskAndTransform implements Callable<AutoTask> {

    private static final String NEED_VALID_CLASS_NAMES = "com.cheche365.bc.model.car.CarInfo";

    /**
     * 任务信息
     */
    private final AutoTask autoTask;

    /**
     * 任务ID
     */
    private final String taskId;

    private final TaskSyncService taskSyncService;

    private final TransformService transformService;

    public RequestTaskAndTransform(
        AutoTask autoTask,
        TransformService transformService,
        TaskSyncService taskSyncService
    ) {
        this.autoTask = autoTask;
        this.taskId = autoTask.getTempValues().get("enquiryId").toString();
        this.transformService = transformService;
        this.taskSyncService = taskSyncService;
    }

    @Override
    public AutoTask call() throws Exception {

        String remark = null;
        try {
            //获取数据
            String response = taskSyncService.requestData(autoTask);

            autoTask.setApplyJson(response);

            if (StrUtil.isBlank(response)) {
                String errorInfo = String.format("任务:%s申请任务数据失败,申请返回内容:%s", taskId, response);
                log.error(errorInfo);
                throw new Exception(errorInfo);
            }

            log.info("任务:{}申请任务数据成功,开始进行数据转换", taskId);

            JSONObject respJSONOBJ = JSONObject.parseObject(response);

            clueTypeSaveRequestMetaData(respJSONOBJ);
            //校验数据
            if ("autoinsure,quote,insure".contains(autoTask.getTaskTypeName())
                && !"true".equals(respJSONOBJ.getString("isrenewal"))) {
                remark = ValidUtil.validDataByClass(respJSONOBJ, NEED_VALID_CLASS_NAMES);
                if (StrUtil.isNotBlank(remark)) throw new RuntimeException(remark);
            }

            //数据转换
            transformService.transformRequestData(autoTask, respJSONOBJ);

            return autoTask;
        } catch (Exception e) {
            if (remark == null) remark = String.format("任务:%s申请任务数据失败，停止执行", taskId);
            log.error(remark, e);
            throw new TaskException(autoTask, remark, e);
        }
    }

    private void clueTypeSaveRequestMetaData(JSONObject respJSONOBJ) {
        if (autoTask.getTaskType().contains(CLUE.code)) {
            getApplicationContext().getBean(AutoRequestMetaDataService.class).saveAutoRequestMetaData(respJSONOBJ, autoTask);
        }
    }
}
