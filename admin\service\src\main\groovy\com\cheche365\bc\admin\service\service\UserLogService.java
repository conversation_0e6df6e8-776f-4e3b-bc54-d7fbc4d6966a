package com.cheche365.bc.admin.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.RoleInfo;
import com.cheche365.bc.entity.mysql.UserInfo;
import com.cheche365.bc.entity.mysql.UserLog;
import com.cheche365.bc.entity.enums.OperateContent;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-05
 */
public interface UserLogService extends IService<UserLog> {

    /**
     * 角色操作日志保存
     *
     * @param roleInfo
     * @param operateUser
     * @param operateContent
     * @param note
     */
    void saveRoleLog(RoleInfo roleInfo, UserInfo operateUser, OperateContent operateContent, String note);

    /**
     * 保存用户操作日志
     *
     * @param userInfo
     * @param operateUser
     * @param operateContent
     * @param note
     */
    void saveUserLog(UserInfo userInfo, UserInfo operateUser, OperateContent operateContent, String note);
}
