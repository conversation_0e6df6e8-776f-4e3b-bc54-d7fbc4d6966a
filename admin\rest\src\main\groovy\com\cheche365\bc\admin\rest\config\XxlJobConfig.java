package com.cheche365.bc.admin.rest.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "schedule.xxl.job.admin", name = "addresses")
public class XxlJobConfig {

    private final Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

    @Value("${schedule.xxl.job.admin.addresses}")
    private String adminAddresses;

    @Value("${schedule.xxl.job.executor.ip}")
    private String ip;

    @Value("${schedule.xxl.job.executor.port}")
    private int port;

    @Bean(initMethod = "start", destroyMethod = "destroy")
    public XxlJobSpringExecutor xxlJobExecutor() {
        logger.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);

        return xxlJobSpringExecutor;
    }

}
