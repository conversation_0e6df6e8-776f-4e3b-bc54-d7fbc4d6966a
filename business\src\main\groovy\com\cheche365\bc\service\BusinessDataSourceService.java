package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.BusinessDataSource;

import java.util.List;

/**
 * <AUTHOR> href="<EMAIL>">蒋昌宝</>
 */

public interface BusinessDataSourceService extends IService<BusinessDataSource> {

    /**
     * 根据 sourceProduct、sourceScenario、sourceChannel获取配置
     *
     * @param product  来源产品
     * @param scenario 来源场景
     * @param channel  来源渠道
     * @return 返回
     */
    BusinessDataSource getDataSource(String product, String scenario, String channel);

    /**
     * 根据 sourceProduct、sourceScenario 获取配置
     *
     * @param product  来源产品
     * @param scenario 来源场景
     * @return 返回
     */
    List<BusinessDataSource> getDataSourceWithProductAndScenario(String product, String scenario);

    /**
     * 根据 sourceProduct、sourceScenario
     *
     * @param product    来源产品
     * @param scenario   来源场景
     * @param channel    来源渠道
     * @param sourceKind 来源归类类别
     * @param desc       来源描述
     */
    void insertDataSource(String product, String scenario, String channel, int sourceKind, String desc);


}
