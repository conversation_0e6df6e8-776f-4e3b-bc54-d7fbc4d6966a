package com.cheche365.bc.admin.service.dto.base;

import com.cheche365.bc.tools.AreaUtil;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
public class AreaInfo {
    private String province;
    private String city;
    private String cityName;

    public static AreaInfo createByCityId(String cityId) {
        if (StringUtils.isBlank(cityId)) {
            return null;
        }
        return AreaInfo.builder()
            .city(cityId)
            .cityName(AreaUtil.getAreaName(cityId))
            .build();
    }
}
