package com.cheche365.bc.message;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;

/**
 * Created by Administrator on 2016/9/7.
 */
public enum TaskType {
    QUOTE("quote", "报价任务"),
    QUOTE_QUERY("quotequery", "报价查询"),
    AUTO_INSURE("autoinsure", "自核"),
    INSURE("insure", "核保任务"),
    INSURE_QUERY("insurequery", "核保查询"),
    INSURE_CALLBACK("insureCallback", "核保回调"),
    PAY("pay", "支付任务"),
    PAY_CALLBACK("payCallback", "支付回调"),
    APPROVED("approved", "承保任务"),
    APPROVED_QUERY("approvedquery", "承保查询"),
    APPROVED_CALLBACK("approvedCallback", "承保回调"),
    POLICY_SEARCH("policySearch", "续保查询任务"),
    CANCEL_INSURE("cancelInsure", "从核保通过撤回"),
    IDENTIFY_SEND("identifySend", "发送身份验证类验证码"),
    IDENTIFY_VERIFY("identifyVerify", "验证身份验证类验证码"),
    CANCEL_POLICY("cancelQuotationPolicy", "撤单"),
    POLICY_APPROVED("policyapproved", "保单回调"),
    POLICY_QUERY("policyquery", "保单查询"),
    SMS_FOR_PAY("getSmsForPay", "发短信"),
    CLUE_CALLBACK("clueCallback", "线索回调"),
    APPROVED_QUERY_BY_POLICY("approvedQueryByPolicy", "PDF识别"),
    CLUE("clue", "线索下发");

    public final String code;
    public final String desc;

    TaskType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return TaskType
     */
    static TaskType of(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        return Arrays.stream(TaskType.values()).filter(t -> t.code.equals(code)).findFirst().orElse(null);
    }

}
