# 数字资产（SDAS）交互文档

## 数字资产 基本信息

### 地址

- 生产地址：http://sdas.bedrock.chetimes.com/
- 准生产地址：http://sdas.i.bedrock.chetimes.com/
- 测试地址：http://sdas.h.bedrock.chetimes.com/

### 创建衍生数字资产组合（Json，多个）

这个API用于“占位”——为新的资产创建一个占位符，但是该位置上还没有实际内容。

assets*.contentType，资产的内容类型

assets*.name，资产名称

assets*.data，在此API中，该字段的含义是“资产位置”，格式参考Postman中的示例，此值由业务方自行保证唯一性

assets*.persistentConfig.sourceStorageType，源存贮类型，通常为3，即外部服务

assets*.persistentConfig.targetStorageType，目标存贮类型，通常为3，即外部服务

assets*.persistentConfig.sourceDataType，源数据类型，通常为0，即实体

assets*.persistentConfig.targetDataType，目标数据类型，通常为1，即引用

assets*.persistentConfig.obfuscatedName，是否混淆资产名称，通常为false，即使用指定资产名称且禁止混淆

此API的请求Header中包含几个关键字段：

X-CheChe365-NewAssetSource，“新源”标识，其值在本需求中必须为“derived;pre”，具体参考Postman示例，其含义为“衍生新源；前置阶段”

Authorization，认证字段，Java客户端可以使用cheche365-common-signature组件完成对请求的数字签名，在开发阶段，其值必须提供app_id，具体参考Postman示例

此API的请求URL中包含几个关键字段：

api_signature_ignored，是否忽略数字签名，此query param仅在测试环境有效，便于在实现数字签名功能前验证业务功能，具体参考Postman示例

此API的响应结果中包含几个关键字段：

payload.assets*.id，资产的唯一标识，需要业务方自行保存，以便后续获取资产

payload.assets*.location，是一个仅接受HTTP PUT method的临时授权访问URL，用于上传资产的实际内容

需要特别注意的是，用此URL发送PUT请求时，必须包含与“占位”时同样的“Content-Type”，比如占位时是“application/pdf”，那么上传时也必须是“application/pdf”。

而且，“Content-Type”需要与实际内容匹配，假设一张Jpeg图片被指定为“application/pdf”，那么后续业务方在浏览器上展示资产时可能会有问题。

用上述API返回的临时授权访问URL发送PUT请求上传实际内容、并得到成功响应之后，业务方便可认为资产已经归档成功，仅需将持有的资产id妥善处置即可。

### 秘钥

百川发送请求时计算签名所需的ak和sk，有两组，分别对应非生产和生产环境（千万别用错了）：

1、 非生产（测试、准生产）

App Key：17e209f5-7a8c-4ad9-8e90-2e8e4232a99f

Secret Key：abfff95c-4bcb-4f40-ade6-29bd113cf738

2、生产

App Key：48292168-c59c-4e3d-940a-79b8fbdd0d37

Secret Key：ce051956-a839-451d-ab9c-532735023d1b

## 电子保单访问SDAS流程

POST请求SDAS->PUT请求文件上传->GET请求获取资源

## SDAS签名 （准生产 生产 需要签名）

HTTP请求访问SDAS前需要签名，需要把关键信息放入进行签名详细见方法preRequestSign()。

该方法返回签名后的字符串  放入Authorization里面

## 第一步POST请求

注意asset里面的contentType 这个关系到上传到SDAS之后文件能不能打开

POST用什么格式去占位

PUT请求就继续上传什么类型文件

==注意此次请求的返回结果为201==

详细见方法 assetOccupancy（）

## 第二步PUT上传文件

PUT文件上传遇到二个坑

HttpEntity 要注意实现类

不能使用平常的存放字符串的StringEntity

文件上传要使用专门的文件类 InputStreamEntity、ByteArrayEntity、FileEntity等


尽量不要使用 InputStreamEntity 用这个类存放资源只可以读取一次

最终使用了ByteArrayEntity。

注意httpClient的创建方式  PUT请求是给华为云上传文件不经过SDAS

华为云使用了高版本额HTTP协议 咱们项目为了适配保险公司 使用的低版本的协议。使用项目默认的httpClient会有4中现象

- 443 Failed connection
- 管道破裂
- connection reset
- 成功

反复横跳

详细见方法 uploadPutAsset（）
## 第三步GET请求验证上传结果

GET请求是和SDAS交互的所以请求之前需要签名

GET请求目前只在测试用例中使用 为了是验证PUT上传的文件是否有问题。用于自测。

详细见getAsset（）

