package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cheche365.bc.entity.mysql.base.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@TableName("batch_test_task")
public class BatchTestTask extends BaseEntity {

    @TableField("batch_no")
    private String batchNo;

    @TableField("name")
    private String name;
}
