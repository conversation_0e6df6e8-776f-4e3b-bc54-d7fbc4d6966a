package com.cheche365.bc.service.impl;

import cn.hutool.core.date.DateUtil;
import com.cheche365.bc.service.ISerialNoService;
import com.cheche365.bc.utils.RuntimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Random;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;
import static java.util.concurrent.TimeUnit.DAYS;

/**
 * 订单号服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SerialNoService implements ISerialNoService {

    private static final String _BATCH_TEST_ENQUIRY_ID_KEY = "batch_test_enquiry_id_key";
    private static final String _PREFIX = RuntimeUtil.isProductionEnv() ? "BT" : "DT";
    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public String generateTestBatchNo() {
        return _PREFIX + generateSerialNo(_BATCH_TEST_ENQUIRY_ID_KEY);
    }

    private String generateSerialNo(String redisKey) {
        String date = DateUtil.format(new Date(), PURE_DATE_PATTERN);
        Long index = incrementIndex(redisKey + date);
        return date + String.format("%1$08d", index).replaceAll(",", "");
    }

    private Long incrementIndex(String redisKey) {
        Long randomNumber = new Random().nextLong(10) + 1;
        Long index = stringRedisTemplate.opsForValue().increment(redisKey, randomNumber);
        if (randomNumber.equals(index)) {
            stringRedisTemplate.expire(redisKey, 1, DAYS);
        }
        return index;
    }
}
