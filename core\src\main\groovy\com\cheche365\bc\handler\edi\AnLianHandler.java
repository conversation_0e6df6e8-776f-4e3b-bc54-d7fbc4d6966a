package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.Hex;
import com.cheche365.bc.utils.encrypt.MD5;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023-09-20
 */
@Component
@Slf4j
public class AnLianHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.ALIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        // 获取key的密码
        String password = (String) autoTask.getConfigs().get("aesSecret");
        // md5的密码
        String md5Password = (String) autoTask.getConfigs().get("md5Secret");
        // 获取header，需要往header里添加签名
        Map<String, String> reqHeaders = autoTask.getReqHeaders();
        // 获取加密用的key
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        random.setSeed(password.getBytes());
        kgen.init(128, random);
        SecretKey secretKey = kgen.generateKey();
        byte[] enCodeFormat = secretKey.getEncoded();
        String key = Base64.getEncoder().encodeToString(enCodeFormat);

        // 加密
        AesDesEncryption build = AesDesEncryption.builder()
                .key(key)
                .keyFormat(EncryptEnum.KeyFormatEnum.Base64)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES)
                .build();

        String aesSecret = build.encrypt(requestBody);
        aesSecret = Hex.encodeToString(Base64.getDecoder().decode(aesSecret.getBytes()));

        // 将加密好的密文放在header里
        reqHeaders.put("sign", MD5.toHex(aesSecret + md5Password));
        // 将加密好的密文转成base64
        String requestBase64Result = Base64.getEncoder().encodeToString(aesSecret.getBytes(StandardCharsets.UTF_8));

        // 创建请求结构体，主结构体包含一个head和一个body。head里为当前时间和uuid，body里为加密好的密文
        JSONObject head = new JSONObject();
        head.put("transactionNo", UUID.randomUUID());
        head.put("timeStamp", LocalDateTime.now());
        JSONObject requestJson = new JSONObject();
        requestJson.put("head", head);
        requestJson.put("body", requestBase64Result);

        // 发送请求
        String response = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestJson.toString(), autoTask.getParams(), reqHeaders, charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());

        JSONObject parse = null;
        try {
            parse = JSONObject.parseObject(response);
        } catch (Exception e) {
            log.error("安联Handler JSON转换失败:{}, {}", autoTask.getTraceKey(), ExceptionUtils.getStackTrace(e));
        }

        if (parse != null && parse.containsKey("result")) {
            String responseBase64Result = new String(Base64.getDecoder().decode(parse.getString("result").replaceAll("\n", "")));
            if ("edi-2049-policyDownload".equals(autoTask.getTempValues().get("curTemplateName"))) {
                // 电子保单下载步骤，返回的result只有一个链接，不用转换为JSONObject
                String pdfUrl = build.decrypt(Hex.decode(responseBase64Result));
                parse.put("result", pdfUrl);
            } else {
                JSONObject aesDecrypt = JSONObject.parseObject(build.decrypt(Hex.decode(responseBase64Result)));
                parse.put("result", aesDecrypt);
            }

            response = parse.toString();
        }

        return response;
    }
}
