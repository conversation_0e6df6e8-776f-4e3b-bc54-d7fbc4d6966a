package com.cheche365.bc.model;


import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;

/**
 * 证件类型枚举
 * Created by austinChen on 2015/9/18.
 */
@ClassDoc(remark="证件类型枚举")
public enum IdCardType {
    /**
     * 身份证
     */
    @FieldDoc(des="0身份证")
     NationalID,
    /**
     * 户口本
     */
    @FieldDoc(des="1户口本")
     LocalID ,
    /**
     * 驾照
     */
    @FieldDoc(des="2驾照")
     DrivingLicense,
    /**
     * 军官证/士兵证
     */
    @FieldDoc(des="3军官证/士兵证")
     MilitaryID ,
    /**
     * 护照
     */
    @FieldDoc(des="4护照")
     Passport,
    /**
     * 港澳回乡证/台胞证"
     */
    @FieldDoc(des="5港澳回乡证/台胞证")
     HomePassId ,
    /**
     * 组织代码证
     */
    @FieldDoc(des="6组织代码证")
     OrganizationCode,
    /**
     * 其他证件
     */
    @FieldDoc(des="7其他证件")
    Other,
    /**
     * 社会信用代码
     */
    @FieldDoc(des="8社会信用代码")
    SocialCredit,
    /**
     * 9税务登记证
     */
    @FieldDoc(des="9税务登记证")
    TaxRegistrationCertificate,
    /**
     * 10营业执照
     */
    @FieldDoc(des="10营业执照")
    BusinessLicense
}
