package com.cheche365.bc.admin.rest.security;

import com.cheche365.bc.entity.mysql.MenuInfo;
import com.cheche365.bc.entity.mysql.UserInfo;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;
import java.util.List;

@Setter
@Getter
@EqualsAndHashCode(callSuper = false)
public class ManageCommonSecurityUser extends User {
    private static final long serialVersionUID = 1L;

    private UserInfo userInfo;

    private List<MenuInfo> menuList;

    public ManageCommonSecurityUser(String username, String password, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
    }

    public ManageCommonSecurityUser(String username, String password, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
    }

    public ManageCommonSecurityUser(String username,
                                    String password,
                                    boolean enabled,
                                    boolean accountNonExpired,
                                    boolean credentialsNonExpired,
                                    boolean accountNonLocked,
                                    Collection<? extends GrantedAuthority> authorities,
                                    UserInfo userInfo,
                                    List<MenuInfo> menuList) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
        this.userInfo = userInfo;
        this.menuList = menuList;
    }
}

