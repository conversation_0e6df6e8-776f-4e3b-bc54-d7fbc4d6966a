#logging
logging:
  file:
    path: /data/nfs2/logs/river_service_qa

#api-interface
api:
  sign-val: 8f0e85d5-b128-652d-a371-dcf0f8e2091k

app:
  key: f87b8ba2
cjy:
  password: zzb2018#
  soft_id: 895193
  username: zhangzhongbao
cos:
  appID: ********
  bucketName: newzzbdev
  secretID: AKIDhvjglwX43Fokcrwf2Tcbxn0qrFP086xt
  secretKey: z09FxlCheYxirVlW90pgMG6QKnFrlwi1
ff:
  app_id: 303046
  app_key: j0KUUhetOuV4sskHG45C3YbbhTH4VAkc
  pd_id: 102846
  pd_key: vyzqgJu9iFV19yohAYeE5okbWhv2MGJG
pull:
  accounts:
    url: https://i.bedrock.chetimes.com
sdas:
  app:
    key: 17e209f5-7a8c-4ad9-8e90-2e8e4232a99f
  secret:
    key: abfff95c-4bcb-4f40-ade6-29bd113cf738
  url: http://sdas.h.bedrock.chetimes.com/api/assets
secret:
  key: 790e-43da-9078-af838d0fd3c0
system:
  akkaPath: /opt/deps/tomcat/webapps/ins-auto-service/WEB-INF/classes/akka.conf
  dataTransFormPath: /msgParseRule.json
  ip: *********
  trainsImgDir: /opt/deps/tomcat/webapps/ins-auto-service/dev1-5/
  validImgDir: /opt/deps/tomcat/webapps/ins-auto-service/dev1-5/

cookie-name: bc_qa
hbase:
  conf:
    enable-auth: true
