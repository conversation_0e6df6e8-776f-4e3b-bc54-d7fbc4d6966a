package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 商业险投保信息
 * Created by austinChen on 2015/10/9.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "商业险险种配置")
@Getter
@Setter
@XmlRootElement
public class BizSuiteInfo extends SuitePriceInfo implements Serializable {

    /**
     * 投保起始日期
     */
    @FieldDoc(des = "起保时间", remark = "报价前最好获取到")
    private String start;
    /**
     * 投保结束日期
     */
    @FieldDoc(des = "结束时间", remark = "报价前最好获取到")
    private String end;

    /**
     * 费率因子集合
     */
    private Map<String, BigDecimal> factors;
    /**
     * 险种配置信息
     */
    @FieldDoc(des = "险种配置信息", relatTypes = {SuiteDef.class})
    private Map<String, SuiteDef> suites;

}
