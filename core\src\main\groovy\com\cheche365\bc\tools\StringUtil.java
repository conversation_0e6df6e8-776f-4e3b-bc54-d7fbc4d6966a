package com.cheche365.bc.tools;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StringUtil {

    private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@(.+)$";

    public static boolean isEmpty(String str) {
        if (str == null || "".equals(str) || "null".equalsIgnoreCase(str)) {
            return true;
        }
        return false;
    }

    public static boolean isNoEmpty(String str) {
        return !isEmpty(str);
    }

    /*都不为空时返回true*/
    public static boolean areNotEmpty(String... str) {
        for (String val : str) {
            if (isEmpty(val)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param str     需要处理的字符串
     * @param matches 通过验证的正则表达式
     * @param isLink  是否获取连续的字符串
     * @return
     */
    public static String getString(String str, String matches, boolean isLink) {
        str = str.trim();
        int length = str.length();
        String result = "";
        boolean isLink_temp = false;
        for (int i = 0; i < length; i++) {
            String temp = str.substring(i, i + 1);
            if (temp.matches(matches)) {
                if (isLink) {
                    if (isLink_temp || "".equals(result)) {
                        result += temp;
                    } else {
                        break;
                    }
                } else {
                    result += temp;
                }
                isLink_temp = true;
            } else {
                isLink_temp = false;
            }
        }
        return result.trim();
    }

    /**
     * 日期(date)转化为字符串
     *
     * @param date   日期
     * @param format 转化格式
     * @return 转化后日期字符串
     */
    public static String parseDate(Date date, String format) {
        return Objects.nonNull(date) ? LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern(format)) : "";

    }

    //太平MD5
    public static String MD5(String sourceStr) {
        try {
            // 获得MD5摘要算法的 MessageDigest对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(sourceStr.getBytes());
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            StringBuffer buf = new StringBuffer();
            for (int i = 0; i < md.length; i++) {
                int tmp = md[i];
                if (tmp < 0) {
                    tmp += 256;
                }
                if (tmp < 16) {
                    buf.append("0");
                }
                buf.append(Integer.toHexString(tmp));
            }
            //return buf.toString().substring(8, 24);// 16位加密
            return buf.toString();// 32位加密
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String Unicode2String(String utfString) {
        StringBuilder sb = new StringBuilder();
        int i = -1;
        int pos = 0;

        while ((i = utfString.indexOf("\\u", pos)) != -1) {
            sb.append(utfString.substring(pos, i));
            if (i + 5 < utfString.length()) {
                pos = i + 6;
                sb.append((char) Integer.parseInt(utfString.substring(i + 2, i + 6), 16));
            }
        }
        sb.append(utfString.substring(pos));
        return sb.toString();
    }

    /**
     * 将中文转换成带\\u的格式
     *
     * @return
     */
    public static String chinesetoUnicode(String str) {
        StringBuffer sb = new StringBuffer();
        char[] charArr = str.toCharArray();
        for (char ch : charArr) {
            if (ch > 127) {
                sb.append("\\u" + Integer.toHexString(ch));
            } else {
                sb.append(ch);
            }
        }
        return sb.toString();
    }

    //true 为空 false 不为空
    public static boolean checkjsonValueEmpty(JSONObject json, String key) {
        boolean result = false;
        if (json == null || json.isEmpty()) {
            result = true;
        }

        if (!json.containsKey(key) || json.get(key) == null || "".equals(json.get(key))) {
            result = true;
        }

        return result;
    }

    //随机产生手机号码
    public static String getMobile() {
        String[] tel = "134,135,136,137,138,139,150,151,152,157,158,159,130,131,132,155,156,133,153".split(",");
        String first = tel[intNum(0, tel.length - 1)];
        String second = String.valueOf(intNum(1, 888) + 10000).substring(1);
        String third = String.valueOf(intNum(1, 9100) + 10000).substring(1);
        return first + second + third;
    }

    //产生指定范围内的随机数
    public static int intNum(int start, int end) {
        return (int) (Math.random() * (end - start));
    }

    /**
     * 按表达式regex匹配字符串
     */
    public static List<String> stringMatcher(String data, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(data);
        List<String> list = Lists.newArrayList();
        while (matcher.find()) {
            list.add(matcher.group());
        }
        return list;
    }

    /**
     * 删除报文中的换行、空格
     *
     * @param data 报文
     * @return 处理后的报文
     */
    public static String deleteSpace(String data) {
        return data.replaceAll("\n|\\s", "").trim();
    }

    /**
     * 将存在于字符串target中的list中的字符串片段逐一替换为replacement
     */
    public static String replaceString(String target, List<String> list, String replacement) {
        if (StringUtils.isNotBlank(target) && CollUtil.isNotEmpty(list)) {
            target = list
                .stream()
                .reduce(
                    target,
                    (str, toRep) -> str.replace(toRep, replacement)
                );
        }
        return target;
    }

    public static boolean isAlphanumeric(String str) {
        Pattern ALPHANUMERIC_PATTERN = Pattern.compile("^[a-zA-Z0-9]+$");
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return ALPHANUMERIC_PATTERN.matcher(str).matches();
    }

    public static boolean isValidEmail(String email) {
        return email != null && email.matches(EMAIL_REGEX);
    }
}
