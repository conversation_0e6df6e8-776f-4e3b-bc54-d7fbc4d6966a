package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.exception.InsReturnException;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.utils.encrypt.smUtils.SM2Util;
import com.cheche365.bc.utils.encrypt.smUtils.SM4Util;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpStatus;
import org.apache.http.impl.client.CloseableHttpClient;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Description:
 * @author: Jwar
 * @date: 2024.04.22
 */
@Component
@Slf4j
public class PingAnHandler implements BaseHandler {
    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.PAIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        //发票下载、预核保 sm2,sm4 加密
        Boolean needEncryptionSm = MapUtils.getBoolean(autoTask.getTempValues(), "needEncryptionSm", false);
        //车型查询接口 rsa加密
        Boolean needEncryption = MapUtils.getBoolean(autoTask.getTempValues(), "needEncryption", false);
        //事后上传接口 规范返回结果
        Boolean needReprocess = MapUtils.getBoolean(autoTask.getTempValues(), "needReprocess", false);
        //
        JSONObject envInfoObject = JSONObject.parseObject(JSONObject.toJSONString(autoTask.getTempValues().get("envInfo")));
        //处理请求
        if (needEncryptionSm) {
            requestBody = inCodeByEncryptEcb(requestBody, envInfoObject);
        }
        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getArrayParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        //处理响应
        if (needEncryptionSm) {
            responseBody = inCodeByDecryptEcb(responseBody, envInfoObject);
            if(StringUtils.isBlank(responseBody)) {
                return String.valueOf(HttpStatus.SC_INTERNAL_SERVER_ERROR);
            }
        }
        if (needEncryption) {
            String privateKey = envInfoObject.getString("privateKey");
            RSA rsa = RSA.builder()
                    .privateKey(privateKey)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                    .signAlgorithm(EncryptEnum.SignAlgorithmEnum.SHA256WithRSA)
                    .build();
            JSONObject responseBodyObject = JSONObject.parseObject(responseBody);
            if (Objects.nonNull(responseBodyObject.getJSONObject("data"))
                    && StringUtils.isNotBlank(responseBodyObject.getJSONObject("data").getString("models"))) {
                String models = rsa.decrypt(responseBodyObject.getJSONObject("data").getString("models"));
                JSONObject dataObject = responseBodyObject.getJSONObject("data");
                dataObject.put("models", JSONArray.parseArray(models));
                responseBodyObject.put("data", dataObject);
                responseBody = responseBodyObject.toJSONString();
            } else {
                throw new InsReturnException("2007查车失败！");
            }
        }
        try {
            if(needReprocess) {
                responseBody = responseBody.replace("\"{" , "{").replace("}\"", "}");
            }
        } catch (Exception e) {
            log.error("2007事后上传返回结果再处理失败：{}", ExceptionUtils.getStackTrace(e));
        }
        return responseBody;
    }

    private String inCodeByEncryptEcb(String requestBody, JSONObject envInfoObject) {
        String result = "";
        try {
            JSONObject param = new JSONObject();
            param.put("data", SM4Util.encryptEcb(envInfoObject.getString("sm4Key"), requestBody));
            param.put("sign", SM2Util.sign(envInfoObject.getString("sm2Pri"), Hex.toHexString(requestBody.getBytes())));
            result = param.toJSONString();
        } catch (Exception e) {
            log.error("平安sm加密失败");
        }
        return result;
    }

    private String inCodeByDecryptEcb(String responseBody, JSONObject envInfoObject) {
        String result = "";
        try {
            JSONObject responseBodyObject = JSONObject.parseObject(responseBody);
            //解密
            String data = SM4Util.decryptEcb(envInfoObject.getString("sm4Key"), responseBodyObject.getJSONObject("data").getString("data"));
            //验签
            Boolean verify = SM2Util.verify(envInfoObject.getString("sm2Pub"), Hex.toHexString(data.getBytes()), responseBodyObject.getJSONObject("data").getString("sign"));
            if (verify) {
                result = data;
            }
        } catch (Exception e) {
            log.error("平安sm解密失败");
        }
        return result;
    }
}

