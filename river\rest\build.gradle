dependencies {
    implementation project(':river:river-service')
    implementation project(':river:river-akka')

    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "com.alibaba:easyexcel:$alibaba_easyexcel_version"
    implementation 'io.micrometer:micrometer-registry-prometheus'
    runtimeOnly("org.springframework.boot:spring-boot-starter-undertow") {
        exclude group: "jakarta.servlet", module: "jakarta.servlet-api"
    }
}
tasks.register("bootRunRiver") {
    finalizedBy("bootRunDev")
}
