package com.cheche365.bc.service.impl.process;

import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.service.TaskProcessService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.AkkaTaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TaskProcessServiceImplByDefault implements TaskProcessService {

    @Override
    public boolean processTask(Interface itf, AutoTask autoTask) throws Exception {
        AkkaTaskUtil.process(itf, autoTask);
        return true;
    }

    @Override
    public boolean isProcessTask(AutoTask autoTask) {
        return true;
    }

    @Override
    public int order() {
        return Integer.MAX_VALUE;
    }
}
