package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSON;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.<PERSON>Handler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.Hex;
import com.cheche365.bc.utils.encrypt.MD5;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;

import java.util.Base64;

//@Component
public class HuaAnHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.HAIC.getCode();
    }


    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {

        AesDesEncryption aes = AesDesEncryption.builder()
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES)
                .key(MD5.toBase64((String) autoTask.getConfigs().get("secretKey")))
                .keyFormat(EncryptEnum.KeyFormatEnum.Base64)
                .build();
        String encryptedText = aes.encrypt(JSON.toJSONString(requestBody));
        requestBody = Hex.encodeToString(Base64.getDecoder().decode(encryptedText)).toUpperCase();

        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());

        if (StringUtils.isNotBlank(responseBody)) {
            responseBody = aes.decrypt(Hex.decode(responseBody));
        }
        return responseBody;
    }
}
