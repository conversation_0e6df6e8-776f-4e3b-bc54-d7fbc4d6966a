package com.cheche365.bc.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.cheche365.bc.service.TaskSyncService;
import com.cheche365.bc.task.AutoTask;
import com.github.rholder.retry.*;
import com.google.common.collect.Maps;
import com.google.common.net.HttpHeaders;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.ConnectTimeoutException;
import org.apache.http.HttpResponse;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskSyncServiceImpl implements TaskSyncService {

    /**
     * 默认请求头
     */
    private static final String DEFAULT_CONTENT_TYPE = "application/json;charset=UTF-8";

    /**
     * 默认字符集
     */
    private static final String DEFAULT_CHARSET = StandardCharsets.UTF_8.name();


    private final CloseableHttpClient httpClient;

    private Retryer<String> retryer;

    @PostConstruct
    public void init() {
        retryer = createRetryer();
    }

    /**
     * 获取任务原始数据
     *
     * @param autoTask 任务信息
     * @return 返回字符串类型原始数据
     * @throws Exception
     */
    @Override
    public String requestData(AutoTask autoTask) throws Exception {
        String requestUrl = autoTask.getRequestUrl();
        String body = buildRequestDataEntity(autoTask);
        return retryer.call(() -> doPost(requestUrl, body));
    }

    @Override
    public String callbackData(AutoTask autoTask) throws Exception {
        String callbackUrl = autoTask.getCallBackUrl();
        String body = autoTask.getFeedbackJson();
        return retryer.call(() -> doPost(callbackUrl, body));
    }

    private String doPost(String url, String body) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, DEFAULT_CONTENT_TYPE);
        httpPost.setEntity(new StringEntity(body, DEFAULT_CHARSET));
        return httpClient.execute(httpPost, this::responseHandler);
    }

    private String responseHandler(HttpResponse response) throws IOException {
        if (response.getStatusLine().getStatusCode() != HttpStatus.HTTP_OK) {
            throw new RuntimeException("请求失败,状态码:" + response.getStatusLine().getStatusCode());
        }
        return EntityUtils.toString(response.getEntity());
    }

    /**
     * 构建请求报文<br>
     * taskId 任务id<br>
     * processType edi/robot<br>
     * monitorid 监控id<br>
     * taskType 任务类型<br>
     *
     * @param autoTask 任务信息
     * @return 响应请求报文
     */
    private String buildRequestDataEntity(AutoTask autoTask) {
        Map<String, Object> requestEntity = Maps.newHashMapWithExpectedSize(4);

        //任务类型 quote/insure...
        String taskType = Optional.ofNullable(autoTask.getTaskType())
            .filter(StrUtil::isNotBlank)
            .map(type -> type.split("-"))
            .filter(parts -> parts.length > 2)
            .map(parts -> parts[2])
            .orElseThrow(() -> new IllegalArgumentException("Invalid taskType format"));

        var tempValues = autoTask.getTempValues();
        requestEntity.put("taskType", taskType);
        requestEntity.put("taskId", tempValues.get("enquiryId").toString());
        requestEntity.put("processType", tempValues.get("processType"));
        requestEntity.put("monitorid", tempValues.get("monitorid"));

        return JSON.toJSONString(requestEntity);
    }


    /**
     * 创建重试器<br>
     * <p>
     * 使用guava-retryer实现<br>
     * 1. 指定需要重试的异常<br>
     * 2. 指数退避策略<br>
     * 3. 最大重试次数<br>
     *
     * @return 重试器
     */
    private Retryer<String> createRetryer() {
        return RetryerBuilder.<String>newBuilder()
            // 指定需要重试的异常
            .retryIfExceptionOfType(UnknownHostException.class)
            .retryIfExceptionOfType(ConnectTimeoutException.class)
            .retryIfExceptionOfType(SocketException.class)
            .retryIfExceptionOfType(SocketTimeoutException.class)
            .retryIfExceptionOfType(NoHttpResponseException.class)
            // 指数退避策略
            .withWaitStrategy(WaitStrategies.exponentialWait(5, TimeUnit.SECONDS))
            // 最大重试次数
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            // 重试监听器
            .withRetryListener(new RetryListener() {
                @Override
                public <V> void onRetry(Attempt<V> attempt) {
                    if (attempt.hasException()) {
                        log.warn("第{}次重试失败: {}",
                            attempt.getAttemptNumber(),
                            attempt.getExceptionCause().getMessage());
                    }
                }
            })
            .build();
    }
}
