package com.cheche365.bc.entity.mysql;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.cheche365.bc.entity.enums.OperateContent;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bc_user_log")
public class UserLog extends Model<UserLog> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 操作内容
     */
    private OperateContent operateContent;

    /**
     * 操作人id
     */
    private Long operateUserId;

    /**
     * 操作人名称
     */
    private String operateUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 日志记录
     */
    private String note;

    /**
     * 更新操作记录
     */
    private String operateLog;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
