package com.cheche365.bc.exception;

/*
* 当前模板不需要执行，抛出此异常可以跳过
* */
public class TempSkipException extends BWException {

    /*完成任务最少需要走多少个模板*/
    private int minSteps;

    public TempSkipException() {
        super();
    }

    public TempSkipException(int minSteps, String message) {
        super(message);
        this.minSteps = minSteps;
    }

    public TempSkipException(String message) {
        super(message);
    }

    public TempSkipException(Throwable cause) {
        super(cause);
    }

    public TempSkipException(String message, Throwable cause) {
        super(message, cause);
    }

    protected TempSkipException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    @Override
    public Object getReturnValue() {
        return returnValue;
    }

    @Override
    public void setReturnValue(Object returnValue) {
        this.returnValue = returnValue;
    }

    public int getMinSteps() {
        return minSteps;
    }

    public void setMinSteps(int minSteps) {
        this.minSteps = minSteps;
    }
}
