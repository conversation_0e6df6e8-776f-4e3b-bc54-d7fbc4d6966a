package com.cheche365.bc.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.entity.mysql.PolicySource;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.PolicySourceService;
import com.cheche365.bc.service.TransformService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.NonMotorPolicyUtil;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sdas.SDASUtils;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.cheche365.bc.constants.Constants.NON_MOTORS;

@Service
@Slf4j
@RequiredArgsConstructor
public class TransformServiceImpl implements TransformService {

    private final InterfaceService interfaceSrv;

    private final PolicySourceService policySourceService;

    /**
     * @param autoTask
     * @param data
     * @throws Exception
     */
    @Override
    public void transformRequestData(AutoTask autoTask, JSONObject data) throws Exception {
        if (Objects.nonNull(data.get("sq"))) {
            policyNoMap(data);
        }

        //处理补充数据项
        if (!autoTask.getTaskType().contains("renewalQuery")) {
            DataUtil.supplyParamConversion(data);
        }
        //初始化车辆信息
        initCarInfo(autoTask, data);

        Interface itf = interfaceSrv.getInterface(autoTask, autoTask.getTaskType());
        Map<String, Object> configs = autoTask.getConfigs();
        //接口管理中使用任务配置开关控制
        if (itf.getUseTaskConfig()) {
            Map iftConfig = itf.loadConfig();
            if (iftConfig != null) configs.putAll(iftConfig);
        }
        if (Interface.STRUCTURE_TYPE_ENQ.equals(itf.getDataStructureType())) {
            autoTask.setTaskEntity(TaskUtil.transform(data.toJSONString(), autoTask.getTaskType()));
            if (DataUtil.containsKey(data, "configInfo.configMap")) {
                configs.putAll((Map<String, Object>) DataUtil.get("configInfo.configMap", data));
                autoTask.getConfigs().put(Constants.WorkerId, autoTask.getConfigs().getOrDefault("login", ""));
            }
        } else {
            Map datasource = Maps.newHashMap();
            datasource.put("enquiry", DataUtil.parseInEdiInterface(data.toJSONString()));
            autoTask.setTaskEntity(datasource);
            if (DataUtil.containsKey((Map) autoTask.getTaskEntity(), "enquiry.configInfo.configMap")) {
                configs.putAll((Map<String, Object>) DataUtil.get("enquiry.configInfo.configMap", autoTask.getTaskEntity()));
            }
        }

        Boolean isOnline = MapUtils.getBoolean(autoTask.getConfigs(), "isOnline", false);
        autoTask.setInternetSales(isOnline ? 1 : 0);
    }

    @Override
    public void transformResponseData(AutoTask task) throws Exception {
        Map<String, Object> callBackParam = new HashMap<>();
        String platformInfo = "";
        Map<String, Object> tempValues = task.getTempValues();
        if (task.getTaskEntity() instanceof Map) {
            log.info("taskEntity:{}", JSONObject.toJSONString(task.getTaskEntity()));
            callBackParam = AkkaTaskUtil.generateCallbackHttpEntity(task);
            Object platformInfoMap = tempValues.get("platformInfo");
            platformInfo = platformInfoMap == null ? "" : JSON.toJSONString(platformInfoMap);
        } else if (task.getTaskEntity() instanceof Enquiry) {
            callBackParam = TaskUtil.transformCallbackTask(task);
            var platformInfoMap = ((Enquiry) task.getTaskEntity()).getOrder().getPlatformInfo();
            if (MapUtil.isNotEmpty(platformInfoMap)) {
                platformInfo = JSON.toJSONString(platformInfoMap);
            }
        } else if (task.getTaskEntity() != null) {
            log.warn("回调任务中出现无法处理的数据类型:{}", task.getTaskEntity().getClass().getSimpleName());
        }

        if (callBackParam != null && !callBackParam.containsKey("businessId") && !callBackParam.containsKey("enquiryId")) {
            callBackParam.put("taskId", task.getTaskId());
            callBackParam.put("taskType", task.getTaskTypeName());
            callBackParam.put("msg", task.getResultStr());
            callBackParam.put("taskStatus", task.getTaskStatus());

            AkkaTaskUtil.mapTempValues(callBackParam, tempValues);
        }
        callBackParam.put("insureStatus", tempValues.get("insureStatus"));

        //保单下载特殊处理
        policyDownloadHandler(task, callBackParam);

        //承保查询特殊处理
        approvedQueryHandler(task, callBackParam);

        task.setPlatformInfo(platformInfo);
        task.setFeedbackJson(JSON.toJSONString(callBackParam));
    }

    /**
     * 保单号映射
     *
     * @param data
     */
    private static void policyNoMap(JSONObject data) {
        Map<String, Object> sq = (Map) data.get("sq");
        Map<String, Object> temp = Maps.newHashMap();
        sq.entrySet().stream()
            .filter(e -> (e.getKey().equals("efcPolicyCode") || e.getKey().equals("efcPolicyNo")))
            .findFirst()
            .ifPresent(x -> {
                temp.put("efcPolicyNo", x.getValue());
                temp.put("efcPolicyCode", x.getValue());
            });
        sq.entrySet().stream()
            .filter(e -> (e.getKey().equals("bizPolicyCode") || e.getKey().equals("bizPolicyNo")))
            .findFirst()
            .ifPresent(x -> {
                temp.put("bizPolicyNo", x.getValue());
                temp.put("bizPolicyCode", x.getValue());
            });
        if (!temp.isEmpty())
            sq.putAll(temp);
        data.put("sq", sq);
    }

    /**
     * 初始化车辆信息
     *
     * @param autoTask
     * @param data
     */
    private void initCarInfo(AutoTask autoTask, JSONObject data) {
        if (!data.containsKey("carInfo")) {
            return;
        }

        JSONObject carInfo = data.getJSONObject("carInfo");
        //初始化车牌号
        String plateNum = carInfo.getString("plateNum");
        if (StringUtils.isNotBlank(plateNum)) {
            autoTask.setPlateNo(plateNum);
        }
        //初始化车架号
        String vin = carInfo.getString("vin");
        if (StringUtils.isNotBlank(vin)) {
            autoTask.setVin(vin);
        }
    }


    private void policyDownloadHandler(AutoTask task, Map<String, Object> callBackParam) {
        if (task.getTaskType().contains("policyDownload") && TaskStatus.EXECUTE_SUCCESS.getState().equals(task.getTaskStatus())) {
            //如果是电子保单下载，将资源ID持久化到mysql
            policyDownload(callBackParam, task);
        }
    }


    private void policyDownload(Map<String, Object> callBackParam, AutoTask autoTask) {
        //如果是电子保单下载，将资源ID持久化到mysql
        if (!callBackParam.containsKey("sq") || Objects.isNull(callBackParam.get("sq"))) {
            return;
        }

        JSONObject jsonSQ = (JSONObject) callBackParam.get("sq");

        String efcId = jsonSQ.getString(Constants.POLICY_SOURCE_EFC);
        String bizId = jsonSQ.getString(Constants.POLICY_SOURCE_BIZ);
        String nonMotorId = jsonSQ.getString(Constants.POLICY_SOURCE_NON_MOTOR);
        int insId = autoTask.getInsId();
        Map<String, Object> tempValues = autoTask.getTempValues();

        boolean tempValueFlag = Objects.nonNull(tempValues);

        if (StringUtils.isBlank(efcId)
            && tempValueFlag
            && Objects.nonNull(tempValues.get(Constants.POLICY_SOURCE_EFC))) {
            efcId = tempValues.get(Constants.POLICY_SOURCE_EFC).toString();
            jsonSQ.put(Constants.POLICY_SOURCE_EFC, efcId);
        }

        if (StringUtils.isNotBlank(efcId)) {
            saveSourceSetUrl(jsonSQ, jsonSQ.getString("efcPolicyNo"), insId, 1, efcId);
        }

        if (StringUtils.isBlank(bizId)
            && tempValueFlag
            && Objects.nonNull(tempValues.get(Constants.POLICY_SOURCE_BIZ))) {
            bizId = tempValues.get(Constants.POLICY_SOURCE_BIZ).toString();
            jsonSQ.put(Constants.POLICY_SOURCE_BIZ, bizId);
        }

        if (StringUtils.isNotBlank(bizId)) {
            saveSourceSetUrl(jsonSQ, jsonSQ.getString("bizPolicyNo"), insId, 2, bizId);
        }

        // 非车电子保单下载用投保单号或保单号
        if (StringUtils.isBlank(nonMotorId)
            && tempValueFlag
            && Objects.nonNull(tempValues.get(Constants.POLICY_SOURCE_NON_MOTOR))) {
            nonMotorId = tempValues.get(Constants.POLICY_SOURCE_NON_MOTOR).toString();
            jsonSQ.put(Constants.POLICY_SOURCE_NON_MOTOR, nonMotorId);
        }

        if (StringUtils.isNotBlank(nonMotorId)) {
            String nonMotorPolicyNo = jsonSQ.getJSONObject("nonMotor").getString("accidentPolicyCode");
            if (StringUtils.isBlank(nonMotorPolicyNo))
                nonMotorPolicyNo = jsonSQ.getJSONObject("nonMotor").getString("accidentProposeCode");
            saveSourceSetUrl(jsonSQ, nonMotorPolicyNo, insId, 3, nonMotorId);
        }

        // 多个非车险
        nonMotorsSaveAssetId(jsonSQ, insId);

        autoTask.setTaskStatus(TaskStatus.EXECUTE_SUCCESS.getState());
        callBackParam.put("taskStatus", TaskStatus.EXECUTE_SUCCESS.getState());
    }

    private void saveSourceSetUrl(JSONObject sq, String policyNo, int insId, int type, String sourceId) {
        savePolicySource(policyNo, insId, type, sourceId);
        setUrlById(sq, type, sourceId);
    }

    private void savePolicySource(String policyNo, int insId, int type, String sourceId) {
        String reverseNo = StrUtil.reverse(policyNo);
        PolicySource source = new PolicySource();
        source.setCreateTime(LocalDateTime.now());
        source.setInsId(insId);
        source.setPolicyType(type);
        source.setPolicyNo(reverseNo);
        source.setSourceId(sourceId);
        policySourceService.savePolicySource(source);
    }

    private void setUrlById(JSONObject sq, int type, String sourceId) {
        String url = SDASUtils.getLocationById(sourceId);
        if (StringUtils.isNotBlank(url)) {
            log.info("根据资源id:{}申请连接到资源连接为:{}", sourceId, url);
            if (type == 1) {
                sq.put("efcSourceUrl", url);
            } else if (type == 2) {
                sq.put("bizSourceUrl", url);
            } else {
                sq.put("nonMotorSourceUrl", url);
            }
        }
    }

    private void approvedQueryHandler(AutoTask autoTask, Map<String, Object> callBackParam) {
        //承保查询，并且成功时次啊执行
        if (!autoTask.getTaskType().contains("approvedquery")
            || !TaskStatus.APPROVED_QUERY_SUCCESS.getState().equals(autoTask.getTaskStatus())) {
            return;
        }
        //写入生效时间
        if (Objects.nonNull(callBackParam.get("sq"))) {
            JSONObject jsonSQ = (JSONObject) callBackParam.get("sq");
            String policyEffectTime = jsonSQ.getString("policyEffectTime");
            if (StringUtils.isBlank(policyEffectTime)) {
                String curTime = DateUtil.now();
                jsonSQ.put("policyEffectTime", curTime);
            }
        }
    }

    private void nonMotorsSaveAssetId(JSONObject sq, int insId) {
        Optional.ofNullable(sq.getJSONArray(NON_MOTORS))
            .ifPresent(nonMotors -> {
                nonMotors
                    .forEach(item -> {
                        String assetId = ((JSONObject) item).getString("assetId");
                        String policyCode = NonMotorPolicyUtil.getNonMotorPolicyOrProposeCode((JSONObject) item);
                        if (StringUtils.isAnyBlank(assetId, policyCode)) {
                            return;
                        }
                        savePolicySource(policyCode, insId, 3, assetId);
                });
            });
    }
}
