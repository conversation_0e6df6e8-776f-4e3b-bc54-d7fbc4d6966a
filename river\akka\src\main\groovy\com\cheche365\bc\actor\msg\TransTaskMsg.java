package com.cheche365.bc.actor.msg;

import com.cheche365.bc.actor.LoginConfig;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.task.AutoTask;
import lombok.Getter;
import lombok.Setter;

/**
 * 传输的任务消息实体
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/4/17 17:23.
 */
@Getter
@Setter
public class TransTaskMsg {

    private AutoTask autoTask;
    private Interface itf;
    private String comId;
    private String comName;
    private LoginConfig loginConfig;
    private String account;
    private String pwd;


    public String getUniqueKey() {
        return autoTask.getTraceKey();
    }

}
