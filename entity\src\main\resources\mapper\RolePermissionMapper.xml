<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cheche365.bc.mapper.RolePermissionMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_id, permission_id
    </sql>

    <select id="listPermissionCodeByUserInfo" resultType="java.lang.String">
        select p.permission_code
        from bc_permission_info p,
        bc_role_permission rp,
        bc_role_info r,
        bc_user_role ur,
        bc_user_info u
        where
        p.id = rp.permission_id
        and rp.role_id = r.id
        and r.id = ur.role
        and ur.user = u.id
        and u.id = #{userId}
        group by p.permission_code
    </select>

    <select id="listRolePermissionByRoleId" resultType="com.cheche365.bc.entity.mysql.RolePermission">
        SELECT
        c.permission_code,
        d.menu_code
        FROM
        (
        SELECT
        b.id as permission_id,
        b.permission_code,
        b.menu_id
        FROM
        bc_permission_info b
        <if test="isAdmin !=true">
            INNER JOIN
            ( SELECT * FROM bc_role_permission WHERE role_id = #{roleId} ) a
            ON a.permission_id = b.id
        </if>
        ) c
        LEFT JOIN bc_menu_info d ON c.menu_id = d.id
    </select>

</mapper>
