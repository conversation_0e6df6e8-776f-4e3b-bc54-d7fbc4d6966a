package com.cheche365.bc.admin.service.dto.base;

import cn.hutool.core.util.StrUtil;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class NonAutoProduct implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    private String productCode;
    private Integer count;

    public static NonAutoProduct createNonAutoProduct(String productCode) {
        if (StrUtil.isBlank(productCode)){
            return null;
        }
        return NonAutoProduct.builder().productCode(productCode).count(1).build();
    }
}
