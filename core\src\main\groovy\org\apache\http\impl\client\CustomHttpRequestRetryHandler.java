package org.apache.http.impl.client;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.net.ConnectException;
import java.util.Arrays;
import java.util.Collection;

/**
 *
 */
public class CustomHttpRequestRetryHandler extends DefaultHttpRequestRetryHandler {

    public static final CustomHttpRequestRetryHandler INSTANCE = new CustomHttpRequestRetryHandler();

    public CustomHttpRequestRetryHandler(final int retryCount, final boolean requestSentRetryEnabled) {
        this(retryCount, requestSentRetryEnabled, Arrays.asList(
            ConnectException.class,
            SSLException.class));
    }

    public CustomHttpRequestRetryHandler() {
        this(3, false);
    }

    public CustomHttpRequestRetryHandler(int retryCount, boolean requestSentRetryEnabled, Collection<Class<? extends IOException>> clazzes) {
        super(retryCount, requestSentRetryEnabled, clazzes);
    }

}
