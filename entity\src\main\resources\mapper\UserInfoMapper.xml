<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cheche365.bc.mapper.UserInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, email, name,institution, password, status,source_type_ids,org_id,org_code,org_name,org_level,first_login,login_permission
    </sql>

    <select id="getUserInfoByEmail" parameterType="java.util.Map" resultType="com.cheche365.bc.entity.mysql.UserInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        bc_user_info
        WHERE 1=1
        AND status = #{status}
        AND email = #{email}
        limit 1
    </select>

    <select id="pageList" resultType="com.cheche365.bc.entity.mysql.UserInfo">
        select t1.*
        from (
        select *
        from bc_user_info
        <if test="ew != null">
            ${ew.customSqlSegment}
        </if>
        ) as t1
        left join bc_user_role t6
        on t1.id = t6.user
        group by t1.id
        order by t1.id desc
    </select>

    <select id="getUserInfoRefById" resultType="map">
        select
        group_concat(distinct (t6.role))        as roleIds
        from bc_user_info as t1
        left join bc_user_role t6
        on t1.id = t6.user
        where t1.id = #{id}
    </select>

</mapper>
