package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.mysql.BusinessDataSourceAutoTaskLog;
import com.cheche365.bc.mapper.BusinessDataSourceAutoTaskLogMapper;
import com.cheche365.bc.service.BusinessDataSourceAutoTaskLogService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.cheche365.bc.constants.RequestSourceConstants.*;

/**
 * 百川请求来源
 *
 * <AUTHOR> href="<EMAIL>">蒋昌宝</a>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessDataSourceAutoTaskLogServiceImpl extends ServiceImpl<BusinessDataSourceAutoTaskLogMapper, BusinessDataSourceAutoTaskLog> implements BusinessDataSourceAutoTaskLogService {

    private static final ConcurrentHashMap<String, BusinessDataSourceAutoTaskLog> CACHE = new ConcurrentHashMap<>();
    private static final Map<String, Integer> PRELOAD_CACHE = new HashMap<>();

    static {
        PRELOAD_CACHE.put(PRODUCT_OF_CBY, DEFAULT_BEDROCK);
        PRELOAD_CACHE.put(DEFAULT_PRODUCT, DEFAULT_BEDROCK);
        PRELOAD_CACHE.put(PRODUCT_OF_BEDROCK, DEFAULT_BEDROCK);
        PRELOAD_CACHE.put(PRODUCT_OF_CHE_ECO, DEFAULT_CHE_ECO);
        PRELOAD_CACHE.put(PRODUCT_PPB, DEFAULT_CHE_ECO);
    }

    @PostConstruct
    public void init() {
        PRELOAD_CACHE.forEach((name, parentId) -> {
            var data = getByNameAndParentId(name, parentId);
            if (data != null) {
                CACHE.put(buildCacheKey(name, parentId), data);
            }
        });
    }

    @Override
    public Integer getDataSourceLogId(String product, String scenario, String channel) {
        // 区分磐石还是车生态，获取 baseParentId 磐石id：1 车生态id：2
        int baseParentId = getBaseParentId(product);

        // 查询此 baseParentId 下是否有此 product。若没有则插入
        BusinessDataSourceAutoTaskLog idWithProduct = getByCache(product, baseParentId);

        //产品不存在
        if (Objects.isNull(idWithProduct)) {
            //存储产品
            int productId = insertRequestSource(product, baseParentId);
            //存储场景
            int scenarioId = insertRequestSource(scenario, productId);
            //存储渠道
            return insertWithChannel(scenarioId, channel);
        }

        //查询场景
        BusinessDataSourceAutoTaskLog idWithScenario = getByCache(scenario, idWithProduct.getId());
        //场景不存在
        if (Objects.isNull(idWithScenario)) {
            int scenarioId = insertRequestSource(scenario, idWithProduct.getId());
            return insertWithChannel(scenarioId, channel);
        }

        //传进去的渠道不存在时，返回场景id
        if (StringUtils.isBlank(channel)) {
            return idWithScenario.getId();
        }

        BusinessDataSourceAutoTaskLog idWithChannel = getByCache(channel, idWithScenario.getId());
        //渠道不存在
        if (Objects.isNull(idWithChannel)) {
            return insertRequestSource(channel, idWithScenario.getId());
        }

        return idWithChannel.getId();
    }

    @Override
    public List<Map<String, Object>> getTreeList() {
        QueryWrapper<BusinessDataSourceAutoTaskLog> wrapper = new QueryWrapper<>();
        wrapper.select("id", "name", "parent_id parentId");
        return listMaps(wrapper);
    }

    @Override
    public List<Integer> getIdListByParentId(Integer parentId) {
        List<Integer> resultList = new ArrayList<>();
        getIdListByParentId(parentId, resultList);
        return resultList;
    }

    private void getIdListByParentId(Integer parentId, List<Integer> resultList) {
        var wrapper = Wrappers.<BusinessDataSourceAutoTaskLog>lambdaQuery()
            .select(BusinessDataSourceAutoTaskLog::getId)
            .eq(BusinessDataSourceAutoTaskLog::getParentId, parentId);
        resultList.add(parentId);
        List<Integer> list = list(wrapper).stream().map(BusinessDataSourceAutoTaskLog::getId).toList();
        if (!list.isEmpty()) {
            for (Integer id : list) {
                getIdListByParentId(id, resultList);
            }
        }
    }

    /**
     * 根据名称和父级id查询
     *
     * @param name
     * @param parentId
     * @return
     */
    private BusinessDataSourceAutoTaskLog getByNameAndParentId(String name, Integer parentId) {
        var wrapper = Wrappers.<BusinessDataSourceAutoTaskLog>lambdaQuery()
            .select(BusinessDataSourceAutoTaskLog::getId)
            .eq(BusinessDataSourceAutoTaskLog::getName, name)
            .eq(BusinessDataSourceAutoTaskLog::getParentId, parentId);
        return getOne(wrapper, false);
    }

    private BusinessDataSourceAutoTaskLog getByCache(String name, Integer parentId) {
        String cacheKey = buildCacheKey(name, parentId);
        if (CACHE.containsKey(cacheKey)) {
            return CACHE.get(cacheKey);
        }
        BusinessDataSourceAutoTaskLog dataSourceAutoTaskLog = getByNameAndParentId(name, parentId);

        if (dataSourceAutoTaskLog != null) {
            CACHE.put(cacheKey, dataSourceAutoTaskLog);
        }
        return dataSourceAutoTaskLog;
    }

    private int insertRequestSource(String name, int parentId) {
        if (-1 == parentId) {
            return -1;
        }

        BusinessDataSourceAutoTaskLog businessDataSourceAutoTaskLog = new BusinessDataSourceAutoTaskLog();
        businessDataSourceAutoTaskLog.setName(name).setParentId(parentId);
        try {
            save(businessDataSourceAutoTaskLog);
        } catch (Exception e) {
            log.error("businessDataSourceAutoTaskLog新增log下拉框失败：{}", ExceptionUtils.getStackTrace(e));
            return -1;
        }

        return businessDataSourceAutoTaskLog.getId();
    }

    private int insertWithChannel(int scenarioId, String channel) {
        if (StringUtils.isBlank(channel)) {
            return scenarioId;
        } else {
            return insertRequestSource(channel, scenarioId);
        }
    }

    private static int getBaseParentId(String product) {
        return BEDROCK_PRODUCT_SET.contains(product) ? DEFAULT_BEDROCK : DEFAULT_CHE_ECO;
    }

    private static String buildCacheKey(String name, int parentId) {
        return String.format("%d:%s", parentId, name);
    }

}
