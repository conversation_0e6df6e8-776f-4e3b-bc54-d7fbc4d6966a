package com.cheche365.bc.service.impl.claim.base

import cn.hutool.core.lang.Assert
import cn.hutool.core.lang.UUID
import cn.hutool.json.JSONUtil
import com.cheche365.bc.entity.hbase.ActionLog
import com.cheche365.bc.entity.mysql.Interface
import com.cheche365.bc.enums.ClaimExceptionCodeEnum
import com.cheche365.bc.exception.FlowException
import com.cheche365.bc.handler.claim.BaseClaimHandler
import com.cheche365.bc.handler.claim.DefaultClaimHandler
import com.cheche365.bc.hbase.ActionLogRepository
import com.cheche365.bc.service.AutoTaskService
import com.cheche365.bc.service.InterfaceService
import com.cheche365.bc.service.TemplateService
import com.cheche365.bc.sharding.OldTableSwitch
import groovy.json.JsonGenerator
import groovy.transform.Memoized
import groovy.util.logging.Slf4j
import groovyx.net.http.RESTClient
import jakarta.annotation.Resource
import org.apache.commons.lang3.exception.ExceptionUtils
import org.springframework.data.mongodb.core.MongoTemplate

import java.nio.file.Path

@Slf4j
trait FlowRunner {

    @Resource
    GroovyScriptEngine engine
    @Resource
    List<BaseClaimHandler> insHandlers
    @Resource
    DefaultClaimHandler defaultClaimHandler
    @Resource
    TemplateService templateService
    @Resource
    InterfaceService interfaceService
    @Resource
    AutoTaskService taskService
    @Resource
    MongoTemplate mongoTemplate
    @Resource
    JsonGenerator jsonGenerator
    @Resource
    ActionLogRepository actionLogRepository

    /**
     * 获取要执行的flow
     * @param jdbcTemplate
     * @param taskType
     * @param companyId
     * @param processType
     * @param autoTraceId autoTask表中的id
     * @param businessData 具体的业务数据
     * @return flow包含step，每一个step包含请求地址，请求方式，编码
     */
    Map<String, Object> buildFlow(taskType, companyId, processType, autoTraceId, businessData) {
        Interface interfaceInfo = interfaceService.getInterfaceByTaskType((processType + '-' + companyId + '-' + taskType) as String)
        Assert.notNull(interfaceInfo, '理赔服务暂无能力处理该任务')

        def flow = interfaceInfo.term?.split(',')
        log.info("FlowRunner_buildFlow flow:{}", flow)
        flow = flow.collect { name ->
            def httpInfo = templateService.findTemplateByName(name)
            [
                'name'       : name,
                'path'       : Path.of(basePathTemplate(), interfaceInfo.comCode, name as String).toString(),
                'url'        : httpInfo?.proUrl,
                'method'     : httpInfo?.requestChannel,
                'autoTraceId': autoTraceId
            ]
        }
        [
            'context': [
                'client'      : new RESTClient(flow[0]?.url),
                'businessData': businessData,
                'config'      : interfaceInfo.loadConfig()
            ],
            'flow'   : flow
        ]
    }

    /**
     * 开始执行流程
     * @param flow
     * @param flowCopy 与 flow 相同，为了执行 AllowRepeatAll
     * @param context 执行过程中的上下文
     * @param allowCount 重试次数
     * @return result flow执行完成后，最终返回的数据
     */
    Map<String, Object> runFlow(flow, companyId, flowCopy, context, allowCount = 0, result = null) {

        if (!flow) {
            return result
        }

        Assert.isFalse(allowCount > 6, "调用保司次数超限")

        try {
            result = executeTemplate(flow[0], companyId, context)
            flow.remove(0)
            runFlow(flow, companyId, flowCopy, context, allowCount, result)
        } catch (FlowException fe) {
            log.error("flow error: {}", ExceptionUtils.getStackTrace(fe))
            if (fe.code == ClaimExceptionCodeEnum.REPEAT_ALL_TEMPLATE.getCode()) {
                flow.clear()
                flow = flowCopy.collect { item -> item }
                runFlow(flow, companyId, flowCopy, context, ++allowCount, result)
            }
            if (fe.code == ClaimExceptionCodeEnum.REPEAT_CURRENT_TEMPLATE.getCode()) {
                runFlow(flow, companyId, flowCopy, context, ++allowCount, result)
            }
            if (!ClaimExceptionCodeEnum.SECURITY_TEMPLATE_CODES.contains(fe.code)) {
                throw fe
            }
        }
    }

    /**
     * 执行模板，发送http请求,请求时对参数加解密处理
     * @param httpInfo
     */
    Object executeTemplate(step, companyId, context) {
        def req = null
        def actionLog = buildActionLog(step.autoTraceId, step.name, step.url, req, '', context, '', new Date(), null, '')
        String rowKey = actionLog.generateRowKey()
        saveStep(actionLog)
        try {
            req = engine.run("${step.path}_req.groovy", new Binding(['context': context]))
            actionLog.requestBody = req
            context.flowLog = context.flowLog + rowKey + '@' + step.name + ','
            context.response = (insHandlers.find { it.support(companyId as String) } ?: defaultClaimHandler).execute(
                context as Map<String, Object>,
                step as Map<String, Object>,
                req as String
            )
            actionLog.tap {
                responseBody = context.response
                outTaskBody = context ? JSONUtil.toJsonStr(context) : context
                receiveTime = new Date()
            }
            saveStep(actionLog)
            engine.run("${step.path}_resp.groovy", new Binding(['context': context]))
        } catch (Exception fe) {
            actionLog.exceptionInfo = fe.getMessage()
            log.error("executeTemplate error： {}", ExceptionUtils.getStackTrace(fe))
            saveStep(actionLog)
            throw fe
        }
    }

    @Memoized
    static String basePathTemplate() {
        return "com${File.separator}cheche365${File.separator}router${File.separator}"
    }


    /**
     * 动作运行中的每一步 存入到mongodb
     * @param step
     */
    ActionLog saveStep(ActionLog actionLog) {
        if (OldTableSwitch.isUseOldTable()) {
            actionLogRepository.save(actionLog)
            com.cheche365.bc.entity.mongo.ActionLog mongoActionLog = buildMongoActionLog(actionLog)
            mongoTemplate.save(mongoActionLog)
        } else {
            actionLogRepository.save(actionLog)
        }
        actionLog
    }

    ActionLog buildActionLog(
        autoTraceId,
        actionName, url, requestBody, responseBody,
        inTaskBody, context,
        sendTime, receiveTime,
        exceptionInfo
    ) {
        if (sendTime == null) {
            sendTime = new Date()
        }

        return new ActionLog().with {
            it.actionId = UUID.randomUUID().toString(true)
            it.autoTraceId = autoTraceId as String
            it.actionName = actionName as String
            it.url = url as String
            it.requestBody = requestBody as String
            it.responseBody = responseBody as String
            it.inTaskBody = jsonGenerator.toJson(inTaskBody)
            it.outTaskBody = context ? JSONUtil.toJsonStr(context) : context
            it.sendTime = sendTime as Date
            it.receiveTime = receiveTime as Date
            it.exceptionInfo = exceptionInfo as String
            it
        }
    }


    com.cheche365.bc.entity.mongo.ActionLog buildMongoActionLog(ActionLog actionLog) {
        return actionLog.with { h ->
            new com.cheche365.bc.entity.mongo.ActionLog().tap {
                _id = h.generateRowKey()
                autoTraceId = h.autoTraceId
                actionName = h.actionName
                url = h.url
                requestBody = h.requestBody
                responseBody = h.responseBody
                inTaskBody = h.inTaskBody
                outTaskBody = h.outTaskBody
                sendTime = h.sendTime
                receiveTime = h.receiveTime
                exceptionInfo = h.exceptionInfo
            }
        }
    }


}
