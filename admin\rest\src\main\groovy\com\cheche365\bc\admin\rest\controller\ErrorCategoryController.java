package com.cheche365.bc.admin.rest.controller;


import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.mysql.ErrorCategory;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.ErrorCategoryService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@RestController
@RequestMapping("/errorCategory")
public class ErrorCategoryController extends BaseController<ErrorCategoryService, ErrorCategory> {

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ERROR_CATEGORY + COMMA + PermissionCode.ERRORCATEGORY_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        return super.getPage(map, authentication);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ERROR_CATEGORY + COMMA + PermissionCode.ERRORCATEGORY_SAVE + SUFFIX)
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public RestResponse saveErrorCategory(@RequestBody ErrorCategory errorCategory ) {
        errorCategory.setCreateTime(LocalDateTime.now());
        if(service.saveOrUpdate(errorCategory)){
            return RestResponse.successMessage("创建操作成功！");
        } else {
            return RestResponse.failedMessage("创建操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ERROR_CATEGORY + COMMA + PermissionCode.ERRORCATEGORY_UPDATE + SUFFIX)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse updateErrorCategory(@RequestBody ErrorCategory errorCategory ) {
        if(service.saveOrUpdate(errorCategory)){
            return RestResponse.successMessage("更新操作成功！");
        } else {
            return RestResponse.failedMessage("更新操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.ERROR_CATEGORY + COMMA + PermissionCode.ERRORCATEGORY_DELETE + SUFFIX)
    @GetMapping("delete/{id}")
    public RestResponse delete(@PathVariable Long id ) {
        if(service.removeById(id)){
            return RestResponse.successMessage("删除操作成功！");
        } else {
            return RestResponse.failedMessage("删除操作失败！");
        }
    }
}

