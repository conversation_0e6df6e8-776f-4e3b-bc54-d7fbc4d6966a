package com.cheche365.bc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.mysql.BcPlatforminfoCode;
import com.cheche365.bc.mapper.BcPlatforminfoCodeMapper;
import com.cheche365.bc.service.BcPlatforminfoCodeService;
import jakarta.annotation.PostConstruct;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-02-23
 */
@Service
public class BcPlatforminfoCodeServiceImpl extends ServiceImpl<BcPlatforminfoCodeMapper, BcPlatforminfoCode> implements BcPlatforminfoCodeService {

    public static Map<String, String> cacheMap = new ConcurrentHashMap<>();

    private BcPlatforminfoCodeMapper mapper;

    @Autowired
    public void setMapper(BcPlatforminfoCodeMapper mapper) {
        this.mapper = mapper;
    }

    @PostConstruct
    void initCacheMap() {
        cacheMap.clear();
        List<BcPlatforminfoCode> list = this.list();
        list.forEach(item -> cacheMap.put(item.getCode(), item.getCnname()));
    }

    @Override
    public boolean exist(BcPlatforminfoCode code) {
        QueryWrapper<BcPlatforminfoCode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code.getCode());
        queryWrapper.eq("cnname", code.getCnname());

        List<BcPlatforminfoCode> list = mapper.selectList(queryWrapper);

        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public void updateCache(String message) {
        JSONObject jsonObject = JSONObject.parseObject(message);
        String type = jsonObject.getString("operate");
        JSONObject bcPlatforminfoCode = jsonObject.getJSONObject("bcPlatforminfoCode");
        switch (type) {
            case "add":
                cacheMap.put(bcPlatforminfoCode.getString("code"), bcPlatforminfoCode.getString("cnname"));
                break;
            case "update":
                this.initCacheMap();
                break;
            case "delete":
                cacheMap.remove(bcPlatforminfoCode.getString("code"));
                break;
        }
    }
}
