package com.cheche365.bc.admin.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.mysql.Template;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.TemplateService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@RestController
@RequestMapping("/template")
public class TemplateController extends BaseController<TemplateService, Template> {

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.TEMPLATE + COMMA + PermissionCode.TEMPLATE_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        Page<Template> page = createPage(map);
        // 获取并设置检索条件
        QueryWrapper<Template> wrapper = new QueryWrapper<>();
        wrapper.select("id,charSet,createTime,name,proUrl,remark,requestChannel,state,templateCategory,templateEngine,testUrl,uatUrl,updateTime,version").eq("templateCategory","template");
        wrapper = getQueryByWrapper(map, "", wrapper);
        service.page(page, wrapper);
        return RestResponse.success(page);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.TEMPLATE + COMMA + PermissionCode.TEMPLATE_SAVE + SUFFIX)
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public RestResponse saveTemplate(@RequestBody Template template) {
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());
        template.setTemplateCategory("template");
        template.setTemplateEngine("groovy");
        if(service.saveOrUpdate(template)){
            return RestResponse.successMessage("创建操作成功！");
        } else {
            return RestResponse.failedMessage("创建操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.TEMPLATE + COMMA + PermissionCode.TEMPLATE_UPDATE + SUFFIX)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse updateTemplate(@RequestBody Template template) {
        template.setUpdateTime(LocalDateTime.now());
        if(service.saveOrUpdate(template)){
            return RestResponse.successMessage("更新操作成功！");
        } else {
            return RestResponse.failedMessage("更新操作失败！");
        }
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.TEMPLATE + COMMA + PermissionCode.TEMPLATE_DELETE + SUFFIX)
    @GetMapping("delete/{id}")
    public RestResponse delete(@PathVariable Long id) {
        if(service.removeById(id)){
            return RestResponse.successMessage("删除操作成功！");
        } else {
            return RestResponse.failedMessage("删除操作失败！");
        }
    }
}

