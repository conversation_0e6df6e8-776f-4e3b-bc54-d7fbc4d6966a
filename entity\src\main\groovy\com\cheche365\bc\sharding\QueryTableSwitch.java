package com.cheche365.bc.sharding;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 查询表开关控制类
 * 用于控制查询操作使用哪个表，独立于写入操作的双写控制
 *
 * 开关逻辑：
 * - true: 查询使用旧表 (auto_task)
 * - false: 查询使用新表 (auto_task_yyyyMM) + HBase
 *
 * <AUTHOR>
 */
@Slf4j
public class QueryTableSwitch {

    /**
     * 查询表开关标识
     * true: 查询使用旧表
     * false: 查询使用新表+HBase
     */
    private static final AtomicBoolean USE_OLD_TABLE_FOR_QUERY = new AtomicBoolean(true);
    private static volatile boolean initialized = false;
    private static final Object initLock = new Object();

    /**
     * Redis缓存键
     */
    public static final String CACHE_KEY = "bc:queryTableSwitch";

    /**
     * 定时任务调度器，用于定期从Redis同步开关状态
     */
    private static volatile ScheduledExecutorService scheduler;

    // 移除static块，改为延迟初始化

    /**
     * 延迟初始化方法
     * 确保在Spring容器完全启动后再初始化
     */
    private static void ensureInitialized() {
        if (!initialized) {
            synchronized (initLock) {
                if (!initialized) {
                    try {
                        init();
                        initialized = true;
                        log.info("QueryTableSwitch 初始化完成");
                    } catch (Exception e) {
                        log.warn("QueryTableSwitch 初始化失败，将使用默认值: {}", e.getMessage());
                        // 初始化失败时使用默认值，不抛出异常
                    }
                }
            }
        }
    }

    /**
     * 实际的初始化逻辑
     */
    private static void init() {
        boolean value = getQueryTableSwitchSafely();
        USE_OLD_TABLE_FOR_QUERY.set(value);
        initScheduler();
    }

    /**
     * 初始化定时任务调度器
     */
    private static void initScheduler() {
        if (scheduler == null) {
            scheduler = Executors.newSingleThreadScheduledExecutor(ThreadFactoryBuilder
                .create()
                .setNamePrefix("QueryTableSwitch-scheduler-")
                .setDaemon(true)
                .setUncaughtExceptionHandler((t, e) -> log.error("Failed to execute QueryTableSwitch scheduler task", e))
                .build());

            // 注册JVM关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                if (scheduler != null && !scheduler.isShutdown()) {
                    scheduler.shutdown();
                    try {
                        if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                            scheduler.shutdownNow();
                        }
                    } catch (InterruptedException e) {
                        log.error("Failed to shutdown QueryTableSwitch scheduler", e);
                    }
                    log.info("QueryTableSwitch scheduler stopped");
                }
            }));

            // 每10秒检查一次Redis中的开关状态
            scheduler.scheduleAtFixedRate(() -> {
                try {
                    boolean newValue = getQueryTableSwitchSafely();
                    if (USE_OLD_TABLE_FOR_QUERY.get() != newValue) {
                        USE_OLD_TABLE_FOR_QUERY.set(newValue);
                        log.info("查询表开关已更新为: {} (true=旧表, false=新表+HBase)", newValue);
                    }
                } catch (Throwable e) {
                    log.error("Failed to check query table switch", e);
                }
            }, 0, 10, TimeUnit.SECONDS);
        }
    }

    /**
     * 安全地获取查询表开关状态
     * 如果Redis不可用，返回默认值true
     */
    private static boolean getQueryTableSwitchSafely() {
        try {
            return getQueryTableSwitchFromRedis();
        } catch (Exception e) {
            log.warn("获取查询表开关失败，使用默认值true: {}", e.getMessage());
            return true; // 默认使用旧表，保证安全
        }
    }

    /**
     * 从Redis获取查询表开关状态
     *
     * @return true表示查询使用旧表，false表示查询使用新表+HBase
     */
    private static boolean getQueryTableSwitchFromRedis() throws Exception{
        String value = "";
        try {
            value = RedisUtil.get(CACHE_KEY);
        } catch (Exception e) {
            log.error("获取查询表开关失败", e);
            throw e; // 重新抛出异常，由上层处理
        }

        // 如果Redis中没有值，则设置默认值为true（使用旧表）
        if (StrUtil.isBlank(value)) {
            try {
                if (RedisUtil.setnx(CACHE_KEY, "true")) {
                    value = "true";
                } else {
                    TimeUnit.MILLISECONDS.sleep(100);
                    value = RedisUtil.get(CACHE_KEY);
                }
            } catch (Exception e) {
                log.error("设置查询表开关失败", e);
                throw e; // 重新抛出异常，由上层处理
            }
        }
        return "true".equals(value);
    }

    /**
     * 获取当前查询表开关状态
     *
     * @return true表示查询使用旧表，false表示查询使用新表+HBase
     */
    public static boolean isUseOldTableForQuery() {
        ensureInitialized(); // 确保已初始化
        return USE_OLD_TABLE_FOR_QUERY.get();
    }

    /**
     * 设置查询表开关状态
     *
     * @param useOldTableForQuery true表示查询使用旧表，false表示查询使用新表+HBase
     */
    public static void setUseOldTableForQuery(boolean useOldTableForQuery) {
        ensureInitialized(); // 确保已初始化
        USE_OLD_TABLE_FOR_QUERY.set(useOldTableForQuery);
        try {
            RedisUtil.set(CACHE_KEY, String.valueOf(useOldTableForQuery));
            log.info("查询表开关已设置为: {} (true=旧表, false=新表+HBase)", useOldTableForQuery);
        } catch (Exception e) {
            log.error("设置查询表开关到Redis失败: {}", e.getMessage());
            // 不抛出异常，避免影响业务流程
        }
    }

    /**
     * 检查是否应该从新表查询（即开关关闭状态）
     *
     * @return true表示应该从新表查询，false表示从旧表查询
     */
    public static boolean shouldQueryFromNewTable() {
        return !isUseOldTableForQuery();
    }
}
