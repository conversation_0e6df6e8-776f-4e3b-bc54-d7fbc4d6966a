package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.sender.AxisSenderUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import com.cheche365.bc.utils.zhonghualianhe.util.ZhongHuaRSAUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import javax.xml.namespace.QName;
import java.util.Map;

@Component
public class ZhongHuaHandler implements BaseHandler {

    private static final String NAMESPACE_URI = "http://ebusiness.sinosoft.com";
    private static final String LOCAL_PART = "getRequest";
    private static final long DEFAULT_TIMEOUT = 30000L;
    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.CICP.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {

        if (MapUtils.getBoolean(autoTask.getConfigs(), "isOnline", false)) {
            QName qName = new QName(NAMESPACE_URI, LOCAL_PART);
            return AxisSenderUtil.send(url, requestBody, qName, DEFAULT_TIMEOUT);
        } else {
            // 中华联合加密、加签
            Map<String, String> encrypt = ZhongHuaRSAUtils.encryptAndSign(requestBody, (String) autoTask.getConfigs().get("publicKey"), (String) autoTask.getConfigs().get("thirdPrivateKey"), "UTF-8", true, true);
            String content = encrypt.get("CONTENT"); //加密后请求参数
            String sign = encrypt.get("GW_CH_SIGN"); //加密签名-解密使用
            Map<String, String> headers = autoTask.getReqHeaders();
            headers.put("GW_CH_SIGN", sign);
            String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, content, null, headers, "UTF-8", TaskUtil.buildReqConfig(autoTask), "", autoTask.getRepHeaders());;
            // 中华联合解密
            responseBody = ZhongHuaRSAUtils.checkSignAndDecrypt(responseBody, autoTask.getRepHeaders().get("GW_CH_SIGN"), (String) autoTask.getConfigs().get("publicKey"), (String) autoTask.getConfigs().get("thirdPrivateKey"), "UTF-8", true, true);
            return responseBody;
        }
    }
}
