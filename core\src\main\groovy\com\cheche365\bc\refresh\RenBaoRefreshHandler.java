package com.cheche365.bc.refresh;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.utils.RedisUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.cheche365.bc.constants.Constants.AUTHORIZATION_CACHE_KEY_PREFIX;
import static com.cheche365.bc.constants.Constants.COOKIE_STRING_CACHE_KEY_PREFIX;
import static com.cheche365.bc.constants.Constants.SESSION_CACHE_KEY_PREFIX;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class RenBaoRefreshHandler implements BaseRefreshHandler {


    // 人保营销系统域名片段
    private static final String MARKET_HOST_FRAGMENT = "yxgl-picc";
    private static final String SUCCESS_STRING = "保存成功";
    @Override
    public boolean isSpecial(String companyId) {
        return String.valueOf(InsCompanyEnum.PICC.getCode()).equals(companyId);
    }

    @Override
    public String refresh(String url, CloseableHttpClient httpClient, RequestConfig requestConfig, String login) throws Exception {

        if (StringUtils.isNotBlank(url) && url.contains(MARKET_HOST_FRAGMENT)) {
            return refreshMarketSystem(url, httpClient, requestConfig, login);
        } else {
            return refreshFourthSystem(url, httpClient, requestConfig, login);
        }
    }

    private String refreshMarketSystem(String url, CloseableHttpClient httpClient, RequestConfig requestConfig, String login) {
        try {
            Map<String, String> headers = buildHeadersForMarketSystem(login);
            return (String) HttpSender.doGet(httpClient, url, headers, null, "UTF-8", requestConfig, false);
        } catch (Exception e) {
            log.error("人保营销系统账号：{}，刷新会话链接:{} 出现异常:{}", login, url, ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    private Map<String, String> buildHeadersForMarketSystem(String login) {
        Map<String, String> headers = Maps.newHashMap();
        String cookie = RedisUtil.get(COOKIE_STRING_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login);
        String authorization = RedisUtil.get(AUTHORIZATION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login);
        if (StringUtils.isNotBlank(cookie)) {
            headers.put("Cookie", cookie);
        }
        if (StringUtils.isNotBlank(authorization)) {
            headers.put("authorization", authorization);
        }
        return headers;
    }

    private String refreshFourthSystem(String url, CloseableHttpClient httpClient, RequestConfig requestConfig, String login) throws Exception {
        String token = RedisUtil.get(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login);
        if (StringUtils.isBlank(token)) {
            return null;
        }

        if (RedisUtil.getKeyExpire(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login, TimeUnit.MINUTES) > 30) {
            String successMsg = RedisUtil.get(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login + ":successMsg");
            if (StringUtils.isNotBlank(successMsg)) {
                return successMsg;
            }
            return SUCCESS_STRING;
        }

        String refreshUrl = RedisUtil.get(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login + ":refreshUrl");
        if (StringUtils.isBlank(url)) {
            url = refreshUrl;
        }

        String senderStr = RedisUtil.get(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login + ":refreshParam");

        Map<String, String> headers = buildHeadersForSession(token, url);

        try {
            Map<String, String> responseHeader = Maps.newHashMap();
            String result = HttpSender.doPostWithRetry(1, httpClient, true, url, senderStr, null, headers, "utf-8", requestConfig, null, responseHeader);
            updateTokenIfPresent(responseHeader, login);
            return result;
        } catch (Exception e) {
            handleSessionRefreshException(e, login, token, senderStr, url);
            return null;
        }
    }

    private Map<String, String> buildHeadersForSession(String token, String url) throws Exception {
        Map<String, String> headers = Maps.newHashMap();
        headers.put(HttpHeaders.CONTENT_TYPE, "application/json;charset=utf-8");
        headers.put(HttpHeaders.ACCEPT, "application/json, text/plain, */*");
        headers.put(HttpHeaders.CONNECTION, "keep-alive");
        headers.put(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        headers.put(HttpHeaders.HOST, new URL(url).getHost());
        headers.put("TOKEN", token);
        return headers;
    }

    private void updateTokenIfPresent(Map<String, String> responseHeader, String login) {
        String newToken = responseHeader.get("NEWTOKEN");
        if (StringUtils.isNotBlank(newToken)) {
            RedisUtil.set(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login, newToken, 60 * 60);
        }
    }

    private void handleSessionRefreshException(Exception e, String login, String token, String senderStr, String url) {
        String error = ExceptionUtils.getStackTrace(e);
        log.error("人保四代保持登录异常: {}，账号：{}，token：{}， 发送post请求参数：{}，url：{}", error, login, token, senderStr, url);
        if (StringUtils.isNotBlank(error) && error.contains("failed statusCode :401")) {
            deleteSessionData(login);
        }
    }

    private void deleteSessionData(String login) {
        RedisUtil.delete(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login);
        RedisUtil.delete(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login + ":refreshUrl");
        RedisUtil.delete(SESSION_CACHE_KEY_PREFIX + InsCompanyEnum.PICC.getCode() + ":" + login + ":refreshParam");
    }
}
