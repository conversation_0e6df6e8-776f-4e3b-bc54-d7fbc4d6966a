package com.cheche365.bc.handler.edi;

import com.cheche365.bc.cache.RedisCache;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.exception.InsReturnException;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.dama.Constant;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import static com.cheche365.bc.constants.Constants.EDI_TOKEN;
/**
 * @Description:
 * @author: Jwar
 * @date: 2023.12.19
 */
@Component
public class ZiJinHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.ZKIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        String result = "";
        try {
            result = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getArrayParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        } catch (Exception e) {
            String operatorCode = (String) autoTask.getConfigs().get("operatorCode");
            StringRedisTemplate redisTemplate = RedisCache.getStringRedis();
            redisTemplate.delete(EDI_TOKEN + "2095:getAccessToken");
            redisTemplate.delete(EDI_TOKEN + "2095:queryToken:" + operatorCode);
            throw new InsReturnException(e.getMessage());
        }
        return result;
    }
}
