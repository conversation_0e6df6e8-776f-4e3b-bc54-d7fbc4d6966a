package com.cheche365.bc.model;


import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;

/**
 * Created by wjl on 2015/9/18.
 */
@ClassDoc(remark = "车辆所属性质")
public enum CarUserType {
    /**
     * 个人用车
     */
    @FieldDoc(des = "个人用车")
    PersonalCar(0),
    /**
     * 企业用车
     */
    @FieldDoc(des = "企业用车")
    EnterpriseCar(1),
    /**
     * 机关团体用车
     */
    @FieldDoc(des = "机关团体用车")
    OrganizationCar(2);

    private int code;

    CarUserType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
