package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.Encryption;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

@Component
public class YangGuangHandler implements BaseHandler {


    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.SMIC.getCode();
    }


    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        requestBody = Encryption.authcodeEncode(requestBody, (String) autoTask.getConfigs().get("key"));
        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getRepHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        //阳光返回报文解密
        responseBody = Encryption.authcodeDecode(responseBody, (String) autoTask.getConfigs().get("key"));
        return responseBody;
    }
}
