akka {
  loglevel = DEBUG
  stdout-loglevel = DEBUG
  loggers = ["akka.event.slf4j.Slf4jLogger"]
  jvm-exit-on-fatal-error = on

  actor {
    provider = "akka.actor.LocalActorRefProvider"
    default-dispatcher {
      type = "com.cheche365.bc.config.DefaultDispatcherConfigurator"
      executor = "fork-join-executor"
      fork-join-executor {
        parallelism-min = 50
        parallelism-factor = 100
        parallelism-max = 1000
        allow-core-timeout = on
      }
    }
  }
}
