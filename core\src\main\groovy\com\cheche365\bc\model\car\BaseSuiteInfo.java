package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Map;

/**
 * 投保基本信息
 * Created by Administrator on 2015-10-12.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "投保险种信息")
@Getter
@Setter
@XmlRootElement
public class BaseSuiteInfo implements Serializable {

    /**
     * 商业险投保信息
     */
    @FieldDoc(des = "商业险配置", remark = "投商业险必填参见BizSuiteInfo")
    private BizSuiteInfo bizSuiteInfo;
    /**
     * 交强险投保信息
     */
    @FieldDoc(des = "交强险配置", remark = "投交强险必填参见EfcSuiteInfo")
    private EfcSuiteInfo efcSuiteInfo;
    /**
     * 车船税投保信息
     */
    @FieldDoc(des = "车船税配置", remark = "投车船税必填参见EfcSuiteInfo")
    private TaxSuiteInfo taxSuiteInfo;

    @FieldDoc(des = "额外险种配置", remark = "跟车险一起购买的其他额外险")
    private Map<String,Object> extSuiteInfo;

}
