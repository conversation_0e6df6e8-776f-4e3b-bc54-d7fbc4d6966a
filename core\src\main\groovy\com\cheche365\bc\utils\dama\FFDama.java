package com.cheche365.bc.utils.dama;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.Objects;

/**
 * 斐斐打码
 * 网站：www.fateadm.com
 *
 * <AUTHOR>
 * Created by austinChen on 2018/4/24 9:15.
 */
@Slf4j
public class FFDama implements DamaHandler {

    public FFDama() {
        this.app_id = Constant.FF_APP_ID;
        this.app_key = Constant.FF_APP_KEY;
        this.usr_id = Constant.FF_PD_ID;
        this.usr_key = Constant.FF_PD_KEY;
        this.pred_url = "http://pred.fateadm.com";
    }

    private int sequence;

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    public int getSequence() {
        return sequence;
    }

    public FFDama(int sequence) {
        this.sequence = sequence;
        this.app_id = Constant.FF_APP_ID;
        this.app_key = Constant.FF_APP_KEY;
        this.usr_id = Constant.FF_PD_ID;
        this.usr_key = Constant.FF_PD_KEY;
        this.pred_url = "http://pred.fateadm.com";
    }

    protected String app_id;
    protected String app_key;
    protected String usr_id;
    protected String usr_key;
    protected String pred_url;


    public HttpResp QueryBalc() throws Exception {
        long cur_tm = System.currentTimeMillis() / 1000;
        String stm = String.valueOf(cur_tm);
        String sign = CalcSign(usr_id, usr_key, stm);
        String url = this.pred_url + "/api/custval";
        String params = "user_id=" + this.usr_id + "&timestamp=" + stm + "&sign=" + sign;
        String pres = HttpPost(url, params);
        HttpResp resp = ParseHttpResp(pres);
        return resp;
    }

    public HttpResp Charge(String cardid, String cardkey) throws Exception {
        long cur_tm = System.currentTimeMillis() / 1000;
        String stm = String.valueOf(cur_tm);
        String sign = CalcSign(usr_id, usr_key, stm);
        String csign = CalcMd5(usr_key + stm + cardid + cardkey);
        String url = this.pred_url + "/api/charge";
        String params = "user_id=" + usr_id + "&timestamp=" + stm + "&sign=" + sign + "&cardid=" + cardid + "&csign=" + csign;
        String pres = HttpPost(url, params);
        HttpResp resp = ParseHttpResp(pres);
        return resp;
    }

    public HttpResp PredictFromFile(String pred_type, String file_name) throws Exception {
        byte[] file_data = ReadBinaryFile(file_name);
        if (file_data == null) {
            HttpResp resp = new HttpResp();
            resp.ret_code = -1;
            resp.err_msg = "ERROR: read file failed! file_name: " + file_name;
            return resp;
        }
        HttpResp resp = Predict(pred_type, file_data);
        return resp;
    }

    public HttpResp Predict(String pred_type, byte[] file_data) throws Exception {
        try {
            long cur_tm = System.currentTimeMillis() / 1000;
            String stm = String.valueOf(cur_tm);
            String sign = CalcSign(usr_id, usr_key, stm);
            String img_data = CalcBase64(file_data);
            img_data = URLEncoder.encode(img_data, "utf-8");
            String url = pred_url + "/api/capreg";
            String params = "user_id=" + usr_id + "&timestamp=" + stm + "&sign=" + sign + "&predict_type=" + pred_type;
            if (!app_id.isEmpty()) {
                String asign = CalcSign(app_id, app_key, stm);
                params = params + "&appid=" + app_id + "&asign=" + asign;
            }
            params += "&img_data=" + img_data;
            //System.out.printf("file_data len: %d img_data: %d params: %s\n", file_data.length, img_data.length(), params);
            String pres = HttpPost(url, params);
            log.info("菲菲打码返回：{}", pres);
            HttpResp resp = ParseHttpResp(pres);
            return resp;
        } catch (Exception e) {
            log.error("菲菲打码异常：{}", ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    public HttpResp Justice(String req_id) throws Exception {
        long cur_tm = System.currentTimeMillis() / 1000;
        String stm = String.valueOf(cur_tm);
        String sign = CalcSign(usr_id, usr_key, stm);
        String url = pred_url + "/api/capjust";
        String params = "user_id=" + usr_id + "&timestamp=" + stm + "&sign=" + sign + "&request_id=" + req_id;
        String pres = HttpPost(url, params);
        HttpResp resp = ParseHttpResp(pres);
        return resp;
    }

    @Override
    public String execute(String base64, String type) {
        try (InputStream is = new ByteArrayInputStream(cn.hutool.core.codec.Base64.decode(base64))) {
            byte[] buffer = Dama2Web.stream2Byte(is);
            HttpResp resp = Predict(type, buffer);
            if (Objects.nonNull(resp)) {
                if (resp.ret_code == 0) {
                    JSONObject back = JSON.parseObject(resp.rsp_data);
                    return back.getString("result");
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }


    public static class HttpResp {
        public int ret_code;
        public double cust_val;
        public String err_msg;
        public String req_id;
        public String rsp_data;
        public String pred_resl;
    }

    // 为避免引入复杂的json包，这里简单实现一个，只用来能解析从网络的回包中的指定字段的内容
    public static class JsonHelper {
        public String json;
        public int next_idx;

        public JsonHelper(String json) {
            this.json = json;
            this.next_idx = 0;
        }

        public void Skip() {
            while (next_idx < json.length()) {
                char c = json.charAt(next_idx);
                if ((c <= 32) || (c == '\\')) {
                    next_idx++;
                } else {
                    break;
                }
            }
        }

        public String NextSToken() {
            //int start = next_idx;
            String ret = "";
            while (next_idx < json.length()) {
                char c = json.charAt(next_idx);
                if (c == '\"') {
                    break;
                }
                if ((c == '\\') && (next_idx + 1 < json.length())) {
                    if (json.charAt(next_idx + 1) == '\\') {
                        ret += '\\';
                        next_idx += 2;
                        continue;
                    }
                    if (json.charAt(next_idx + 1) == '\"') {
                        ret += '\"';
                        next_idx += 2;
                        continue;
                    }
                }
                ret += c;
                next_idx++;
            }
            return ret;
        }

        public String NextNToken() {
            String ret = "";
            while (next_idx < json.length()) {
                char c = json.charAt(next_idx);
                if (c == '\\') {
                    next_idx++;
                    continue;
                }
                if ((c < '0' || c > '9') && c != '.') {
                    // not number
                    break;
                }
                ret += c;
                next_idx++;
            }
            return ret;
        }

        public void Key2Val(HttpResp rsp, String key, String val) {
            if (key.equals("RetCode")) {
                rsp.ret_code = Integer.parseInt(val);
            } else if (key.equals("ErrMsg")) {
                rsp.err_msg = val;
            } else if (key.equals("RequestId")) {
                rsp.req_id = val;
            } else if (key.equals("RspData")) {
                rsp.rsp_data = val;
            } else if (key.equals("result")) {
                rsp.pred_resl = val;
            } else if (key.equals("cust_val")) {
                rsp.cust_val = Double.parseDouble(val);
            }
        }

        public void Parse(HttpResp rsp) {
            //rsp.ret_code    = -1;
            next_idx = 0;
            String key = "";
            String sval = "";
            for (next_idx = 0; next_idx < json.length(); ) {
                Skip();
                char c = json.charAt(next_idx);
                switch (c) {
                    case ':':
                    case ',':
                        break;
                    case '[':
                    case '{':
                    case '}':
                    case ']':
                        // not support here
                        break;
                    case '\"':
                        next_idx++;
                        sval = NextSToken();
                        Skip();
                        if (next_idx >= json.length()) {
                            break;
                        }
                        if (json.charAt(next_idx + 1) == ':') {
                            key = sval;
                            next_idx++;
                            continue;
                        }
                        // key to val
                        Key2Val(rsp, key, sval);
                        key = "";
                        break;
                    case '+':
                    case '-':
                    case '.':
                    case '0':
                    case '1':
                    case '2':
                    case '3':
                    case '4':
                    case '5':
                    case '6':
                    case '7':
                    case '8':
                    case '9':
                        // is number
                        sval = NextNToken();
                        // key to val
                        Key2Val(rsp, key, sval);
                        key = "";
                        break;
                    case 'n':
                    case 'N':
                        sval = json.substring(next_idx, 4).toLowerCase();
                        if (!sval.equals("null")) {
                            //error
                            break;
                        }
                        sval = "";
                        next_idx += 4;
                        // key to val
                        Key2Val(rsp, key, sval);
                        key = "";
                        break;
                    default:
                        break;
                }
                next_idx++;
            }
        }
    }

    public static String ToHex(byte[] arr) {
        StringBuffer md5str = new StringBuffer();
        int digital;
        for (int i = 0; i < arr.length; i++) {
            digital = arr[i];
            if (digital < 0) {
                digital += 256;
            }
            if (digital < 16) {
                md5str.append("0");
            }
            md5str.append(Integer.toHexString(digital));
        }
        return md5str.toString().toLowerCase();
    }

    public static String CalcMd5(String src) {
        String md5str = "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] input = src.getBytes();
            byte[] buff = md.digest(input);
            md5str = ToHex(buff);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return md5str;
    }

    public static String CalcBase64(byte[] data) {
        String s = "";
        if (data != null) {
            s = Base64.getEncoder().encodeToString(data);
        }
        return s;
    }

    public static String CalcSign(String id, String key, String tm) {
        String chk1 = CalcMd5(tm + key);
        String sum = CalcMd5(id + tm + chk1);
        //System.out.printf("userid: %s key: %s tm: %s chk1: %s chk2: %s\n", id, key, tm, chk1, sum);
        return sum;
    }

    public static HttpResp ParseHttpResp(String resl) {
        HttpResp resp = new HttpResp();
        resp.ret_code = -1;
        JsonHelper json = new JsonHelper(resl);
        json.Parse(resp);
        if (!resp.rsp_data.isEmpty()) {
            JsonHelper rjson = new JsonHelper(resp.rsp_data);
            rjson.Parse(resp);
        }
        return resp;
    }

    public static byte[] ReadBinaryFile(String file_name) throws IOException {
        InputStream in = null;
        BufferedInputStream buffer = null;
        DataInputStream dataIn = null;
        ByteArrayOutputStream bos = null;
        DataOutputStream dos = null;
        byte[] bArray = null;
        try {
            in = new FileInputStream(file_name);
            buffer = new BufferedInputStream(in);
            dataIn = new DataInputStream(buffer);
            bos = new ByteArrayOutputStream();
            dos = new DataOutputStream(bos);
            byte[] buf = new byte[1024];
            while (true) {
                int len = dataIn.read(buf);
                if (len < 0) {
                    break;
                }
                dos.write(buf, 0, len);
            }
            bArray = bos.toByteArray();
        } catch (Exception e) {
            return bArray;
        } finally {
            if (in != null) {
                in.close();
            }
            if (dataIn != null) {
                dataIn.close();
            }
            if (buffer != null) {
                buffer.close();
            }
            if (bos != null) {
                bos.close();
            }
            if (dos != null) {
                dos.close();
            }
        }
        return bArray;
    }

    public static String HttpPost(String url, String params) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(params);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }
}
