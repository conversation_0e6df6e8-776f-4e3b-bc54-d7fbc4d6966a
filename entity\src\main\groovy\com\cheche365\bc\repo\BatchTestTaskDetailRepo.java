package com.cheche365.bc.repo;

import com.cheche365.bc.entity.mongo.BatchTestTaskDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BatchTestTaskDetailRepo extends MongoRepository<BatchTestTaskDetail, String> {

    BatchTestTaskDetail findFirstByEnquiryIdOrderByCreateTimeDesc(String enquiryId);

    List<BatchTestTaskDetail> findAllByBatchTestTaskId(Long batchTestTaskId);
}
