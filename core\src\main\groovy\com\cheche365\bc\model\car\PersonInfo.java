package com.cheche365.bc.model.car; /**
 *
 */

import com.cheche365.bc.annotation.FieldDoc;
import com.cheche365.bc.model.IdCardType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;

/**
 * 人员信息
 *
 * <AUTHOR>
 *         created at 2015年6月15日 下午1:35:20
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@XmlRootElement
public class PersonInfo implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 6482393019408603644L;

    @FieldDoc(des="工号")
    private String workerId;
    /**
     * 中文姓名
     */
    @FieldDoc(des="姓名",need = true,remark = "")
    private String name;

    /**
     * 微信绑定的openId*/
    @FieldDoc(des="微信绑定的openId",remark = "")
    private String wxOpenId;
    /**
     * 英文名
     */
    @FieldDoc(des="英文名",remark = "")
    private String enName;
    //0男1女
    /**性别0男1女*/
    @FieldDoc(des="性别",need = true,remark = "性别0男1女")
    private Integer sex;
    /**居住地址*/
    @FieldDoc(des="居住地址",remark = "")
    private String address;
    /**证件类型*/
    @FieldDoc(des="证件类型",need = true,remark = "参见IdCardType",relatTypes= {IdCardType.class})
    private Integer idCardType;
    /**证件号*/
    @FieldDoc(des="证件号",need = true)
    private String idCard;
    /**手机号码*/
    @FieldDoc(des="手机号码")
    private String mobile;
    /**电话号码*/
    @FieldDoc(des="固话号码")
    private String phone;
    /**电子邮箱*/
    @FieldDoc(des="E-Mail")
    private String email;
    /**民族*/
    @FieldDoc(des="民族")
    private String national;
    /**出生日期*/
    @FieldDoc(des="出生日期")
    private Date birthday;
    /**邮编*/
    @FieldDoc(des="邮政编码")
    private String postCode;
    /**有效起期*/
    @FieldDoc(des="有效起期")
    private String regDate;
    /**有效止期*/
    @FieldDoc(des="有效止期")
    private String validDate;
    /**身份证签发机关*/
    @FieldDoc(des="身份证签发机关")
    private String iDCardIssuingAuthority;
    /**密码*/
    private String pwd;
    /**创建时间*/
    private Date created;
    /**更新时间*/
    private Date updated;

}
