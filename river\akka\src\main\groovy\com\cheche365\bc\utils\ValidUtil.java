package com.cheche365.bc.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.annotation.FieldDoc;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.springframework.util.StringUtils.tokenizeToStringArray;

/**
 * <AUTHOR>
 */
public class ValidUtil {

    // 添加缓存字段
    private static final ConcurrentMap<String, ClassInfo> CLASS_CACHE = new ConcurrentHashMap<>();

    /**
     * 使用Function接口实现的字段校验
     */
    public static String validDataByClass(JSONObject respJSONOBJ, String needValidationClassNames) {
        if (respJSONOBJ == null || StrUtil.isBlank(needValidationClassNames)) {
            return null;
        }
        Stream<String> classNames = Arrays.stream(tokenizeToStringArray(needValidationClassNames, ",; \t\n"));
        return classNames.map(validField(respJSONOBJ))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining());
    }

    private static Function<String, String> validField(JSONObject respJSONOBJ) {
        return className -> {
            // 从缓存获取ClassInfo
            ClassInfo classInfo = CLASS_CACHE.computeIfAbsent(className, key -> new ClassInfo(ClassUtil.loadClass(className)));

            if (ObjUtil.isEmpty(classInfo)) {
                return String.format("类未找到:%s;", className);
            }

            var simpleName = classInfo.clazz.getSimpleName();
            var shorName = StrUtil.lowerFirst(simpleName);

            JSONObject classDataJSON = respJSONOBJ.getJSONObject(shorName);
            if (classDataJSON == null) {
                return String.format("缺少类:%s;", className);
            }

            if (CollUtil.isEmpty(classInfo.needFields)) {
                return "";
            }
            List<String> missingFields = classInfo.needFields.stream()
                    .map(Field::getName)
                    .filter(name -> classDataJSON.get(name) == null)
                    .collect(Collectors.toList());

            return missingFields.isEmpty() ? ""
                    : String.format("缺少类:%s字段:%s;", className, String.join(",", missingFields));
        };
    }

    private static class ClassInfo {
        private final Class<?> clazz;
        private final List<Field> needFields;

        public ClassInfo(Class<?> clazz) {
            this.clazz = clazz;
            this.needFields = Arrays.stream(clazz.getDeclaredFields())
                    .filter(field -> {
                        FieldDoc fieldDoc = field.getAnnotation(FieldDoc.class);
                        return fieldDoc != null && fieldDoc.need();
                    })
                    .collect(Collectors.toList());
        }
    }
}
