package com.cheche365.bc.model.car;

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;

/**
 * 上年投保的信息
 * Created by Austin on 2016/3/17.
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "上年出单信息")
@Getter
@Setter
@XmlRootElement
public final class LastYearInfo {

    /**
     * 上年商业险保单号
     */
    @FieldDoc(des = "商业险投保单号", remark = "")
    private String lastBizProposeNum;
    /**
     * 上年交强险保单号
     */
    @FieldDoc(des = "交强险投保单号", remark = "")
    private String lastEfcProposeNum;

    @FieldDoc(des = "商业险保单号", remark = "")
    private String lastBizPolicyCode;
    /**
     * 上年交强险保单号
     */
    @FieldDoc(des = "交强险保单号", remark = "")
    private String lastEfcPolicyCode;

    /**
     * 上年投保保险公司Id
     */
    @FieldDoc(des = "保险公司Id", remark = "")
    private String lastComId;
    /**
     * 上年投保保险公司名称
     */
    @FieldDoc(des = "保险公司名称", remark = "")
    private String lastComName;

}
