package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)

public enum OperationType {

    /**
     * 操作类型
     */
    IMPORT_COMMISSION(1, "导入发佣清单"),
    EXPORT_COMMISSION(2, "导出发佣清单"),
    IMPORT_SETTLEMENT(3, "导入保险公司清单"),
    EXPORT_SETTLEMENT_EXAMINE_DETAIL(4, "生成审核清单"),
    EXPORT_SETTLEMENT(5, "导出结算清单"),
    CHECK_SETTLEMENT_BATCH(6, "审核结算批次"),
    EXPORT_FEE_SETTLEMENT_TAIZHANG(7, "导出手续费结算台账表"),
    EXPORT_ACCOUNT_RECEIVE(8, "导出应收账龄表"),
    CHECK_REFUSED(9, "作废结算批次"),
    CHECK_REVERT_CHECK(10, "反审核结算批次")
    ;

    @EnumValue
//    @JsonValue
    private final int code;

    private final String text;

    OperationType(int code, String text) {
        this.code = code;
        this.text = text;
    }

    @JsonCreator
    public static OperationType get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(null);
    }

    public static OperationType get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }

}
