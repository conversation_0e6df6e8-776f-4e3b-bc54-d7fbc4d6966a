package com.cheche365.bc.service.listener;

import cn.hutool.core.collection.CollUtil;
import com.cheche365.bc.entity.mongo.ActionLog;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.LogFormatUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.mongodb.core.mapping.event.AbstractMongoEventListener;
import org.springframework.data.mongodb.core.mapping.event.BeforeConvertEvent;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class ActionLogLifecycleListener extends AbstractMongoEventListener<ActionLog> {

    private static final Long MAX_BODY_LENGTH = 1024 * 1024 * 3L;

    @Override
    public void onBeforeConvert(BeforeConvertEvent<ActionLog> event) {
        ActionLog actionLog = event.getSource();
        try {
            actionLog.setRequestBody(compressStrSize(actionLog.getRequestBody()));
            actionLog.setResponseBody(compressStrSize(actionLog.getResponseBody()));
            actionLog.setInTaskBody(maxLength(actionLog.getInTaskBody()));
            actionLog.setOutTaskBody(maxLength(actionLog.getOutTaskBody()));
        } catch (Exception e) {
            log.error("actionLog before save mongo filter body error:{}", ExceptionUtils.getStackTrace(e));
        }
    }

    private static String compressStrSize(String body) {
        return maxLength(replaceBase64(body));
    }

    private static String replaceBase64(String body) {
        if (StringUtils.isNotBlank(body)) {
            List<String> list = StringUtil.stringMatcher(body, LogFormatUtils.BASE64_PARSER);
            if (CollUtil.isNotEmpty(list)) {
                body = StringUtil.replaceString(body, list, LogFormatUtils.BASE64_FRAGMENT);
            }
        }
        return body;
    }

    private static String maxLength(String body) {
        if (StringUtils.isNotBlank(body) && body.length() > MAX_BODY_LENGTH) {
            return "BODY SO LONG (more than 3M) ........";
        }
        return body;
    }
}
