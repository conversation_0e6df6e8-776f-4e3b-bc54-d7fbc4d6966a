package com.cheche365.bc.config;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class HttpClientConfig {

    @Value("${http.maxTotal:100}")
    private int maxTotal;

    @Value("${http.defaultMaxPerRoute:50}")
    private int defaultMaxPerRoute;

    @Value("${http.connectTimeout:5000}")
    private int connectTimeout;

    @Value("${http.socketTimeout:30000}")
    private int socketTimeout;

    @Value("${http.connectionRequestTimeout:30000}")
    private int connectionRequestTimeout;

    @Value("${http.validateAfterInactivity:5000}")
    private int validateAfterInactivity;

    @Value("${http.evictIdleConnections:10}")
    private int evictIdleConnections;

    @Bean
    public PoolingHttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        poolingConnectionManager.setMaxTotal(maxTotal);
        poolingConnectionManager.setDefaultMaxPerRoute(defaultMaxPerRoute);
        poolingConnectionManager.setValidateAfterInactivity(validateAfterInactivity);
        return poolingConnectionManager;
    }

    @Bean
    public RequestConfig requestConfig() {
        return RequestConfig.custom()
            .setConnectTimeout(connectTimeout)
            .setSocketTimeout(socketTimeout)
            .setConnectionRequestTimeout(connectionRequestTimeout)
            .build();
    }

    @Bean(destroyMethod = "close")
    public CloseableHttpClient httpClient(
        PoolingHttpClientConnectionManager poolingConnectionManager,
        RequestConfig requestConfig) {
        return HttpClients.custom()
            //不共享连接池httpclient关闭ccm也关闭
            .setConnectionManagerShared(false)
            //默认keepalive策略
            .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
            //每10秒回收空闲连接
            .evictIdleConnections(evictIdleConnections, TimeUnit.SECONDS)
            .setDefaultRequestConfig(requestConfig)
            .setConnectionManager(poolingConnectionManager)
            .build();
    }

}
