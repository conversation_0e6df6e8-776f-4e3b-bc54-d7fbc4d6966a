package com.cheche365.bc.utils

import cn.hutool.json.JSONUtil
import com.cheche365.bc.utils.sender.HttpParams
import groovy.transform.Memoized
import groovyx.net.http.RESTClient

import java.util.concurrent.ConcurrentHashMap

class HttpClientUtils {

    private static ConcurrentHashMap<String, RESTClient> CLIENTS = new ConcurrentHashMap<>()

    static Closure TEXT_RESPONSE_PARSE = {
        text -> text
    }

    static Closure JSON_RESPONSE_PARSE = {
        text -> JSONUtil.toJsonStr(text)
    }


    def static call(HttpParams httpParams) {

        RESTClient client = CLIENTS.computeIfAbsent(httpParams.path, { path -> new RESTClient(path) })

        return client.(convertMethodName(httpParams.method))(
            toHttpClientParam(httpParams),
            {
                resp, text -> httpParams.parseResp text
            }
        )
    }

    static Map toHttpClientParam(HttpParams httpParams) {
        httpParams.properties.subMap(
            ['requestContentType', 'contentType', 'headers', 'path', 'body', 'query']
        )
    }

    @Memoized
    static String convertMethodName(String method) {
        [
            'http_post': 'post',
            'http_get' : 'get'
        ].getOrDefault(method, method)
    }
}
