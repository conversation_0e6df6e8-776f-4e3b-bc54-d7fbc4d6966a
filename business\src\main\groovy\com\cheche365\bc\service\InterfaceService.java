package com.cheche365.bc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.task.AutoTask;
import groovy.lang.Script;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
public interface InterfaceService extends IService<Interface> {

    Script getInterfaceScriptByIntType(String intType);

    boolean hasInterfaceAbility(String intType);

    Interface getInterfaceByTaskType(String taskType);

    boolean updateScriptCache(String taskType);

    Interface getInterface(AutoTask t, String intType);
}
