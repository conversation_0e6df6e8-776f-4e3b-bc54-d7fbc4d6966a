package com.cheche365.bc.handler.robot;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.MD5;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;
import java.util.zip.GZIPOutputStream;

@Component
public class YangGuangRobotHandler implements BaseHandler {

    private static final String ENCRYPTION_KEY = "sinosig0123456789";

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.ROBOT.getKey() + InsCompanyEnum.SMIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        Map<String, Object> tempValues = autoTask.getTempValues();
        Boolean isEncrypt = (Boolean) tempValues.get("isEncrypt");
        Boolean isZip = (Boolean) tempValues.get("isZip");
        Boolean isKey = (Boolean) tempValues.get("isKey");

        if (Objects.nonNull(isEncrypt) && isEncrypt) {
            requestBody = encrypt(JSONObject.parseObject(requestBody), isKey);
            if (Objects.nonNull(isZip) && isZip) {
                requestBody = gzip(requestBody);
            }
        }
        return HttpSender.doPostWithRetry(1, closeableHttpClient, true, url, requestBody,
            null, autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey(), autoTask.getRepHeaders());
    }

    private String encrypt(JSONObject param, Boolean isKey) {
        String data;
        if (Objects.nonNull(isKey) && isKey) {
            data = fixStr(param.toString() + ENCRYPTION_KEY);
        } else {
            data = fixStr(param.toString());
        }

        String tempData = Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8));
        String authKey = MD5.toHex(fixStr(tempData));
        JSONObject auth = new JSONObject();
        auth.put("key", authKey);
        param.put("auth", auth);
        return Base64.getEncoder().encodeToString(param.toString().getBytes(StandardCharsets.UTF_8));
    }

    private String fixStr(String str) {
        return str.replaceAll("[ \"']", "");
    }

    /**
     * 报文压缩
     *
     * @param body 压缩需要请求的字符串
     */
    private String gzip(String body) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream);
        gzipOutputStream.write(body.getBytes());
        gzipOutputStream.close();
        byte[] compressedBytes = byteArrayOutputStream.toByteArray();
        return Base64.getEncoder().encodeToString(compressedBytes);
    }

}

