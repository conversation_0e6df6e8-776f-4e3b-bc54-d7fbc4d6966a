package com.cheche365.bc.admin.rest.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.service.dto.AutoTaskData;
import com.cheche365.bc.admin.service.service.BatchTestTaskService;
import com.cheche365.bc.admin.service.service.InsAccountService;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.entity.mongo.BatchTestTaskDetail;
import com.cheche365.bc.entity.mysql.BatchTestTask;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.repo.BatchTestTaskDetailRepo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/ins/account/validation")
@AllArgsConstructor
public class InsAccountController {

    private final InsAccountService insAccountService;
    private final BatchTestTaskService batchTestTaskService;
    private final BatchTestTaskDetailRepo batchTestTaskDetailRepo;

    @PostMapping("/excel/exchange")
    public RestResponse<List<AutoTaskData>> excelExchange(@RequestParam("file") MultipartFile file) throws IOException {
        return RestResponse.success(insAccountService.exchange(file.getBytes()));
    }

    @PostMapping
    public RestResponse<BatchTestTask> validation(@RequestBody List<AutoTaskData> autoTaskDataList) {
        autoTaskDataList.forEach(AutoTaskData::check);
        return RestResponse.success(insAccountService.validation(autoTaskDataList));
    }

    @GetMapping("tasks")
    public RestResponse<Page<BatchTestTask>> tasks(@RequestParam(defaultValue = "1") Integer currentPage,
                                                   @RequestParam(defaultValue = "10") Integer pageSize,
                                                   @RequestParam(required = false) String batchNo) {
        // 获取并设置检索条件
        QueryWrapper<BatchTestTask> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(batchNo)) {
            wrapper.eq("batch_no", batchNo);
        }
        wrapper.orderByDesc("create_time");
        return RestResponse.success(batchTestTaskService.page(new Page<>(currentPage, pageSize),wrapper));
    }

    @GetMapping("task/{id}")
    public RestResponse<List<BatchTestTaskDetail>> task(@PathVariable Long id) {
        return RestResponse.success(batchTestTaskDetailRepo.findAllByBatchTestTaskId(id));
    }
}
