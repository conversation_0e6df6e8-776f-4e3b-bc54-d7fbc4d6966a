package com.cheche365.bc.handler.edi;

import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.SM4.Sm4Utils;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;

/**
 * <AUTHOR> zhangying
 * @date 2023-11-07
 * @descript :合众请求SM4加解密
 */
@Component
public class HeZhongHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.ULIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        Sm4Utils.secretKey = autoTask.getConfigs().get("secretKey").toString();
        //加密
        String str = URLEncoder.encode(Sm4Utils.encryptDataEcb(requestBody), "UTF-8");
        String responseBody = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, str, null, autoTask.getReqHeaders(), "UTF-8", TaskUtil.buildReqConfig(autoTask), "", autoTask.getRepHeaders());
        //解密
        responseBody = Sm4Utils.decryptDataEcb(responseBody);
        return responseBody;
    }
}
