package com.cheche365.bc.admin.service.dto.base;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> shanxf
 */
@Data
public class BaseSuiteInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    private BizSuiteInfo bizSuiteInfo;

    private EfcSuiteInfo efcSuiteInfo;

    public static BaseSuiteInfo createBaseSuiteInfo(String bizStartDate, String efcStartDate) {
        if (StrUtil.isAllBlank(bizStartDate, efcStartDate)) {
            return null;
        }
        BaseSuiteInfo baseSuiteInfo = new BaseSuiteInfo();
        if (StrUtil.isNotBlank(bizStartDate)) {
            baseSuiteInfo.setBizSuiteInfo(
                BizSuiteInfo.builder()
                    .start(bizStartDate)
                    .build()
            );
        }
        if (StrUtil.isNotBlank(efcStartDate)) {
            baseSuiteInfo.setEfcSuiteInfo(
                EfcSuiteInfo.builder()
                    .start(efcStartDate)
                    .build()
            );
        }
        return baseSuiteInfo;
    }
}
