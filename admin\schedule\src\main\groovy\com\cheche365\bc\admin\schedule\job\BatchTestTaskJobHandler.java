package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.admin.service.dto.AutoTaskData;
import com.cheche365.bc.admin.service.external.api.QuoteAPI;
import com.cheche365.bc.admin.service.external.dto.QuoteResp;
import com.cheche365.bc.admin.service.service.BatchTestTaskDetailService;
import com.cheche365.bc.entity.mongo.BatchTestTaskDetail;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.repo.BatchTestTaskDetailRepo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cheche365.bc.admin.service.external.dto.QuoteRequest.createByQuoteRequest;

@Component
@JobHandler(value = "batchTestTaskJobHandler")
@AllArgsConstructor
@Slf4j
public class BatchTestTaskJobHandler extends IJobHandler {

    private final BatchTestTaskDetailService batchTestTaskDetailService;
    private final BatchTestTaskDetailRepo batchTestTaskDetailRepo;
    private final QuoteAPI quoteAPI;

    @Override
    public ReturnT<String> execute(String s) {
        BatchTestTaskDetail detail = batchTestTaskDetailService.popTestTaskDetail();
        if (detail == null) {
            return ReturnT.SUCCESS;
        }
        AutoTaskData autoTaskData = (AutoTaskData) detail.getAutoTaskData();
        try {
            QuoteResp quoteResp = quoteAPI.doQuote(createByQuoteRequest(autoTaskData));

            detail.setEnquiryId(quoteResp.getEnquiryId());
            detail.setInsCompany(BatchTestTaskDetail.InsCompany.builder()
                .insCompanyId(quoteResp.getInsComId())
                .insCompanyName(
                    StrUtil.isNotBlank(quoteResp.getInsComId()) ? InsCompanyEnum.get(Integer.parseInt(quoteResp.getInsComId())).getDesc() : null)
                .build()
            );
            if (StrUtil.isNotBlank(quoteResp.getErrorMessage())) {
                detail.setErrorMessage(quoteResp.getErrorMessage());
                detail.setTaskStatus(TaskStatus.QUOTE_FAILED.getState());
            }
        } catch (Exception e) {
            detail.setTaskStatus(TaskStatus.QUOTE_FAILED.getState());
            detail.setErrorMessage(e.getMessage());
        }
        batchTestTaskDetailRepo.save(detail);

        return ReturnT.SUCCESS;
    }
}
