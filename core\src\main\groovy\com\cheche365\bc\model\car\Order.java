package com.cheche365.bc.model.car; /**
 *
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.cheche365.bc.common.VersionObject;
import com.cheche365.bc.model.AgentInfo;
import com.cheche365.bc.model.ChannelInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 针对以前的多方对象,只是作为基本信息进行定义,不进行存储
 *
 * <AUTHOR>
 *         created at 2015年6月12日 下午1:52:10
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ClassDoc(remark = "订单信息")
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@XmlRootElement
public class Order extends VersionObject implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = -3576383470236540152L;

    public Order() {

    }

    /**
     * 供应商列表
     */
    @FieldDoc(des = "单方Id列表")
    private List<String> sqIds;

    /**
     * 测试标志
     */
    @FieldDoc(des = "测试标志", remark = "true 为测试单")
    private boolean testFlag;


    /**
     * 渠道信息
     */
    @FieldDoc(des = "渠道信息", need = true)
    private ChannelInfo channel;

    /**
     * 归属中介机构
     */
    @FieldDoc(des = "归属机构", need = true)
    private IssueInfo belongOrg;

    /**
     * 代理人信息
     */
    @FieldDoc(des = "代理人信息", need = true)
    private AgentInfo agentInfo;

    /**
     * 投保区域
     */
    @FieldDoc(des = "投保区域")
    private InsArea insureArea;
    /**
     * 车辆信息
     */
    @FieldDoc(des = "车辆信息", need = true)
    private CarInfo carInfo;
    /**
     * 车主信息
     */
    @FieldDoc(des = "车主信息", need = true)
    private CarOwnerInfo carOwnerInfo;

    /**
     * 投保人
     */
    @FieldDoc(des = "投保人", need = true)
    private InsurePerson insurePerson;
    /**
     * 受益人列表
     */
    @FieldDoc(des = "受益人列表", need = true)
    private List<BeneficiaryPerson> beneficiaryPersons;
    /**
     * 被保人列表
     */
    @FieldDoc(des = "被保人列表", need = true)
    private List<InsurePerson> insuredPersons;

    /**
     * 投保险种的基本信息
     */
    @FieldDoc(des = "投保险种的配置", need = true)
    private BaseSuiteInfo suiteInfo;



    @FieldDoc(des = "额外险种配置", remark = "跟车险一起购买的其他额外险")
    private Map<String,Object> extSuiteInfo;

    /**
     * 订单创建时间
     */
    @FieldDoc(des = "订单创建时间")
    private Date created;
    /**
     * 订单更新时间
     */
    @FieldDoc(des = "订单更新时间")
    private Date updated;


    /**
     * 配送信息
     */
    @FieldDoc(des = "订单配送信息", remark = "投保时候需要填写")
    private DeliverInfo deliverInfo;

    /**
     * 平台信息
     */
    @FieldDoc(des = "平台查询的结果信息列表")
    private Map<String, Object> platformInfo;

    /**
     * 销售来源,网销,传统
     */
    @FieldDoc(des = "销售来源", remark = "1传统，2网销")
    private int saleFrom;

    /**
     * 上年的基本信息,用来续保等作用
     */
    @FieldDoc(des = "该车的上年信息")
    private LastYearInfo lastYearInfo;

    /**
     * 影像资料集合
     */
    @FieldDoc(des = "影像资料集合")
    private Map<String, String> imgAddress;

    /**
     * 供应商列表Map
     */
    @FieldDoc(des = "供应商列表Map", need = true)
    private Map<String, ProviderInfo> providerInfoMap;

    /**电子保单*/
    @FieldDoc(des="电子保单")
    private List<Map<String,Object>> elecPolicyInfo;


    @Override
    public String toString() {
        return getId() + "-" + this.getCarInfo().getPlateNum();
    }

    public boolean isBiz() {
        if (suiteInfo != null) {
            return suiteInfo.getBizSuiteInfo() != null;
        }
        return false;
    }

    public boolean isEfc() {
        if (suiteInfo != null) {
            return suiteInfo.getEfcSuiteInfo() != null;
        }
        return false;
    }

}
