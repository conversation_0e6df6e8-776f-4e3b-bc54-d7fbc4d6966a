package com.cheche365.bc.tools;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 地区工具
 */
@Slf4j
@UtilityClass
public final class AreaUtil {

    private static final TreeMap<Integer, Area> AREA_TREE_MAP;

    private static final List<Integer> MUNICIPALITY_IDS = List.of(
        110000,
        120000,
        110000,
        500000
    );

    static {
        List<Area> areas = JSONUtil.toList(
            FileUtil.readResource("/area.json", AreaUtil.class),
            Area.class
        );

        AREA_TREE_MAP = areas
            .stream()
            .collect(
                Collectors.toMap(
                    Area::getId,
                    area -> area,
                    (v1, v2) -> v2,
                    TreeMap::new
                )
            );
    }

    /**
     * 递归向上找所有name拼接
     * @param code 地区编码
     * @return 地址群路径
     */
    public String getCnName(String code) {
        Area area = AREA_TREE_MAP.get(Integer.valueOf(code));
        if (Objects.nonNull(area.getPid()) && area.getPid() != 0 && !MUNICIPALITY_IDS.contains(area.getPid())) {
            return getCnName(area.getPid().toString()) + area.getName();
        }
        return area.getName();
    }

    public String getAreaName(String code) {
        return AREA_TREE_MAP.get(Integer.valueOf(code)).getName();
    }

    public String getProvinceName(String code) {
        return getAreaName(getProvinceCode(code));
    }

    public String getProvinceCode(String code) {
        return code.substring(0, 2) + "0000";
    }

    @Data
    @EqualsAndHashCode
    private static class Area {
        private Integer id;
        private String name;
        private String type;
        private Integer pid;
    }
}
