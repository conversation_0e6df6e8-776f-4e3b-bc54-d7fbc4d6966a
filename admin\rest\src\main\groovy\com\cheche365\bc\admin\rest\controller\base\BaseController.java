package com.cheche365.bc.admin.rest.controller.base;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.admin.service.constant.SecurityConstants;
import com.cheche365.bc.model.RestResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通用Controller类
 *
 * @param <M>
 * @param <T>
 * <AUTHOR>
 */
@Slf4j
public class BaseController<M extends IService<T>, T extends Model> {
    @Autowired
    protected M service;

    private final Class<T> beanClass;
    private static final Pattern ORDER_PATTERN = Pattern.compile("(.+)_(asc|desc)end$");
    protected Map<String, Field> fieldMap;
    protected TableInfo tableInfo;
    private static final String SORT_KEY = "sorter";

    /**
     * 当前页
     */
    protected final static String PAGE_CURRENT = "currentPage";
    /**
     * 分页大小
     */
    protected final static String PAGE_SIZE = "pageSize";
    /**
     * 总数据量
     */
    protected final static String PAGE_TOTAL = "total";



    public BaseController() {
        ParameterizedType parameterizedType = (ParameterizedType) this.getClass().getGenericSuperclass();
        beanClass = (Class<T>) parameterizedType.getActualTypeArguments()[1];
        fieldMap = ReflectionKit.getFieldMap(beanClass);
        tableInfo = TableInfoHelper.getTableInfo(beanClass);
    }

    /**
     * 通用分页查询
     *
     * @param map
     * @param authentication
     * @return
     */
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        Page<T> page = createPage(map);

        // 获取并设置检索条件
        QueryWrapper<T> wrapper = getQueryWrapper(map, authentication);

        service.page(page, wrapper);

        return RestResponse.success(page);
    }

    /**
     * 根据map参数常见分页参数
     *
     * @param map
     * @return
     */
    protected Page<T> createPage(Map<String, ?> map) {
        Page<T> page = new Page<>();
        page.setCurrent(MapUtils.getLong(map, PAGE_CURRENT, 1L));
        page.setSize(MapUtils.getLong(map, PAGE_SIZE, 10L));
        page.setTotal(MapUtils.getLong(map, PAGE_TOTAL, 0L));
        return page;
    }

    /**
     * 通用全量查询
     *
     * @param map
     * @param authentication
     * @return
     */
    @RequestMapping(value = "all", method = RequestMethod.GET)
    public RestResponse all(@RequestParam Map<String, String> map, Authentication authentication) {
        // 获取并设置检索条件
        QueryWrapper<T> wrapper = getQueryWrapper(map, authentication);
        List<T> list = service.list(wrapper);
        return RestResponse.success(list);
    }

    /**
     * 根据ID查询单个对象
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "{id}", method = RequestMethod.GET)
    public RestResponse getType(@PathVariable Long id) {
        return RestResponse.success(service.getById(id));
    }

    /**
     * 新增对象
     *
     * @param rowData
     * @return
     */
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public RestResponse add(@RequestBody T rowData) {
        service.save(rowData);
        return RestResponse.success(rowData);
    }

    /**
     * 全量更新对象
     *
     * @param id
     * @param rowData
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public RestResponse update(@PathVariable Long id, @RequestBody T rowData) throws Exception {
        UpdateWrapper<T> wrapper = new UpdateWrapper<>();
        wrapper.eq(tableInfo.getKeyColumn(), id);
        for (TableFieldInfo tableFieldInfo : tableInfo.getFieldList()) {
            Field field = fieldMap.get(tableFieldInfo.getProperty());
            field.setAccessible(true);
            wrapper.set(
                    field.get(rowData) == null,
                    tableFieldInfo.getColumn(),
                    null);
        }
        service.update(rowData, wrapper);
        return RestResponse.success(rowData);
    }

    /**
     * 增量更新对象
     *
     * @param id
     * @param rowData
     * @return
     * @throws IllegalAccessException
     */
    @RequestMapping(value = "{id}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public RestResponse patch(@PathVariable Long id, @RequestBody T rowData) throws IllegalAccessException {
        Field field = fieldMap.get(tableInfo.getKeyColumn());
        field.setAccessible(true);
        field.set(rowData, id);
        service.updateById(rowData);
        return RestResponse.success(rowData);
    }

    /**
     * 根据ID删除对象
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE)
    public RestResponse delete(@PathVariable Long id) {
        service.removeById(id);
        return RestResponse.success();
    }

    /**
     * 判断是否增加用户筛选条件
     *
     * @param map
     * @param authentication
     * @return
     */
    protected QueryWrapper<T> getQueryWrapper(Map<String, String> map, Authentication authentication) {
        String adminAuthority = authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).filter(SecurityConstants.ROLE_ADMIN::equals).findAny().orElse(null);
        if (StringUtils.isEmpty(adminAuthority)) {
            return getQueryWrapper(map, authentication.getName());
        }
        return getQueryWrapper(map, "");
    }

    /**
     * 根据用户权限判断是否增加用户筛选条件
     *
     * @param authentication
     * @return
     */
    protected String getAuthorityUserName(Authentication authentication) {
        String adminAuthority = authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).filter(SecurityConstants.ROLE_ADMIN::equals).findAny().orElse(null);
        if (StringUtils.isEmpty(adminAuthority)) {
            return authentication.getName();
        }
        return "";
    }

    protected QueryWrapper<T> getQueryWrapper(Map<String, String> map, String curUserName) {
        return getQueryByWrapper(map,curUserName,null);
    }

    /**
     * 生产通用查询排序条件
     *
     * @param map
     * @param curUserName
     * @return
     */
    protected QueryWrapper<T> getQueryByWrapper(Map<String, String> map, String curUserName, QueryWrapper<T> wrapper) {
        if(wrapper == null) {
            wrapper = new QueryWrapper<>();
        }
        wrapper.eq("1", 1);
        if(tableInfo == null){
            tableInfo = TableInfoHelper.getTableInfo(beanClass);
        }
        for (TableFieldInfo tableFieldInfo : tableInfo.getFieldList()) {
            //字段名（转义后的）
            String columnName = tableFieldInfo.getColumn();
            //属性名
            String propertyName = tableFieldInfo.getProperty();
            Field field = fieldMap.get(propertyName);
            if (StringUtils.isNotEmpty(map.get(propertyName))) {
                String value = map.get(propertyName);
                //限制模糊查询只支持左侧匹配模糊查询
                if (value.startsWith("%") || value.endsWith("%")) {
                    wrapper.likeRight(columnName, value.replaceAll("%", ""));
                }  else {
                    if (field.getType().equals(Boolean.class)) {
                        wrapper.eq(columnName, Boolean.parseBoolean(value));
                    } else
                        wrapper.eq(columnName, value);
                }
            } else {
                //判断是否是时间类型
                if (field.getType().equals(LocalDate.class) || field.getType().equals(LocalDateTime.class)) {
                    String startTime = propertyName.concat("StartTime");
                    String endTime = propertyName.concat("EndTime");
                    if (StringUtils.isNotEmpty(map.get(startTime))) {
                        wrapper.ge(columnName, map.get(startTime));
                    }
                    if (StringUtils.isNotEmpty(map.get(endTime))) {
                        wrapper.le(columnName, map.get(endTime));
                    }
                }
            }
        }

        // 获取并设置排序信息
        if (map.get(SORT_KEY) != null) {
            String orderBy = map.get("sorter");
            Matcher matcher = ORDER_PATTERN.matcher(orderBy);
            if (matcher.find()) {
                String columns = matcher.group(1);
                String order = matcher.group(2);
                for (String columnName : columns.split(",")) {
                    TableFieldInfo tableFieldInfo = tableInfo.getFieldList().stream().filter(it -> columnName.equals(it.getProperty())).findAny().orElse(null);
                    if (tableFieldInfo != null) {
                        if (SqlKeyword.ASC.getSqlSegment().equalsIgnoreCase(order)) {
                            wrapper.orderByAsc(tableFieldInfo.getColumn());
                        } else {
                            wrapper.orderByDesc(tableFieldInfo.getColumn());
                        }
                    }
                }
            }
        }
        if (!"autoTraceId".equals(tableInfo.getKeyColumn())) {
            wrapper.orderByAsc(tableInfo.getKeyColumn());
        }
        return wrapper;
    }

    protected ResponseEntity downloadFile(String fileName, File file) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition", "attachment; filename=" + fileName + "; filename*=utf-8''" + fileName);
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentLength(file.length())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new FileSystemResource(file));
    }

    protected ResponseEntity downloadFile(File file) {
        return downloadFile(file.getName(), file);
    }


}
