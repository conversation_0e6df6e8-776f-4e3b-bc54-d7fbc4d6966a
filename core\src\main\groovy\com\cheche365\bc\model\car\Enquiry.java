package com.cheche365.bc.model.car; /**
 * 
 */

import com.cheche365.bc.annotation.ClassDoc;
import com.cheche365.bc.annotation.FieldDoc;
import com.cheche365.bc.common.VersionObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;

import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 单方对象，给EDI或者精灵的对象
 * <AUTHOR>
 * created at 2015年6月12日 下午1:51:53
 */
@JsonSerialize(include= JsonSerialize.Inclusion.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ClassDoc(remark="单方信息")
@Getter
@Setter
@XmlRootElement
public class Enquiry extends VersionObject implements Serializable {

    public Enquiry()
    {

    }
	/**
	 * 序列化ID
	 */
	private static final long serialVersionUID = -8951813245158515440L;


    @FieldDoc(des="订单信息")
    private Order order;

    /**供应商信息*/
    @FieldDoc(des="供应商信息",need=true)
    private ProviderInfo providerInfo;

    @FieldDoc(des="是否续保")
    private boolean  isRenewal;

    /**支付信息*/
    @FieldDoc(des="支付信息")
    private PayInfo payInfo;


    /**出单机构信息*/
    @FieldDoc(des="出单机构信息")
    private IssueInfo issueInfo;

    @FieldDoc(des="结果信息")
    private ResultInfo resultInfo;

    /**特别约定列表*/
    @FieldDoc(des="特别约定列表")
    private Map<String, SpecialClause> specialClauseMap;

    /**单方状态*/
    @FieldDoc(des="单方状态")
    private String state;

    @FieldDoc(des="处理人")
    private String processor;

    /** 该单方的配置选项*/
    @FieldDoc(des="配置信息")
    private Map config;

    /**保险公司订单ID*/
    @FieldDoc(des="保险公司订单号")
    private String insOrderId;

    /**商业险暂存单ID或者报价ID*/
    @FieldDoc(des="商业险暂存单ID或者报价ID")
    private String bizTempId;
    /**交强险暂存单ID或者报价ID*/
    @FieldDoc(des="交强险暂存单ID或者报价ID")
    private String efcTempId;
    /**总的暂存单ID或者报价ID*/
    @FieldDoc(des="总的暂存单ID或者报价ID")
    private String totalTempId;
    /**商业险投保单号*/
    @FieldDoc(des="商业险投保单号")
    private String bizProposeNum;
    /**交强险投保单号*/
    @FieldDoc(des="交强险投保单号")
    private String efcProposeNum;
    /**总的投保单号*/
    @FieldDoc(des="总的投保单号")
    private String totalProposeNum;

    /**商业险保单号*/
    @FieldDoc(des="商业险保单号")
    private String bizPolicyCode;
    /**交强险保单号*/
    @FieldDoc(des="交强险保单号")
    private String efcPolicyCode;

    /**总的保单号*/
    @FieldDoc(des="总的保单号")
    private String totalPolicyCode;

    /**总保费*/
    @FieldDoc(des="总保费")
    private BigDecimal totalCharge;

    @FieldDoc(des="备注信息")
    private String remark;

    /*杂项*/
    @FieldDoc(des="杂项")
    private Map misc;


    /**订单创建时间*/
    @FieldDoc(des="创建时间")
    private Date created;
    /**订单更新时间*/
    @FieldDoc(des="更新时间")
    private Date updated;

    /**转保验证码*/
    @FieldDoc(des="转保验证码")
    private Map<String, String> checkCode;

    /**出单网点编码*/
    @FieldDoc(des="出单网点编码")
    private String deptCode;
}
