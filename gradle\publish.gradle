apply plugin: 'maven-publish'

def nexusUrl = "http://192.168.1.251:8081/nexus/content/repositories/releases/"
if (version.endsWith("-SNAPSHOT")) {
    nexusUrl = "http://192.168.1.251:8081/nexus/content/repositories/snapshots/"
}


tasks.register('sourceJar', Jar) {
    from sourceSets.main.allJava
    archiveClassifier = "sources"
}

publishing {
    publications {
        maven(MavenPublication) {
            from(components.java)
            artifact sourceJar
        }
    }
    repositories {
        maven {
            url nexusUrl
            allowInsecureProtocol true
            credentials {
                username System.getProperty('repo.username') ?: "$cheche_nexus_username"
                password System.getProperty('repo.password') ?: "$cheche_nexus_password"
            }
        }
    }
}
