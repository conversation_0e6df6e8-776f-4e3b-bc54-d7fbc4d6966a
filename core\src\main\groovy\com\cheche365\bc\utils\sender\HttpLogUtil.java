package com.cheche365.bc.utils.sender;

import com.cheche365.bc.utils.LogFormatUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class HttpLogUtil {

    private static final String NOTIFICATION_PREFIX = "* ";
    private static final String REQUEST_PREFIX = "> ";

    /**
     * 打印请求日志
     *
     * @param url           请求 url
     * @param header        请求 header
     * @param body          请求 body
     * @param requestMethod 请求 requestMethod （POST GET）
     */
    static void logRequest(String url, Map<String, String> header, Object body, String requestMethod,
                                   Object params, long id) {
        if (StringUtils.isNotBlank(url) && LogFormatUtils.ignoreLog(url)) {
            return;
        }

        StringBuilder sb = new StringBuilder();

        sb.append(NOTIFICATION_PREFIX).append("Client out-bound request ").append(id)
                .append("\n")
                .append(REQUEST_PREFIX).append(" ").append(requestMethod.toUpperCase()).append(": ").append(url);

        sb.append("\n").append(REQUEST_PREFIX).append(" ").append("HEADER:").append(" ");
        if (null != header && !header.isEmpty()) {
            sb.append(header);
        } else {
            sb.append("{}");
        }

        sb.append("\n").append(REQUEST_PREFIX).append(" ").append("PARAMS:").append(" ");
        if (Objects.nonNull(params)) {
            sb.append(LogFormatUtils.logFormat(params.toString()));
        } else {
            sb.append("{}");
        }

        sb.append("\n").append(REQUEST_PREFIX).append(" ").append("BODY:").append(" ");
        if (Objects.nonNull(body) && !"".equals(body)) {
            sb.append(LogFormatUtils.logFormat(body.toString()));
        } else {
            sb.append("{}");
        }

        log.info(sb.toString());
    }


    /**
     * 打印响应日志
     *
     * @param response 响应 CloseableHttpResponse
     * @param result   响应 body
     */
    static void logResponse(CloseableHttpResponse response, Object result, String url, long id) {
        if (StringUtils.isNotBlank(url) && LogFormatUtils.ignoreLog(url)) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(NOTIFICATION_PREFIX).append("Client out-bound response ").append(id);
        if (Objects.nonNull(response)) {
            Header[] allHeaders = response.getAllHeaders();
            sb.append("\n").append(REQUEST_PREFIX).append(" ").append("HEADER:").append(" ");
            if (Objects.nonNull(allHeaders) && allHeaders.length > 0) {
                Map<String, String> collect = Arrays.stream(allHeaders)
                        .collect(Collectors.toMap(
                                Header::getName,
                                Header::getValue,
                                (existingValue, newValue) -> existingValue
                        ));
                sb.append(collect);
            } else {
                sb.append("{}");
            }
            sb.append("\n").append(REQUEST_PREFIX).append(" ").append("BODY:").append(" ");
            if (Objects.nonNull(result)) {
                sb.append(LogFormatUtils.logFormat(result.toString()));
            } else {
                sb.append("{}");
            }
        }
        log.info(sb.toString());
    }
}
