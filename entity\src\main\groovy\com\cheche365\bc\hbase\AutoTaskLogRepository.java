package com.cheche365.bc.hbase;

import com.cheche365.bc.entity.hbase.AutoTaskLog;

import java.io.IOException;
import java.util.List;

/**
 * AutoTaskLog HBase服务接口
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface AutoTaskLogRepository {

    /**
     * 保存任务日志
     *
     * @param autoTaskLog 任务日志对象
     * @throws IOException HBase操作异常
     */
    void save(AutoTaskLog autoTaskLog) throws IOException;

    /**
     * 根据autoTraceId查询任务日志
     *
     * @param autoTraceId 任务ID
     * @return 任务日志对象，如果不存在返回null
     * @throws IOException HBase操作异常
     */
    AutoTaskLog findByAutoTraceId(String autoTraceId) throws IOException;

    /**
     * 根据RowKey查询任务日志
     *
     * @param rowKey RowKey
     * @return 任务日志对象，如果不存在返回null
     * @throws IOException HBase操作异常
     */
    AutoTaskLog findByRowKey(String rowKey) throws IOException;

    /**
     * 检查任务日志是否存在
     *
     * @param autoTraceId 任务ID
     * @return 是否存在
     * @throws IOException HBase操作异常
     */
    boolean exists(String autoTraceId) throws IOException;

    /**
     * 删除任务日志
     *
     * @param autoTraceId 任务ID
     * @throws IOException HBase操作异常
     */
    void delete(String autoTraceId) throws IOException;

    /**
     * 批量保存任务日志
     *
     * @param autoTaskLogs 任务日志列表
     * @throws IOException HBase操作异常
     */
    void batchSave(List<AutoTaskLog> autoTaskLogs) throws IOException;

    /**
     * 根据traceKey查询任务日志
     * 注意：这个方法需要扫描表，性能较低，建议谨慎使用
     *
     * @param traceKey 单号ID
     * @return 任务日志列表
     * @throws IOException HBase操作异常
     */
    List<AutoTaskLog> findByTraceKey(String traceKey) throws IOException;
}
