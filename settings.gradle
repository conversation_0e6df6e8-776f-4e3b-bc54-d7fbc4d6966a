pluginManagement {
    repositories {
        gradlePluginPortal()
        maven {
            url 'https://maven.aliyun.com/repository/public'
        }
    }
}

initProject(rootDir)

def initProject(File dir, def parentName = '') {
    dir.listFiles().findAll {
        it.isDirectory()
    }.forEach {
        def projectName = (parentName ? parentName + ":" : '') + it.name
        if (new File(it, "build.gradle").exists()) {
            include(projectName)
            project(":" + projectName).name = projectName.replaceAll(':', '-')
        }
        initProject(it, projectName)
    }
}