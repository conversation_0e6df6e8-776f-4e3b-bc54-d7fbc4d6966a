package com.cheche365.bc.river.rest.controller;

import com.cheche365.bc.actor.BaseActor;
import com.cheche365.bc.model.RestResponse;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.cheche365.bc.constants.Constants.LOGIN_FAIL_ACCOUNTS;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@RestController
@RequestMapping("/actors")
@Lazy
public class ActorsController {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @RequestMapping(value = "accounts", method = RequestMethod.GET)
    public Map getAccountIllegal(@RequestParam Map<String, String> map) {
        String search = map.get("search") == null ? "" : map.get("search");
        int currentPage = map.get("currentPage") == null ? 1 : Integer.parseInt(map.get("currentPage"));
        int pageSize = map.get("pageSize") == null ? 10 : Integer.parseInt(map.get("pageSize"));
        List<Map<String, String>> data = BaseActor.allActors.keySet().stream()
                .filter(key -> key.contains(search) && key.split("-").length > 1 && key.split("-")[0].matches("[0-9]{4}"))//过滤掉非accountActor,winActor
                .collect(Collectors.groupingBy(key -> {
                            //key comId-account{-winIndex}
                            String[] keys = key.split("-");
                            String comId = keys.length > 0 ? keys[0] : key;
                            String account = "";
                            if (keys.length == 2) {
                                account = keys[1];
                            }
                            if (keys.length > 2) {//帐号中有会"-"
                                int len = keys.length;
                                StringJoiner joiner = new StringJoiner("-");

                                if (keys[keys.length - 1].matches("[1-9][0-9]?")) len = keys.length - 1;//最后一个为winIndex
                                for (int i = 1; i < len; i++) joiner.add(keys[i]);
                                account = joiner.toString();
                            }
                            return comId + "-" + account;
                        }, Collectors.counting()
                )).entrySet().stream().map(entry -> {
                    Map<String, String> pairs = Maps.newHashMap();
                    String[] keys = entry.getKey().split("-");

                    String comId = keys.length > 0 ? keys[0] : entry.getKey();
                    String account;
                    String winCount = entry.getValue() + "";

                    StringJoiner joiner = new StringJoiner("-");
                    for (int i = 1; i < keys.length; i++) joiner.add(keys[i]);
                    account = joiner.toString();

                    pairs.put("comId", comId);
                    pairs.put("account", account);
                    pairs.put("winCount", winCount);
                    return pairs;
                }).collect(Collectors.toList());

        Map<String, Object> result = Maps.newHashMap();
        result.put("total", data.size());
        result.put("restCode", 1);

        data = data.subList((currentPage - 1) * pageSize, Math.min(currentPage * pageSize, data.size()));
        for (Map<String, String> e : data) {
            String illegal = redisTemplate.opsForValue().get(LOGIN_FAIL_ACCOUNTS + e.get("comId") + "-" + e.get("account"));
            e.put("illegal", illegal);
        }
        result.put("current", currentPage);
        result.put("size", pageSize);
        result.put("rows", data);
        return result;
    }

    @RequestMapping(value = "kill", method = RequestMethod.POST)
    public RestResponse killWinActor(@RequestBody Map<String, String> param) {
        String actorName = param.get("comId") + "-" + param.get("account");
        redisTemplate.convertAndSend("killWin", actorName);
        return RestResponse.successMessage("akka actor关闭成功!");
    }

    @RequestMapping(value = "clearIllegal", method = RequestMethod.POST)
    public RestResponse clearIllegal(@RequestBody Map<String, String> param) {
        redisTemplate.opsForValue().set(LOGIN_FAIL_ACCOUNTS + param.get("comId") + "-" + param.get("account"), "0");
        return RestResponse.successMessage("重置登录失败次数成功!");
    }
}

