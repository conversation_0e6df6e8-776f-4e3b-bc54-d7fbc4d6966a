package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.mysql.ErrorCategory;
import com.cheche365.bc.mapper.ErrorCategoryMapper;
import com.cheche365.bc.service.ErrorCategoryService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Service
public class ErrorCategoryServiceImpl extends ServiceImpl<ErrorCategoryMapper, ErrorCategory> implements ErrorCategoryService {

    @Override
    public ErrorCategory errorCategoryMatch(String errorDesc) {
        return getOne(Wrappers.<ErrorCategory>query()
                        .eq("isEffective", 1)
                        .like("keyword", errorDesc)
                        .orderByDesc("length(keyword)")
                , false);
    }
}
