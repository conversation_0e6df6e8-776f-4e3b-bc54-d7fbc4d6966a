package com.cheche365.bc.admin.rest.security;

import com.cheche365.bc.admin.service.service.MenuInfoService;
import com.cheche365.bc.admin.service.constant.SecurityConstants;
import com.cheche365.bc.entity.mysql.MenuInfo;
import com.cheche365.bc.entity.mysql.RoleInfo;
import com.cheche365.bc.entity.mysql.RolePermission;
import com.cheche365.bc.entity.mysql.UserInfo;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.entity.enums.LoginPermission;
import com.cheche365.bc.entity.enums.MenuInfoStatus;
import com.cheche365.bc.admin.service.service.RolePermissionService;
import com.cheche365.bc.admin.service.service.UserInfoService;
import com.cheche365.bc.admin.service.service.UserRoleService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 登录验证
 * Created by luocong on 2018/12/11.
 */
@Service
public class SecurityUserService implements UserDetailsService {

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private RolePermissionService rolePermissionService;

    @Autowired
    private MenuInfoService menuInfoService;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        // 通过email获取用户信息
        UserInfo userInfo = userInfoService.getUserInfoByEmail(email, CommStatus.ENABLE.getCode());

        if (userInfo == null) {
            throw new UsernameNotFoundException(SecurityConstants.LOGIN_ERROR_MESSAGE);
        }
        if (userInfo.getLoginPermission() == LoginPermission.ONLINE || userInfo.getLoginPermission() == LoginPermission.ON_OFF_LINE) {
            return buildUserForAuthentication(userInfo);
        } else {
            throw new BadCredentialsException(SecurityConstants.AUTHENTICATION_ERROR_MESSAGE);
        }
    }

    private ManageCommonSecurityUser buildUserForAuthentication(UserInfo userInfo) {
        boolean enabled = true;
        boolean accountNonExpired = true;
        boolean credentialsNonExpired = true;
        boolean accountNonLocked = true;
        List<GrantedAuthority> authorityList = buildUserAuthority(userInfo);
        List<MenuInfo> menuInfoList = menuInfoService.listMenuByUserId(userInfo.getId(), MenuInfoStatus.ENABLE);

        // 验证是否为首次登陆或密码不符合安全规则
        if (userInfo.getFirstLogin()) {
            userInfo.setChangePassword(true);
        }

        return new ManageCommonSecurityUser(userInfo.getEmail(), userInfo.getPassword(), enabled, accountNonExpired,
                credentialsNonExpired, accountNonLocked, authorityList, userInfo, menuInfoList);
    }

    private List<GrantedAuthority> buildUserAuthority(UserInfo userInfo) {
        List<GrantedAuthority> authorityList = new ArrayList<>();
        List<RoleInfo> roleCodeList = userRoleService.listRoleCodeByUserInfo(userInfo.getId(), CommStatus.ENABLE.getCode());
        if (CollectionUtils.isEmpty(roleCodeList)) {
            throw new BadCredentialsException(SecurityConstants.AUTHENTICATION_ERROR_MESSAGE);
        }
        //取角色下所有权限
        roleCodeList.forEach(roleInfo -> {
            if (StringUtils.isNotBlank(roleInfo.getCode())) {
                authorityList.add(new SimpleGrantedAuthority(SecurityConstants.PREFIX_ROLE + roleInfo.getCode()));
            }
            List<RolePermission> rolePermissions =
                    rolePermissionService.listRolePermissionByRoleId(roleInfo.getId(), SecurityConstants.ROLE_CODE_ADMIN.equals(roleInfo.getCode()));
            rolePermissions.forEach(rolePermission -> authorityList.add(
                    new SimpleGrantedAuthority(String.format("%s-%s", rolePermission.getMenuCode(), rolePermission.getPermissionCode()))));
        });
        return authorityList;
    }
}
