package com.cheche365.bc.admin.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.UserInfoService;
import com.cheche365.bc.admin.service.service.UserLogService;
import com.cheche365.bc.admin.service.service.UserRoleService;
import com.cheche365.bc.admin.service.constant.UserLogConstants;
import com.cheche365.bc.entity.mysql.UserInfo;
import com.cheche365.bc.entity.mysql.UserRole;
import com.cheche365.bc.entity.enums.CommStatus;
import com.cheche365.bc.entity.enums.LoginPermission;
import com.cheche365.bc.entity.enums.OperateContent;
import com.cheche365.bc.mapper.UserInfoMapper;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.utils.CommonUtils;
import com.cheche365.bc.utils.encrypt.MD5;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@Service
@AllArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    private final UserRoleService userRoleService;
    private final UserLogService userLogService;

    @Override
    public UserInfo getUserInfoByEmail(String email, int status) {

        if (StringUtils.isNotEmpty(email)) {

            Map<String, Object> params = new HashMap<>(2);
            params.put("email", email);
            params.put("status", status);

            return baseMapper.getUserInfoByEmail(params);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse saveOrUpdateUser(UserInfo userInfo, UserInfo operateUser) {
        //判断是否为更新操作
        boolean isUpdate = false;
        if (userInfo.getId() != null) {
            isUpdate = true;
        }

        if (isUpdate) {
            userInfo.setUpdateTime(LocalDateTime.now());
        } else {
            userInfo.setCreateUserId(operateUser.getId());
            userInfo.setCreateUserName(operateUser.getName());
            userInfo.setCreateTime(LocalDateTime.now());
            userInfo.setUpdateTime(LocalDateTime.now());
        }
        if (StringUtils.isNotEmpty(userInfo.getPassword())) {
            userInfo.setPassword(MD5.toHex(userInfo.getPassword()));
        }

        if (StringUtils.isEmpty(userInfo.getEmail())) {
            return RestResponse.failedMessage("用户名不能为空!");
        }
        if (!isUpdate && StringUtils.isEmpty(userInfo.getPassword())) {
            return RestResponse.failedMessage("用户密码不能为空!");
        }
        if (CollectionUtils.isEmpty(userInfo.getRoleIds())) {
            return RestResponse.failedMessage("用户角色不能为空");
        }

        UserInfo userInfoExists = baseMapper.selectOne(new QueryWrapper<UserInfo>().eq("email", userInfo.getEmail()));
        if (!isUpdate) {
            if (userInfoExists != null) {
                return RestResponse.failedMessage("用户名已存在!");
            }
        } else {
            if (userInfoExists != null && userInfoExists.getId().longValue() != userInfo.getId().longValue()) {
                return RestResponse.failedMessage("用户名已存在!");
            }
        }

        saveOrUpdateUserRef(userInfo, isUpdate, operateUser);
        return RestResponse.success();
    }

    private void saveOrUpdateUserRef(UserInfo userInfo, boolean isUpdate, UserInfo operateUser) {
        if (isUpdate) {
            //删除用户角色
            userRoleService.remove(new QueryWrapper<UserRole>().lambda().eq(UserRole::getUser, userInfo.getId()));
            UpdateWrapper<UserInfo> updateWrapper = new UpdateWrapper();
            updateWrapper.lambda()
                    .eq(UserInfo::getId, userInfo.getId())
                    .set(UserInfo::getEmail, userInfo.getEmail())
                    .set(UserInfo::getName, userInfo.getName())
                    .set(UserInfo::getStatus, userInfo.getStatus())
                    .set(UserInfo::getLoginPermission, userInfo.getLoginPermission())
                    .set(UserInfo::getOrgId, userInfo.getOrgId())
                    .set(UserInfo::getOrgCode, userInfo.getOrgCode())
                    .set(UserInfo::getOrgLevel, userInfo.getOrgLevel())
                    .set(UserInfo::getOrgName, userInfo.getOrgName())
                    .set(UserInfo::getUpdateTime, userInfo.getUpdateTime());
            if (StringUtils.isNotEmpty(userInfo.getPassword())) {
                updateWrapper.lambda().set(UserInfo::getPassword, userInfo.getPassword());
            }
            update(updateWrapper);
        } else {
            save(userInfo);
        }

        //记录日志
        OperateContent operateContent = isUpdate ? OperateContent.EDIT : OperateContent.ADD;
        String template = isUpdate ? UserLogConstants.USER_INFO_EDIT : UserLogConstants.USER_INFO_ADD;
        userLogService.saveUserLog(userInfo, operateUser, operateContent,
                UserLogConstants.getUserLogNote(template, operateUser.getName(), userInfo.getEmail(), JSONObject.toJSONString(userInfo)));

        //用户-角色关联表
        if (CollectionUtils.isNotEmpty(userInfo.getRoleIds())) {
            List<UserRole> userRoleList = userInfo.getRoleIds().stream().map(it -> {
                UserRole userRole = new UserRole();
                userRole.setUser(userInfo.getId());
                userRole.setRole(it);
                return userRole;
            }).collect(Collectors.toList());
            userRoleService.saveBatch(userRoleList);
        }

        //线上权限
        if (userInfo.getLoginPermission() == LoginPermission.ONLINE) {
            return;
        }


    }

    @Override
    public void changeStatus(Long id) {
        UserInfo userInfo = baseMapper.selectOne(new QueryWrapper<UserInfo>().eq("id", id));
        if (userInfo.getStatus() == CommStatus.ENABLE) {
            userInfo.setStatus(CommStatus.DISABLED);
        } else {
            userInfo.setStatus(CommStatus.ENABLE);
        }
        baseMapper.updateById(userInfo);
    }

    @Override
    public RestResponse updatePassword(JSONObject object, String userName) {
        String oldPassword = object.getString("oldPassword");
        String newPassword = object.getString("newPassword");
        String confirmPassword = object.getString("confirmPassword");

        if (CommonUtils.isEmpty(oldPassword) || CommonUtils.isEmpty(newPassword) || CommonUtils.isEmpty(confirmPassword)) {
            return RestResponse.failedMessage("请输入密码!");
        }

        if (!newPassword.equals(confirmPassword)) {
            return RestResponse.failedMessage("新密码与确认密码不一致!");
        }
        UserInfo userInfo = baseMapper.selectOne(new QueryWrapper<UserInfo>()
                .eq("email", userName)
                .eq("password", MD5.toHex(oldPassword)));

        if (userInfo == null) {
            return RestResponse.failedMessage("密码不正确!");
        }
        userInfo.setPassword(MD5.toHex(newPassword));
        userInfo.setFirstLogin(false);
        baseMapper.updateById(userInfo);
        return RestResponse.successMessage("密码更新成功");
    }

    @Override
    public List<UserInfo> pageList(QueryWrapper<UserInfo> wrapper, Map<String, String> map, Page<UserInfo> page) {
        return baseMapper.pageList(page, wrapper, map);
    }

    @Override
    public UserInfo getUserInfoById(Long userId) {
        UserInfo userInfo = baseMapper.selectById(userId);
        if (userInfo == null) {
            return null;
        }
        Map<String, Object> resultMap = baseMapper.getUserInfoRefById(userInfo.getId());
        return setFieldByReflect(resultMap, userInfo);
    }

    /**
     * 反射设置userInfo 中的 idList
     *
     * @param fieldMap：Map<属性名, 属性值><
     * @param userInfo:         反射对象
     * @return
     */
    private UserInfo setFieldByReflect(Map<String, Object> fieldMap, UserInfo userInfo) {
        if (CollectionUtils.isEmpty(fieldMap.entrySet())) {
            return userInfo;
        }

        fieldMap.entrySet().stream().forEach(it -> {
            try {
                Field field = userInfo.getClass().getDeclaredField(it.getKey());
                Object FieldVal = it.getValue();
                if (field != null && FieldVal != null && StringUtils.isNotEmpty(FieldVal.toString())) {
                    List<Long> idList = transStr2Long(Arrays.asList(FieldVal.toString().split(",")));
                    field.setAccessible(true);
                    field.set(userInfo, idList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return userInfo;
    }

    private List<Long> transStr2Long(List<String> sourceList) {
        List<Long> targetList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return targetList;
        }
        targetList = sourceList.stream().map(it -> Long.parseLong(it)).collect(Collectors.toList());
        return targetList;
    }

}
