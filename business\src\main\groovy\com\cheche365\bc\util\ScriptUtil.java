package com.cheche365.bc.util;

import com.cheche365.bc.config.SpringUtil;
import com.cheche365.bc.entity.mysql.Interface;
import groovy.lang.Binding;
import groovy.util.GroovyScriptEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;

import java.util.Map;

@Slf4j
public class ScriptUtil {

    private static final GroovyScriptEngine scriptEngine;

    static {
        ObjectProvider<GroovyScriptEngine> beanProvider = SpringUtil.getApplicationContext().getBeanProvider(GroovyScriptEngine.class, true);
        scriptEngine = beanProvider.getIfAvailable();
    }

    public static String engine(String key, Interface itf, Map param) throws Exception {
        return engineWithTemplateName(String.format("%s/%s/%s.groovy", itf.getComCode(), itf.getIntType(), key), param);
    }

    public static String engineWithTemplateName(String templateName, Map param) throws Exception {
        Object obj = scriptEngine.run(templateName, new Binding(param));
        return obj != null ? String.valueOf(obj).trim() : "";
    }
}
