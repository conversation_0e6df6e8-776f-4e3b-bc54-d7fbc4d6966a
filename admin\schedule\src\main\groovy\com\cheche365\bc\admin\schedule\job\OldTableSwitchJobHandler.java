package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cheche365.bc.service.util.TableSwitchUtil;
import com.cheche365.bc.sharding.OldTableSwitch;
import com.cheche365.bc.sharding.QueryTableSwitch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 表切换Job处理器
 * 支持双开关控制：OldTableSwitch（双写控制）+ QueryTableSwitch（查询控制）
 *
 * 参数格式：
 * 1. 简单模式（向后兼容）：传入 "true" 或 "false" 只控制双写开关
 * 2. JSON模式：{"oldTable": true, "queryTable": false, "action": "target"}
 * 3. 预设模式：传入 "initial"、"target"、"complete" 等预设状态
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "oldTableSwitchJobHandler")
@AllArgsConstructor
@Slf4j
public class OldTableSwitchJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            log.info("开始执行表切换Job，参数: {}", param);

            // 记录切换前状态
            String beforeStatus = TableSwitchUtil.getCurrentSwitchStatus();
            log.info("切换前状态: {}", beforeStatus);

            // 解析参数并执行切换
            String result = parseAndExecute(param);

            // 记录切换后状态
            String afterStatus = TableSwitchUtil.getCurrentSwitchStatus();
            log.info("切换后状态: {}", afterStatus);

            return new ReturnT<>(ReturnT.SUCCESS_CODE, result);

        } catch (Exception e) {
            log.error("表切换Job执行失败，参数: {}", param, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 解析参数并执行相应的切换操作
     */
    private String parseAndExecute(String param) {
        if (StrUtil.isBlank(param)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        param = param.trim();

        // 1. 预设模式
        if (isPresetMode(param)) {
            return executePresetMode(param);
        }

        // 2. JSON模式
        if (isJsonMode(param)) {
            return executeJsonMode(param);
        }

        // 3. 简单模式（向后兼容）
        return executeSimpleMode(param);
    }

    /**
     * 检查是否为预设模式
     */
    private boolean isPresetMode(String param) {
        return "initial".equalsIgnoreCase(param) ||
               "target".equalsIgnoreCase(param) ||
               "complete".equalsIgnoreCase(param) ||
               "rollback".equalsIgnoreCase(param);
    }

    /**
     * 检查是否为JSON模式
     */
    private boolean isJsonMode(String param) {
        return param.startsWith("{") && param.endsWith("}");
    }

    /**
     * 执行预设模式
     */
    private String executePresetMode(String preset) {
        switch (preset.toLowerCase()) {
            case "initial":
            case "rollback":
                TableSwitchUtil.rollbackToInitialState();
                return "已设置为初始状态：双写开启，查询旧表";

            case "target":
                TableSwitchUtil.setTargetMigrationState();
                return "已设置为目标状态：双写开启，查询新表";

            case "complete":
                TableSwitchUtil.setFullyMigratedState();
                return "已设置为完全迁移状态：双写关闭，查询新表";

            default:
                throw new IllegalArgumentException("不支持的预设模式: " + preset);
        }
    }

    /**
     * 执行JSON模式
     */
    private String executeJsonMode(String jsonParam) {
        try {
            JSONObject json = JSONUtil.parseObj(jsonParam);

            Boolean oldTable = json.getBool("oldTable");
            Boolean queryTable = json.getBool("queryTable");
            String action = json.getStr("action");

            // 如果指定了action，优先使用action
            if (StrUtil.isNotBlank(action)) {
                return executePresetMode(action);
            }

            // 否则根据具体的开关值设置
            if (oldTable != null) {
                OldTableSwitch.setUseOldTable(oldTable);
            }
            if (queryTable != null) {
                QueryTableSwitch.setUseOldTableForQuery(queryTable);
            }

            return String.format("已设置开关状态 - 双写开关: %s, 查询开关: %s",
                oldTable != null ? (oldTable ? "开启" : "关闭") : "未变更",
                queryTable != null ? (queryTable ? "旧表" : "新表") : "未变更");

        } catch (Exception e) {
            throw new IllegalArgumentException("JSON参数解析失败: " + e.getMessage());
        }
    }

    /**
     * 执行简单模式（向后兼容）
     */
    private String executeSimpleMode(String param) {
        boolean flag = Convert.toBool(param);
        OldTableSwitch.setUseOldTable(flag);
        return String.format("已设置双写开关为: %s（简单模式，查询开关未变更）", flag ? "开启" : "关闭");
    }
}
