package com.cheche365.bc.rest.controller

import com.alibaba.fastjson.JSON
import com.cheche365.bc.message.Response
import com.cheche365.bc.exception.FlowException
import com.cheche365.bc.service.dto.ClaimsCallbackReq
import com.cheche365.bc.service.impl.claim.InsCallbackService
import com.cheche365.bc.service.util.RSAUtil
import groovy.json.JsonOutput
import groovy.json.StringEscapeUtils
import groovy.transform.TupleConstructor
import groovy.util.logging.Slf4j
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*


import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE

/**
 * 推修 controller
 */
@RequestMapping(
    value = '/api/push-repair',
    produces = APPLICATION_JSON_VALUE
)
@Validated
@Slf4j
@RestController
@TupleConstructor(defaults = false, includeFields = true)
class PushController {

    private InsCallbackService insCallbackService;


    /**
     * 提供给保险公司回调接口，所有保司以车车定义的数据结构为准
     * @param companyId
     * @param taskType
     * @param callbackInfo
     * @return
     */
    @PostMapping('/{companyId}/{taskType}')
    @Valid
    def callbackForService(
        @Pattern(regexp = '\\d{3,5}', message = '保司的ID必须为四位数字')
        @PathVariable('companyId') String companyId,
        @Pattern(regexp = '[a-zA-z-_]{3,30}', message = '回调类型必须为字母')
        @PathVariable('taskType') String taskType,
        @RequestBody Map<String, Object> callbackInfo
    ) {
        log.info('companyId:{}, taskType:{}, callbackInfo:{} 收到（理赔）推送修数据，正在处理.......',
            companyId, taskType, StringEscapeUtils.unescapeJava(JsonOutput.toJson(callbackInfo)))
        String claimsNo = ""
        if ("2011".equals(companyId) && "service".equals(taskType)) {
            String decryptData = RSAUtil.decrypt(callbackInfo.get("data").toString());
            String decodeData = URLDecoder.decode(decryptData, "UTF-8");
            callbackInfo.put("data", decodeData);
            claimsNo = JSON.parseObject(decodeData).get("caseNo");

            try {
                insCallbackService.execute(
                    new ClaimsCallbackReq(
                        companyId: companyId,
                        taskType: taskType,
                        claimsNo: claimsNo,
                        callbackInfo: callbackInfo
                    )
                )
                ['resultCode': 1]
            } catch (FlowException e) {
                ['resultCode': 0, 'message': e.message]
            }
        } else {
            new Response(200, insCallbackService.execute(
                new ClaimsCallbackReq(
                    companyId: companyId,
                    taskType: taskType,
                    claimsNo: claimsNo,
                    callbackInfo: callbackInfo
                )), '执行成功！')

        }
    }
}
