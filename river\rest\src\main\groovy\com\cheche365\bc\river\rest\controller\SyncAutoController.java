package com.cheche365.bc.river.rest.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.enums.CallbackStatusEnum;
import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.message.Reply;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.TemplateService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.ErrorInfoKeys;
import com.cheche365.bc.task.KeepSessionConfig;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static com.cheche365.bc.utils.sender.HttpSender.buildHttpClient;

@Slf4j
@RestController
@Lazy
public class SyncAutoController {

    @Resource
    private AutoTaskService taskService;
    @Resource
    private InterfaceService interfaceService;
    @Resource
    private TemplateService templateService;

    /**
     * 未定义Vo类型说明：
     * 1.百川auto接口历史有余；百川是通过taskType、companyId分发到具体的保司模板的，requestData依赖于具体的taskType实现功能，无法定义
     * 2.百川模板项目（river_template）使用groovy，groovy脚本大量使用[:]，不定义Vo，更灵活
     * <p>
     * 以下参数只适用于pdf识别保单功能，其他使用auto需更改taskType、requestData
     *
     * @param taskApply : {
     *                  "enquiryId": "2023031400015661@2016", // 流水号 + 保司ID
     *                  "taskType": "approvedQueryByPdf", // 请求类型 双方约定 比如：报价
     *                  "companyId": "2016", // 请求保司
     *                  "requestData":
     *                  {
     *                  "efcPolicyUrl": "交强险保单地址",
     *                  "bizPolicyUrl": "商业险保单地址",
     *                  "nonMotorPolicyUrl": "非车险保单地址"
     *                  }, // 请求数据
     *                  "client": "车生态_小鹏C端", // 来源
     *                  "processType": "edi" // 精灵/edi
     *                  }
     * @return {
     * "status": "0", // 0 成功  1 失败
     * "message": "", // 错误信息
     * "resultData": {}
     * }
     */
    @RequestMapping(value = "/syncAuto", method = RequestMethod.POST)
    public Object auto(@RequestBody JSONObject taskApply) {

        if (validatedReqParam(taskApply) != null) {
            return validatedReqParam(taskApply);
        }
        log.info("syncAuto 收到请求:{}", taskApply.toJSONString());
        AutoTask autoTask = null;
        try {
            autoTask = buildAutoTask(taskApply);
            taskService.save(autoTask);
            process(autoTask);
            taskService.updateById(autoTask);
        } catch (Exception e) {
            log.error("enquiryId：{}，系统异常：{} ", taskApply.getString("enquiryId"), ExceptionUtils.getStackTrace(e));
            return Reply.reply("1", "系统异常", (Object) e.getMessage());
        }
        return Reply.reply("0", "调用成功，尽情享受吧！", JSON.parseObject(autoTask.getFeedbackJson(), Map.class));
    }

    private void process(AutoTask task) throws Exception {
        //装载接口
        Interface itf = interfaceService.getInterface(task, task.getTaskType());
        if (Objects.isNull(itf))
            throw new Exception("自动化服务暂无能力处理该任务");
        //加载模板
        templateService.loadTemplate(itf, task);

        try {
            //执行模板
            AkkaTaskUtil.process(itf, task);
        } catch (Exception e) {
            task.setEndFlag(true);
            task.setResultFlag(false);
            if (Strings.isNullOrEmpty(task.getTaskStatus())) {
                task.setTaskStatus(itf.getDefaultFailedStatus());
            }
            log.error("enquiryId：{}，执行接口流程发生异常：{} ", task.getTraceKey(), ExceptionUtils.getStackTrace(e));
            if (!task.getErrorInfo().containsKey(ErrorInfoKeys.ERROR_CODE)) {
                task.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
                task.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, e.getMessage());
            }
            task.setConcatResultStr(e.getMessage());
            task.setFailureCause(e.getMessage());
        }
        task.setResultFlag(true);
        task.setEndFlag(true);
        task.setEndTime(LocalDateTime.now());
        if (Strings.isNullOrEmpty(task.getTaskStatus()) || !TaskStatus.isCallbackStatus(task.getTaskStatus())) {
            if (!DataUtil.containsKey((Map) task.getTaskEntity(), "enquiry.taskStatus")) {
                task.setTaskStatus(itf.getDefaultSuccessStatus());
            } else {
                task.setTaskStatus((String) DataUtil.get("enquiry.taskStatus", task.getTaskEntity()));
            }
        }
        task.setCallbackStatus(CallbackStatusEnum.NO_REQUIRED.getCode());
        task.setFeedbackJson(JSON.toJSONString(AkkaTaskUtil.generateCallbackHttpEntity(task)));
        task.setConcatResultStr("。此操作为同步操作，无需回写。为适应百川任务状态显示：回写成功！");
    }

    @SuppressWarnings("unchecked")
    private AutoTask buildAutoTask(JSONObject taskApply) throws Exception {
        AutoTask task = new AutoTask();
        task.setStartTime(LocalDateTime.now());
        if (taskApply.containsKey("requestSource")) {
            com.cheche365.bc.task.AutoTask.RequestSource tRequestSource = new AutoTask.RequestSource();
            BeanUtil.copyProperties(taskApply.getJSONObject("requestSource"), tRequestSource);
            task.setRequestSource(tRequestSource);
        }
        task.setAutoTraceId(UUID.randomUUID().toString());
        task.setTraceKey(taskApply.getString("enquiryId").split("@")[0]);
        task.setCompanyId(taskApply.getString("companyId"));
        task.setTaskType(taskApply.getString("processType").concat("-").concat(StringUtils.left(task.getCompanyId(), 4)).concat("-").concat(taskApply.getString("taskType")));
        JSONObject taskApplyCopy = JSONObject.parseObject(taskApply.toJSONString());
        taskApplyCopy.remove("requestData");
        task.setConcatResultStr(taskApplyCopy.toJSONString());
        task.setApplyJson(taskApply.getJSONObject("requestData").toJSONString());
        // TODO 第一版syncAuto接口，暂时只支持edi，以下为构建edi enquiry httpClient
        Map<String, Object> datasource = Maps.newHashMap();
        datasource.put("enquiry", DataUtil.parseInEdiInterface(taskApply.getJSONObject("requestData").toJSONString()));
        task.setTaskEntity(datasource);
        Map<String, Object> map = new HashMap<>(2);
        map.put("enquiryId", task.getTraceKey().concat("@").concat(task.getCompanyId()));
        task.setTempValues(map);
        task.getTempValues().putAll(taskApplyCopy);
        Map<String, Object> configMap = task.getConfigs();
        if (DataUtil.containsKey((Map) task.getTaskEntity(), "enquiry.configInfo.configMap")) {
            configMap.putAll((Map<String, Object>) DataUtil.get("enquiry.configInfo.configMap", task.getTaskEntity()));
        }
        Interface itf = interfaceService.getInterface(task, task.getTaskType());
        if (Objects.isNull(itf)) {
            throw new Exception("自动化服务暂无能力处理该任务");
        }
        if (itf.getUseTaskConfig()) {
            Map iftConfig = itf.loadConfig();
            if (iftConfig != null)
                configMap.putAll(iftConfig);
        }
        //设置HttpClient
        CloseableHttpClient closeableHttpClient = null;
        boolean useSSL = Objects.nonNull(configMap.get("useNewSSL")) && "true".equals(configMap.get("useNewSSL").toString());
        if (configMap.containsKey(HttpSender.proxyHost)
            && configMap.containsKey(HttpSender.proxyPort)
            && configMap.containsKey(HttpSender.proxyName)
            && configMap.containsKey(HttpSender.proxyPass)) {
            closeableHttpClient = buildHttpClient(true, configMap.get(HttpSender.proxyHost).toString(), Integer.parseInt(configMap.get(HttpSender.proxyPort).toString()), configMap.get(HttpSender.proxyName).toString(), configMap.get(HttpSender.proxyPass).toString(), useSSL ? HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}) : null);
        } else {
            boolean usePool = Boolean.parseBoolean(configMap.getOrDefault(KeepSessionConfig.USE_POOL_CONNECT, "true").toString());
            if (useSSL) {
                try {
                    closeableHttpClient = buildHttpClient(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}));
                } catch (Exception e) {
                    log.error("EDI初始化TLSv1.2类型HttpClient失败：{}", e.getMessage());
                    closeableHttpClient = buildHttpClient(usePool);
                }
            } else {
                closeableHttpClient = HttpSender.buildHttpClient(usePool);
            }
        }
        task.setHttpClient(closeableHttpClient);
        return task;
    }


    private Map validatedReqParam(JSONObject taskApply) {
        if (Strings.isNullOrEmpty(taskApply.getString("enquiryId"))) {
            return Reply.reply("1", "参数缺少关键字段enquiryId", (Object) null);
        }
        if (Strings.isNullOrEmpty(taskApply.getString("taskType"))) {
            return Reply.reply("1", "参数缺少关键字段taskType", (Object) null);
        }
        if (Strings.isNullOrEmpty(taskApply.getString("companyId"))) {
            return Reply.reply("1", "参数缺少关键字段companyId", (Object) null);
        }
        if (Strings.isNullOrEmpty(taskApply.getString("requestData"))) {
            return Reply.reply("1", "参数缺少关键字段requestData", (Object) null);
        }
        if (Strings.isNullOrEmpty(taskApply.getString("client"))) {
            return Reply.reply("1", "参数缺少关键字段client", (Object) null);
        }
        if (Strings.isNullOrEmpty(taskApply.getString("processType"))) {
            return Reply.reply("1", "参数缺少关键字段processType", (Object) null);
        }
        return null;
    }

}
