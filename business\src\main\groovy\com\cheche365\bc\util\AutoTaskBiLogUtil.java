package com.cheche365.bc.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.config.SpringUtil;
import com.cheche365.bc.dto.AutoTaskBiLogSchema;
import com.cheche365.bc.service.AutoTaskExceptionConfigService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.Util;
import com.cheche365.bc.utils.VehicleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Optional;

import static com.cheche365.bc.constants.Constants.NON_MOTORS;
import static com.cheche365.bc.message.TaskType.APPROVED_QUERY_BY_POLICY;

/**
 * <AUTHOR>
 */

@Slf4j
public class AutoTaskBiLogUtil {

    private static final AutoTaskExceptionConfigService configService;

    static {
        configService = SpringUtil.getApplicationContext().getBean(AutoTaskExceptionConfigService.class);
    }

    public static AutoTaskBiLogSchema getAutoTaskBiLogSchema(AutoTask task) {
        try {
            AutoTaskBiLogSchema schema = new AutoTaskBiLogSchema();
            //填充错误信息
            fillErrorInfo(task, schema);
            //填充基本信息
            BeanUtil.copyProperties(task, schema, CopyOptions.create().setIgnoreNullValue(true));
            BeanUtil.copyProperties(task.getRequestSource(), schema, CopyOptions.create().setIgnoreNullValue(true));
            schema.setCompanyId(task.getInsId().toString());
            schema.setProcessType(task.getProcessType());
            schema.setTaskType(task.getTaskTypeName());
            schema.setEventStartTime(task.getStartTime().atZone(ZoneId.systemDefault())
                .toInstant().toEpochMilli());
            schema.setEventEndTime(task.getEndTime().atZone(ZoneId.systemDefault())
                .toInstant().toEpochMilli());

            String jsonParam = Objects.nonNull(schema.getErrorType()) ? task.getApplyJson() : task.getFeedbackJson();
            if (Objects.nonNull(jsonParam) && !APPROVED_QUERY_BY_POLICY.code.equals(task.getTaskTypeName())) {
                return getApplyAndBackParam(jsonParam, schema);
            } else {
                return schema;
            }
        } catch (Exception e) {
            log.error("BI 信息AutoTaskBiLog初始化异常：{}", ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    private static void fillErrorInfo(AutoTask task, AutoTaskBiLogSchema schema) {
        String errorMsg = task.getErrorMsg();
        if (StringUtils.isNotBlank(errorMsg)) {
            String translateException = configService.findExceptionConversion(
                task.getInsId().toString(),
                task.getProcessType(),
                task.getTaskTypeName(),
                errorMsg
            );
            if (StringUtils.isNotBlank(translateException)) {
                schema.setErrorTransform(translateException);
                schema.setErrorType(2);
            } else {
                schema.setErrorOriginal(errorMsg);
                schema.setErrorType(1);
            }
        }
    }

    private static AutoTaskBiLogSchema getApplyAndBackParam(String jsonParam, AutoTaskBiLogSchema schema) {
        JSONObject finalJsonObject = new JSONObject();
        JSONObject jsonParamObject = JSONObject.parseObject(jsonParam);
        finalJsonObject.putAll(jsonParamObject);
        JSONObject insAreaObject = jsonParamObject.getJSONObject("insArea");
        if (Objects.nonNull(insAreaObject)) {
            finalJsonObject.putAll(insAreaObject);
        }
        JSONObject carInfoObject = jsonParamObject.getJSONObject("carInfo");
        if (Objects.nonNull(carInfoObject)) {
            finalJsonObject.putAll(carInfoObject);
        }
        JSONObject definitionObject = jsonParamObject.getJSONObject("definition");
        if (Objects.nonNull(definitionObject)) {
            finalJsonObject.putAll(definitionObject);
        }
        JSONObject sqObject = jsonParamObject.getJSONObject("sq");
        if (Objects.nonNull(sqObject)) {
            finalJsonObject.putAll(sqObject);
            JSONObject nonMotorObject = sqObject.getJSONObject("nonMotor");
            if (Objects.nonNull(nonMotorObject)) {
                finalJsonObject.put("nonMotorCharge", nonMotorObject.get("discountCharge"));
            }

            // 多个非车险
            nonMotorsCharge(sqObject, finalJsonObject);
        }
        BeanUtil.copyProperties(finalJsonObject, schema, CopyOptions.create().setIgnoreNullValue(true).setIgnoreCase(true));
        //加工
        schema.setIsNewEnergy(Util.isNewEnergy(schema.getCarModelName(), schema.getPlateNum()));
        boolean isOutVehicles = false;
        if (StringUtils.isNotBlank(schema.getProvince()) && StringUtils.isNotBlank(schema.getPlateNum()) && !"新车未上牌".equals(schema.getPlateNum())) {
            isOutVehicles = !VehicleUtil.getAlias(schema.getProvince()).equals(schema.getPlateNum().substring(0, 1));
        }
        schema.setIsOutVehicles(isOutVehicles);
        JSONObject carOwnerInfoObject = jsonParamObject.getJSONObject("carOwnerInfo");
        if (checkRelationInfo(carOwnerInfoObject)) {
            String idCard = carOwnerInfoObject.getString("idCard");
            if(Util.checkIdCard(idCard)) {
                schema.setCarOwnerAge(IdcardUtil.getAgeByIdCard(idCard));
            }
            schema.setCarOwnerSex(carOwnerInfoObject.getInteger("sex"));
        }
        JSONObject applicantPersonInfoObject = jsonParamObject.getJSONObject("applicantPersonInfo");
        if (checkRelationInfo(applicantPersonInfoObject)) {
            String idCard = applicantPersonInfoObject.getString("idCard");
            if(Util.checkIdCard(idCard)) {
                schema.setApplicantAge(IdcardUtil.getAgeByIdCard(idCard));
            }
            schema.setApplicantSex(applicantPersonInfoObject.getInteger("sex"));
        }
        //险种
        JSONObject baseSuiteInfoObject = jsonParamObject.getJSONObject("baseSuiteInfo");
        if (Objects.isNull(baseSuiteInfoObject)) {
            return schema;
        }
        JSONObject bizSuiteInfoObject = baseSuiteInfoObject.getJSONObject("bizSuiteInfo");
        if (Objects.nonNull(bizSuiteInfoObject)) {
            JSONArray suiteArray = bizSuiteInfoObject.getJSONArray("suites");
            if (Objects.nonNull(suiteArray)) {
                JSONObject riskObject = new JSONObject();
                suiteArray.forEach(it -> {
                    JSONObject suiteInfo = (JSONObject) it;
                    String code = suiteInfo.getString("code");
                    if (StringUtils.isBlank(code)) {
                        return;
                    }
                    if (suiteInfo.containsKey("discountCharge")) {
                        riskObject.put(code + "Charge", suiteInfo.getBigDecimal("discountCharge"));
                    }
                    if (suiteInfo.containsKey("amount")) {
                        riskObject.put(code + "Amount", suiteInfo.getBigDecimal("amount"));
                    }
                });
                BeanUtil.copyProperties(riskObject, schema, CopyOptions.create().setIgnoreNullValue(true).setIgnoreCase(true));
            }
        }
        return schema;
    }

    static boolean checkRelationInfo(JSONObject target) {
        return Objects.nonNull(target) && "0".equals(target.getString("idCardType"));
    }

    private static void nonMotorsCharge(JSONObject sq, JSONObject jsonObject) {
        Optional.ofNullable(sq.getJSONArray(NON_MOTORS))
            .ifPresent(nonMotors -> {
                BigDecimal totalCharge = nonMotors.stream()
                    .map(item -> new BigDecimal(((JSONObject) item).get("discountCharge").toString()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                jsonObject.put("nonMotorCharge", totalCharge);
            });
    }
}
