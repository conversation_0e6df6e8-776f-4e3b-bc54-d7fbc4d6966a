package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

/**
 * author:<PERSON><PERSON>haoliang
 * Date:2019/9/2 16:32
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum NatureEnum {

    PERSONAL(1, "个人"),
    TEAM(2, "团体法人")
    ;

    @EnumValue
    private final int code;

    private final String text;

    NatureEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    @JsonCreator
    public static NatureEnum get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(null);
    }

    public static NatureEnum get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }

}
