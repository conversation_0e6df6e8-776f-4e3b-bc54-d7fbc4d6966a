# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
trim_trailing_whitespace = true

# Java files
[*.{java,groovy}]
indent_style = space
indent_size = 4
max_line_length = 120

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# XML files
[*.xml]
indent_style = space
indent_size = 4

# Gradle files
[*.gradle]
indent_style = space
indent_size = 4

# Properties files
[*.properties]
indent_style = space
indent_size = 4

# Markdown files
[*.md]
trim_trailing_whitespace = false