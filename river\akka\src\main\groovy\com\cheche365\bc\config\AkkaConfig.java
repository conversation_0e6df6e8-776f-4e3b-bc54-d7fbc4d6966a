package com.cheche365.bc.config;

import akka.actor.ActorSystem;
import com.cheche365.bc.utils.dama.Constant;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import groovy.util.GroovyScriptEngine;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.io.IOException;

/**
 * 创建ActorSystem，并将其放入到spring管理，初始化ApplicationContext
 * <p>
 * <p/>
 * Date 2019年8月8日 下午6:50:20
 * <p/>
 *
 * <AUTHOR>
 */
@Configuration
@Log4j2
class AkkaConfig {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private SpringExtension springExtension;

    @Autowired
    private Environment env;

    @Value("${web.groovy-files}")
    private String filePath;
    ActorSystem actorSystem;

    @Bean
    public ActorSystem actorSystem() {
        try {
            String akkaPath = "akka.conf";
            //创建akka系统
            log.info("加载akka配置,路径:{}", akkaPath);
            Config config = ConfigFactory.load(akkaPath);
            log.info("ActorSystem 初始化...");
            log.info("akka.default-dispatcher 初始化参数 parallelism-min:{}", config.getInt("akka.actor.default-dispatcher.fork-join-executor.parallelism-min"));
            log.info("akka.default-dispatcher 初始化参数 parallelism-factor:{}", config.getInt("akka.actor.default-dispatcher.fork-join-executor.parallelism-factor"));
            log.info("akka.default-dispatcher 初始化参数 parallelism-max:{}", config.getInt("akka.actor.default-dispatcher.fork-join-executor.parallelism-max"));
            //创建akka系统
            actorSystem = ActorSystem.create("sys", config);
            springExtension.initialize(applicationContext);
            //系统启动时预先创建的actor
            actorSystem.actorOf(springExtension.props("taskActor"), "taskActor");
            return actorSystem;
        } catch (Exception e) {
            log.error("ActorSystem 启动失败：", e);
            return null;
        }
    }

    @Bean(initMethod = "init")
    public Constant contant() {
        Constant constant = new Constant();
        constant.setDataTransFormPath(env.getProperty("system.dataTransFormPath"));
        return constant;
    }

    @Bean
    public GroovyScriptEngine scriptEngine() throws IOException {
        // 配置编译器
//        CompilerConfiguration configuration = new CompilerConfiguration();
//
//        //禁用不必要的元数据生成，默认就是 false
//        configuration.setParameters(false);
//        // 禁用调试输出
//        configuration.setDebug(false);
//        // 禁用详细输出
//        configuration.setVerbose(false);
//        configuration.setTargetBytecode(CompilerConfiguration.JDK17);
//        configuration.setSourceEncoding(StandardCharsets.UTF_8.name());
//
//        // 检测脚本最小重新编译间隔，单位为毫秒
//        configuration.setMinimumRecompilationInterval(10000);
//
//        // 启用优化选项
//        Map<String, Boolean> optimizationOptions = new HashMap<>(4);
//        optimizationOptions.put(CompilerConfiguration.PARALLEL_PARSE, true);
//        optimizationOptions.put(CompilerConfiguration.INVOKEDYNAMIC, true);
//        optimizationOptions.put(CompilerConfiguration.RUNTIME_GROOVYDOC, false);
//        optimizationOptions.put(CompilerConfiguration.GROOVYDOC, false);
//        configuration.setOptimizationOptions(optimizationOptions);
//
//        GroovyScriptEngine engine = ;
//        engine.setConfig(configuration);

        return new GroovyScriptEngine(filePath);
    }
}
