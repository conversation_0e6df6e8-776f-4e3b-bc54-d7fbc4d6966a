package com.cheche365.bc.task;

import cn.hutool.core.map.MapUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.MDC;

import java.util.Map;

import static com.cheche365.bc.constants.TraceConstants.TRACE_ID;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MetaData {

    /**
     * traceId
     */
    private String flowId;



    public static MetaData buildByMDC() {
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        if (MapUtil.isEmpty(copyOfContextMap)) {
            return new MetaData();
        }
        return MetaData.builder()
                .flowId(copyOfContextMap.get(TRACE_ID))
                .build();
    }

}
