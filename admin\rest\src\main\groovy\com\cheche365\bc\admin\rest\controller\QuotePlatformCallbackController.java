package com.cheche365.bc.admin.rest.controller;

import com.cheche365.bc.admin.service.dto.callback.QuoteCallback;
import com.cheche365.bc.admin.service.service.BatchTestTaskService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/quote/platform/callback")
@AllArgsConstructor
public class QuotePlatformCallbackController {

    private final BatchTestTaskService batchTestTaskService;

    @PostMapping
    public String callback(@RequestBody QuoteCallback quoteCallback) {
        log.info("收到报价回调 :{}", quoteCallback);
        batchTestTaskService.updateBatchTestTaskDetailStatus(quoteCallback);
        return "success";
    }
}
