package com.cheche365.bc.utils;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.tools.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * javascript工具类
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/4/21 8:45.
 */
@Slf4j
public class JsUtil {

    public final static ConcurrentMap<String, ScriptEngine> caches = new ConcurrentHashMap<>();

    public static Map<String,String> jsFiles;

    public JsUtil() {

    }

    public static boolean putScript(String key, String scriptContent,boolean force) {
        try {
            if(force) {
                ScriptEngineManager manager = new ScriptEngineManager();
                ScriptEngine engine = manager.getEngineByName("javascript");
                engine.eval(scriptContent);
                caches.put(key, engine);
            }
            else
            {
                if(caches.containsKey(key))
                {
                    return false;
                }
                else
                {
                    ScriptEngineManager manager = new ScriptEngineManager();
                    ScriptEngine engine = manager.getEngineByName("javascript");
                    engine.eval(scriptContent);
                    caches.put(key, engine);
                }
            }
            return true;
        } catch (Exception ex) {
            log.error("放入Js脚本时候解析出错", ex);
        }
        return false;
    }

    /**
     * @param key           公司id，脚本的key
     * @param scriptContent 脚本的内容
     * @return 是否成功编译脚本并放入，成功返回True，否则False
     */
    public static boolean putScript(String key, String scriptContent) {
        return putScript(key,scriptContent,false);
    }

    /**
     * @param key     某一个公司的key
     * @param funName 函数名字
     * @param args    参数列表
     * @return 调用函数的返回值
     * @throws Exception
     */
    public static Object exeFun(String key, String funName, Object... args) throws Exception {
        Assert.notNull(key,"保司KEY不能是空");
        Assert.notNull(funName,"funName不能是空");
        if (caches.containsKey(key)) {
            Invocable invocable = (Invocable) caches.get(key);
            return invocable.invokeFunction(funName, args);
        } else {
            throw new IllegalArgumentException("不存在key为" + key + "的脚本");
        }
    }

    public Map<String, String> getJsFiles() {
        return jsFiles;
    }

    public void setJsFiles(Map<String, String> jsFiles) {
        this.jsFiles = jsFiles;
        jsFiles.forEach((k,v)->{
            log.info("开始加载key为{},的脚本文件:{}",k,v);
            boolean flag= JsUtil.putScript(k,FileUtil.getResourceFromStream(v,"utf-8"));
            log.info("加载key为{},的脚本文件完成，结果:{}",k,flag);
        });
    }
}
