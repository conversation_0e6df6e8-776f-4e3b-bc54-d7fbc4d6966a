package com.cheche365.bc.admin.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.entity.mongo.ActionLog;
import com.cheche365.bc.hbase.ActionLogRepository;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.BusinessDataSourceAutoTaskLogService;
import com.cheche365.bc.sharding.OldTableSwitch;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
@RestController
@RequestMapping("/autoTaskLog")
@AllArgsConstructor
public class AutoTaskController extends BaseController<AutoTaskService, AutoTask> {

    private final MongoTemplate mongoTemplate;
    private final BusinessDataSourceAutoTaskLogService businessDataSourceAutoTaskLogService;

    private final ActionLogRepository actionLogRepository;

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTO_TASK_LOG + COMMA + PermissionCode.AUTOTASKLOG_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDate defaultStartTime = LocalDate.now().minusMonths(2).withDayOfMonth(1);
        LocalDateTime defaultEndTime = LocalDateTime.now().with(LocalTime.MAX);

        String startTimeStr = map.get("startTimeStartTime");
        if (StringUtils.isEmpty(startTimeStr)) {
            map.put("startTimeStartTime", defaultStartTime.format(dateFormatter));
        } else {
            LocalDate providedStartTime = LocalDate.parse(startTimeStr, dateFormatter);
            if (providedStartTime.isBefore(defaultStartTime)) {
                map.put("startTimeStartTime", defaultStartTime.format(dateFormatter));
            }
        }

        String endTimeStr = map.get("startTimeEndTime");
        if (StringUtils.isEmpty(endTimeStr)) {
            map.put("startTimeEndTime", defaultEndTime.format(dateTimeFormatter));
        } else {
            LocalDateTime providedEndTime = LocalDateTime.parse(endTimeStr, dateTimeFormatter);
            if (providedEndTime.isAfter(defaultEndTime)) {
                map.put("startTimeEndTime", defaultEndTime.format(dateTimeFormatter));
            }
        }

        Page<AutoTask> page = createPage(map);

        String dataSourceLogId = map.remove("dataSourceId");
        // 获取并设置检索条件
        QueryWrapper<AutoTask> wrapper = new QueryWrapper<>();
        wrapper.select("autoTraceId, traceKey, taskType, company_id, plate_no, task_status, startTime, endTime, actionLogs,resultStr,callback_status").orderByDesc("startTime");
        wrapper = getQueryByWrapper(map, "", wrapper);

        if (StringUtils.isNotEmpty(dataSourceLogId)) {
            int parentId = Integer.parseInt(dataSourceLogId);
            if (parentId != 0) {
                List<Integer> idList = businessDataSourceAutoTaskLogService.getIdListByParentId(parentId);
                wrapper.lambda().in(AutoTask::getDataSourceLogId, idList);
            }
        }

        service.page(page, wrapper);
        return RestResponse.success(page);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTO_TASK_LOG + COMMA + PermissionCode.AUTOTASKLOG_GETPAGE + SUFFIX)
    @GetMapping("getActionLog/{id}")
    public Object getActionLog(@PathVariable String id, Authentication authentication) throws IOException {
        if (OldTableSwitch.isUseOldTable()) {
            return mongoTemplate.findById(id, ActionLog.class);

        }
        return actionLogRepository.findByRowKey(id);
    }

    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTO_TASK_LOG + COMMA + PermissionCode.AUTOTASKLOG_GETPAGE + SUFFIX)
    @GetMapping("getTaskBody/{bodyType}/{autoTraceId}")
    public AutoTask getTaskBody(@PathVariable String bodyType, @PathVariable String autoTraceId, Authentication authentication) {
        return service.getTaskBodyByAutoTraceId(bodyType, autoTraceId);
    }
}
