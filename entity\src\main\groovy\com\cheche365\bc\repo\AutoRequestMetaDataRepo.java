package com.cheche365.bc.repo;

import com.cheche365.bc.entity.mysql.AutoRequestMetaData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AutoRequestMetaDataRepo extends MongoRepository<AutoRequestMetaData, String> {

    AutoRequestMetaData findFirstByEnquiryIdOrderByCreateDateDesc(String enqueueId);
}
