package com.cheche365.bc.admin.schedule.job;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.service.util.TableSwitchUtil;
import com.cheche365.bc.sharding.QueryTableSwitch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 查询表开关Job处理器
 * 专门控制QueryTableSwitch，独立于双写开关
 * 
 * 参数格式：
 * - "true" 或 "old": 查询使用旧表
 * - "false" 或 "new": 查询使用新表+HBase
 * - "status": 查看当前状态
 * 
 * <AUTHOR>
 */
@Component
@JobHandler(value = "queryTableSwitchJobHandler")
@AllArgsConstructor
@Slf4j
public class QueryTableSwitchJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            log.info("开始执行查询表开关Job，参数: {}", param);
            
            if (StrUtil.isBlank(param)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }
            
            param = param.trim().toLowerCase();
            
            // 记录切换前状态
            String beforeStatus = TableSwitchUtil.getCurrentSwitchStatus();
            log.info("切换前状态: {}", beforeStatus);
            
            String result = executeSwitch(param);
            
            // 记录切换后状态
            String afterStatus = TableSwitchUtil.getCurrentSwitchStatus();
            log.info("切换后状态: {}", afterStatus);
            
            return new ReturnT<>(ReturnT.SUCCESS_CODE, result);
            
        } catch (Exception e) {
            log.error("查询表开关Job执行失败，参数: {}", param, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开关切换
     */
    private String executeSwitch(String param) {
        switch (param) {
            case "status":
                return getCurrentStatus();
                
            case "true":
            case "old":
                QueryTableSwitch.setUseOldTableForQuery(true);
                return "已设置查询开关为：使用旧表查询";
                
            case "false":
            case "new":
                QueryTableSwitch.setUseOldTableForQuery(false);
                return "已设置查询开关为：使用新表+HBase查询";
                
            default:
                // 尝试转换为boolean
                try {
                    boolean flag = Convert.toBool(param);
                    QueryTableSwitch.setUseOldTableForQuery(flag);
                    return String.format("已设置查询开关为：%s", flag ? "使用旧表查询" : "使用新表+HBase查询");
                } catch (Exception e) {
                    throw new IllegalArgumentException("不支持的参数: " + param + 
                        "，支持的参数: true/false, old/new, status");
                }
        }
    }

    /**
     * 获取当前状态
     */
    private String getCurrentStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== 查询表开关状态 ===\n");
        status.append("当前查询开关: ").append(QueryTableSwitch.isUseOldTableForQuery() ? "旧表" : "新表+HBase").append("\n");
        status.append("完整状态: ").append(TableSwitchUtil.getCurrentSwitchStatus()).append("\n");
        status.append("迁移阶段: ").append(TableSwitchUtil.getMigrationPhase()).append("\n");
        status.append("==================");
        return status.toString();
    }
}
