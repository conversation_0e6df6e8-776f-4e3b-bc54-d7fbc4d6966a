package com.cheche365.bc.admin.service.external.api;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.cheche365.bc.admin.service.external.dto.TokenResp;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.stereotype.Service;

import javax.ws.rs.HttpMethod;
import java.util.concurrent.TimeUnit;

import static cn.hutool.json.JSONUtil.toJsonStr;

@Service
public class TokenAPI extends BaseQuotePlatformAPI<TokenResp> {

    private static final String _QUOTE_PLATFORM_KEY = "quote_platform_token";

    private static final Cache<String, String> tokenCache = CacheBuilder.newBuilder()
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build();

    @Override
    public Boolean needToken() {
        return false;
    }

    @Override
    public Class<?> responseType() {
        return TokenResp.class;
    }

    @Override
    public String path() {
        return "/partners/token";
    }

    @Override
    public String method() {
        return HttpMethod.POST;
    }

    public String getToken() {
        String cacheToken = getTokenFromCache();
        if (StrUtil.isNotBlank(cacheToken)) {
            return cacheToken;
        }
        return fetchToken();
    }

    private String fetchToken() {
        String token = super.call(
            toJsonStr(
                new JSONObject() {{
                    set("grantType", "app_id");
                }}
            )
        ).getAccessToken();

        Assert.notBlank(token);
        cacheToken(token);
        return token;
    }

    private static void cacheToken(String token) {
        tokenCache.put(_QUOTE_PLATFORM_KEY, token);
    }

    private static String getTokenFromCache() {
        return tokenCache.getIfPresent(_QUOTE_PLATFORM_KEY);
    }

}
