# river_templates 说明
### 模块间依赖关系
    core -> river_templates
    依赖core作用为了本地跑测试用例的目的，在线上当前项目就相当于一个文件夹的作用，目的为了改代码实时生效， 
    原理：利用 jenkins git hook 当提交模板时候触发 git 事件执行jenkins脚本发布git代码到对应文件夹，更新文件夹文件，
          该项目为 ins-auto-service 提供 groovy template 模板，线上项目执行是通过 GroovyScriptEngine 动态执行脚本方式执行代码的。
# ins-auto-service 说明
### 模块间的依赖关系
    core -> entity -> business -> akka -> web
    core: 核心组件包含 entity，model，utils，enums等一系列功能项目，回写实体类，
        CarInfo（车信息），PersonInfo（投被保人信息），BaseSuiteInfo（险别信息）等实体类
        river_templates只依赖了core模块。core提供了templates项目核心功能
    entity：包含了操作数据库需要的实体类，mapper文件，profile配置文件，提供不同环境下的不通配置需求
    bussiness：提供一系列service服务接口，服务于web层各个controller提供业务服务功能接口。
    akka：负责消息传递，任务分发，天然的分布式高并发框架，在项目中主要用到了消息分发 akka 的异步，actor 实例缓存复用等功能。
    web：对外提供服务模块，核心控制器有 AutoController 中的 auto 方法用来接收任务，启动类在该模块下 SpringApplicationLauncher
        具体的任务类型详见 TaskType.java 类，以此来实现对应的业务需求
### 一个请求发送到百川的处理流程
    一. 首先经过 AutoController 中的 auto 方法接收请求，经过一系列的包装后组装为 AutoTask 类，然后调用 taskActor 的 tell    
        方法实现消息的传递。同时 auto 返回 Reply 类型的结果，实现异步功能。一个流程一个 AutoTask 类一个流程的上下文作用。
    二.taskActor 接收到消息后，有三个分支判断
        1.RequestTaskAndTransform：这个是 taskActor 接收消息后首先进行的逻辑操作。这个 Callable 实现类作用是调用获取任务参数的 url
            请求 url 获取参数后进一步封装为百川对应的实体类。
        2.在获取到数据后，调用如下三个方法初始化流程
            (1)装载接口（当前任务下的接口流程）itf = getInterface(t, t.getTaskType());
            (2)加载配置（根据接口流程装载对应的配置参数） this.loadConfig(itf, t);
            (3)加载模板（根据接口加载对应的模板数据） this.loadTemplate(itf, t); 
            (4)对任务类型做进一步判断：
                ● keepSession(t, itf): 是否需要保持登录（一般精灵业务）
                    ● 进入到 keepSession 方法中后首先进行一些列的参数处理。重新启动3个actor为： 
                        InsActor(保险公司) ——> AccountActor（账号） ——> WinActor（窗口处理真正业务逻辑）;
                    注：一个公司有多个账号（没限制），一个账号多个窗口（默认10可配置），总 actors 数量最大不超出 1000000（目前硬编码）
                    ● winActor 调用   processTask ——> TaskUtil.process();
                ● ProcessTask ——> TaskUtil.process(): 该分支是处理业务流程的核心分支(一般 edi 业务)
                最终两个分支都到 TaskUtil.process() 方法中,因此该方法是业务处理的真正地方。
        3.CallbackAndTransform：判断流程是否结束用来回调发送报价，平台信息等业务处理结果。这个回调需要调用方传百川回调 url，
### 详解百川核心业务代码 TaskUtil.process 方法
    1.该方法拿到之前 loadTemplate() 所有模板然后循环执行模板，当模板抛出 InsReturnException.AllowRepeat 异常时候 for 循环就会将当前
        下标回退对应的步数，来实现模板的循环。没执行一次模板就会调用  TaskUtil.saveMongoObject(autoTask, actionLog) 方法来保存
        当前模板请求报文，回写报文，异常信息等数据。
    2.template 项目中的模板通过 scriptEngine.run(scriptName, binding),脚本引擎执行，我们在其之上封装一层
       ● DataUtil.engine(temp.getName(), itf, inActionData);其中 inActionData 为 Map 类型形如 
            inActionData.put("enquiry", autoTask.getTaskEntity());
            inActionData.put("script", "");
            inActionData.put("taskType", autoTask.getTaskType());
            inActionData.put("config", autoTask.getConfigs());
            inActionData.put("params", autoTask.getParams());
            inActionData.put("reqHeaders", autoTask.getReqHeaders());
            inActionData.put("tempValues", autoTask.getTempValues());
            因此模板中可以直接取到 enquiry 对象,因此 template 模板中取到未定义的值都来自这里，
       ● 一般一个接口请求包含两个模板，形如：
            xxx_req.groovy, xxx_rep.groovy,在 process 方法中可以以 HttpSender.post 或 get 等方法为分割，
           responseBody = HttpSender.doPostWithRetryAndLoadHeader(5, closeableHttpClient, true, realUrl, requestBody, autoTask.getArrayParams(), autoTask.getReqHeaders(), temp.getCharSet(), buildReqConfig(autoTask), autoTask.keepSessionKey(), autoTask.getRepHeaders());
            顾名思义 _req 结尾为请求参数处理，_rep 结尾的为返回报文的处理，有报价信息外转内，业务处理等逻辑。
            如上方法：requestBody 为 xxx_req.groovy 模板处理后返回的结果。其他参数都是配置信息，或者模板中根据实际业务自行处理的
            如上方法返回的 responseBody 会放到 autoTask backRoot/root 中（精灵/edi）然后调用 xxx_rep.groovy 模板处理返回结果
       ● 模板请求结束会调用如下方法：
            TaskUtil.saveMongoObject(autoTask, actionLog); // 将模板的请求报文返回报文存 MongoDB 库（现在保存7天）
            concatActionLog(autoTask, actionLog);          // 将模板动作信息与当前 taskActor 关联
            
### 百川错误信息
    1.百川对错误信息进行了翻译，目的是让运营或者机构同事可以看懂错误信息，前提在异常配置中配置异常由运营同事自主发起，相关表为tb_error_category，相关代码请看TaskActor内部类CallbackAndTransform
    2.如果您想修改回写信息格式，请修改TaskActor内部类相关代码请看TaskActor内部类CallbackAndTransform
       
       
### 百川代码开发中常遇到问题
    1. 模板更新后线上代码不生效。这种问题一般由于模板引用的问题导致，因为 scriptEngine 有缓存的功能，当模板引用关系超过两个个时候
       调用关系如 A -> B -> C, 如果只提交模板 C ，导致引擎检测不到 A 模板也需要更新
       解决方案：同时修改 B C 模板并提交。
    2. 线上 bug 本地复现不了，前提是代码一致情况下。
       原因：本地因为使用的是 template 项目使用的是 test类，而线上使用的代码和本地的代码不一套（有重复代码），根据具体情况具体分析
        （1）常见深坑 bug 险种加上道路救援险种后报莫名其妙 bug 注意 该类型的参数 amount 值，生产上会转成 2.0，本地代码为2。导致的问题，一般处理为整数 String 即可
    3. 线上报 AutoTask 类找不到等问题 
       原因：参数类型 dateType 选的 Map 而模板中使用 autoTask，模板使用数据类型分为 Enquiry 和 Map 两种类型。
       一般精灵使用的是 Enquiry 类型，接口和精灵支付任务类型使用的是 Map 类型，
       两种类型的原因：历史原因（可能当时为了兼容什么），现在推荐优先使用 Enquiry 类型，Enquiry 完全可以替代 Map 类型，根据具体情况分析。
    4.本地测试报错 407 
       原因：配置参数中 proxy 代理信息与测试类中硬编码的 proxy 配置信息不一致导致的。
    5.本地跑测试用例报 OutOfMemoryError
       原因：Idea 编译所有的模板项目导致的，删除部分不需要的公司模板即可解决问题.
    6.百川电子保单下载增加了该功能是否允许执行的后门，实现在ToolsController
    7.如果您开发功能过程中需要使用redis，请一定要定义该功能相关redis所使用key的前缀

### 启动问题
1. 修改启动配置 Shorten command line 为 classpath file
2. 以 gradle 中的 bootRun 启动

### 新增菜单及权限

1. 新增菜单需在 `bc_menu_info` 中手动添加数据

2. 新增权限需在 `bc_permission_info` 中手动添加数据，`admin` 用户默认拥有全部权限。
