package com.cheche365.bc.service.impl;

import com.cheche365.bc.entity.mysql.Link;
import com.cheche365.bc.service.LinkService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.ws.rs.core.UriBuilder;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.cheche365.bc.service.util.LinkUtils.base62ToDecimal;
import static com.cheche365.bc.service.util.LinkUtils.decimalToBase62;

@Service
public class LinkHandleService {

    @Resource
    LinkService linkService;

    @Value("${domain}")
    private String domain;

    /**
     * 长链接转换成短链接
     *
     * @param originalLink 原始链接
     * @return 短链接
     */
    public String genShortLink(String originalLink) {
        Link link = new Link();
        link.setOriginalLink(originalLink);
        link.setShortLink("0");
        link.setCreateTime(LocalDateTime.now());
        linkService.save(link);
        String shortLink = decimalToBase62(link.getId());
        link.setShortLink(shortLink);
        linkService.updateShortLinkById(link);
        return UriBuilder.fromUri(domain).path("link").path(shortLink).build().toString();
    }

    /**
     * 获取长链接
     *
     * @param shortLink 短链接
     * @return 长链接
     */
    public String getOriginalLink(String shortLink) {
        long id = base62ToDecimal(shortLink);
        Link link = linkService.getById(id);
        return Objects.nonNull(link) ? link.getOriginalLink() : null;
    }
}

