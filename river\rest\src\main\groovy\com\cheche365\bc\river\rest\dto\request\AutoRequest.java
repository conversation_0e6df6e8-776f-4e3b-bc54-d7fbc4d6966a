package com.cheche365.bc.river.rest.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
/**
 * <AUTHOR>
 */

@Data
public class AutoRequest {

    @NotNull(message = "monitorid不能为空")
    private String monitorid;
    @NotNull(message = "callBackUrl不能为空")
    private String callBackUrl;
    @NotNull(message = "businessId不能为空")
    private String businessId;
    @NotNull(message = "taskType不能为空")
    private String taskType;
    @NotNull(message = "companyId不能为空")
    private String companyId;
    @NotNull(message = "requestDataUrl不能为空")
    private String requestDataUrl;
    @NotNull(message = "enquiryId不能为空")
    private String enquiryId;
    @NotNull(message = "processType不能为空")
    private String processType;

    /**
     * 规则id
     */
    private String ruleid;
    /**
     * 规则引擎地址
     */
    private String ruleUrl;
    /**
     * 平台信息url
     */
    private String platformSaveUrl;
    /**
     * 任务状态
     */
    private String taskStatus;
    /**
     * 特别约定
     */
    private String specialStr;
    /**
     * 验证码
     */
    private String verificationCode;
    /**
     * 请求来源
     */
    private RequestSource requestSource;

    /**
     * 车辆免验原因
     */
    private String reasonsForExemption;

    /**
     * 01 传统业务  02 网销业务
     */
    private String buybusitype;
    /**
     * 请求来源中文描述
     */
    private String client;

    /**
     * 内外网标识
     */
    private String isSourceCheche;
    /**
     * 任务id
     */
    private String autoTraceId;
    /**
     * proposalFormJumpUrl 回滚
     */
    private String proposalFormJumpUrl;
    /**
     * 支付方式
     */
    private String paytype;
    /**
     * 主动回推url
     */
    private String writeBackUrl;

    @Data
    public static class RequestSource {
        private String sourceProduct;
        private String sourceScenario;
        private String sourceChannel;
        private String desc;
    }
}
