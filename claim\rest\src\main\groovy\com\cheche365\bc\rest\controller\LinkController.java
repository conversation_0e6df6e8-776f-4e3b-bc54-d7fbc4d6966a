package com.cheche365.bc.rest.controller;

import com.cheche365.bc.exception.FlowException;
import com.cheche365.bc.rest.exception.RestException;
import com.cheche365.bc.service.impl.LinkHandleService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;


/**
 * <AUTHOR> zhangying
 * @date 2024-03-20
 * @descript :链接转换
 */
@Slf4j
@RestController
@AllArgsConstructor
public class LinkController {

    private LinkHandleService linkHandleService;

    /**
     * 通过短链接ID获取长链接地址并访问
     *
     * @param response
     * @param shortLink
     * @throws IOException
     */
    @RequestMapping(value = "/link/{shortLink}", method = RequestMethod.GET)
    public void redirectOriginalLink(HttpServletResponse response, @PathVariable() String shortLink) throws Exception {
        if (StringUtils.isNotEmpty(shortLink)) {
            String originalLink = linkHandleService.getOriginalLink(shortLink);
            log.info("重定向长链接地址:{},{}", shortLink, originalLink);
            response.sendRedirect(originalLink);
        }
    }
}
