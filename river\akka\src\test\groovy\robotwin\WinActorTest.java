package robotwin;

import akka.actor.ActorRef;
import akka.actor.ActorSystem;
import akka.actor.Props;
import com.cheche365.bc.actor.LoginConfig;
import com.cheche365.bc.actor.WinActor;
import com.cheche365.bc.utils.encrypt.MD5;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 *         Created by austinChen on 2017/10/18 17:29.
 */
public class WinActorTest {

    private ActorSystem actorSystem;

    @Before
    public void init() {
        actorSystem = ActorSystem.create("sys");
        System.out.println(MD5.toHex("baoxian2019"));

    }

    @Test
    public void testRefresh() throws Exception
    {
        LoginConfig loginConfig=new LoginConfig();
        loginConfig.setRefreshUrl("http://www.baidu.com");
        loginConfig.setExpectSuccessFlag("百度");
        loginConfig.setRefreshMin(2);
        loginConfig.setUsePoolConnect(false);
        ActorRef actor = actorSystem.actorOf(Props.create(WinActor.class, 1, loginConfig), "win-1");
        Thread.sleep(100*1000);
    }
}
