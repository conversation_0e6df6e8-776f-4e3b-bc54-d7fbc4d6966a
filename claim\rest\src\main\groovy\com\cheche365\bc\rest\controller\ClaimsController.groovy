package com.cheche365.bc.rest.controller

import com.cheche365.bc.message.Response
import com.cheche365.bc.service.dto.ClaimsCallbackReq
import com.cheche365.bc.service.dto.ClaimsReq
import com.cheche365.bc.service.impl.claim.ClaimsService
import com.cheche365.bc.service.impl.claim.InsCallbackService
import groovy.json.JsonOutput
import groovy.json.StringEscapeUtils
import groovy.transform.TupleConstructor
import groovy.util.logging.Slf4j
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*


import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE

/**
 * 理赔 controller
 */
@RequestMapping(
    value = '/api/claims',
    produces = APPLICATION_JSON_VALUE
)
@Validated
@Slf4j
@RestController
@TupleConstructor(defaults = false, includeFields = true)
class ClaimsController {

    private ClaimsService claimsService
    private InsCallbackService insCallbackService;

    /**
     * 理赔统一入口
     * @param client 请求来源
     * @param companyId 保司编码
     * @param processType 精灵还是edi
     * @param taskType 报案、影像上传，报案查询
     * @return
     */
    @PostMapping('/{client}/{companyId}/{processType}/{taskType}')
    @Valid
    Response<Map<String, Object>> claimsEntrance(
        @Pattern(regexp = '.{3,20}', message = 'client提供的不太对吧！')
        @PathVariable('client') String client,
        @Pattern(regexp = '\\d{3,10}', message = 'companyId提供的不太对，路由器无法分发呀！')
        @PathVariable('companyId') String companyId,
        @Pattern(regexp = 'edi|robot', message = 'processType提供的不太对，没法处理呀！')
        @PathVariable('processType') String processType,
        @Pattern(regexp = '[a-zA-z-_]{3,30}', message = 'taskType类型不符合规则')
        @PathVariable('taskType') String taskType,
        @RequestBody Map<String, Object> claims
    ) {
        log.info('client:{}, companyId:{}, processType:{}, taskType:{}, enquiryId:{}, claims:{}, 收到理赔请求，正在处理.......',
            client, companyId, processType, taskType, claims.enquiryId, StringEscapeUtils.unescapeJava(JsonOutput.toJson(claims)))
        new Response(
            200,
            claimsService.execute(
                new ClaimsReq(
                    client: client,
                    companyId: companyId,
                    processType: processType,
                    taskType: taskType,
                    claims: claims
                )
            ),
            '执行成功！'
        )
    }

    /**
     * 提供给保险公司回调接口，所有保司以车车定义的数据结构为准
     * @param companyId
     * @param taskType
     * @param claimsNo 理赔号
     * @param callbackInfo
     * @return
     */
    @PostMapping('/checheDefine/{companyId}/{taskType}/{claimsNo}')
    @Valid
    Response<Map<String, Object>> callbackForCheche(
        @Pattern(regexp = '\\d{3,5}', message = '保司的ID必须为四位数字')
        @PathVariable('companyId') String companyId,
        @Pattern(regexp = '[a-zA-z-_]{3,30}', message = '回调类型必须为字母')
        @PathVariable('taskType') String taskType,
        @Pattern(regexp = '[a-zA-z-_\\d]{3,30}', message = '报案号格式不正确')
        @PathVariable('claimsNo') String claimsNo,
        @RequestBody Map<String, Object> callbackInfo
    ) {
        log.info('companyId:{}, taskType:{}, claimsNo:{}, callbackInfo:{} 收到车车定义的数据结构（理赔）回调，正在处理.......',
            companyId, taskType, claimsNo, StringEscapeUtils.unescapeJava(JsonOutput.toJson(callbackInfo)))
        new Response(
            200,
            insCallbackService.execute(
                new ClaimsCallbackReq(
                    companyId: companyId,
                    taskType: taskType,
                    claimsNo: claimsNo,
                    callbackInfo: callbackInfo
                )
            ),
            '执行成功！'
        )
    }
}
