package com.cheche365.bc.admin.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.admin.service.service.UserRoleService;
import com.cheche365.bc.entity.mysql.RoleInfo;
import com.cheche365.bc.entity.mysql.UserRole;
import com.cheche365.bc.mapper.UserRoleMapper;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-11
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    @Override
    public List<RoleInfo> listRoleCodeByUserInfo(Long id, int status) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("id", id);
        params.put("status", status);
        return baseMapper.listRoleCodeByUserInfo(params);
    }
}
