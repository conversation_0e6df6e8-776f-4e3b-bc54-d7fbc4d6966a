package com.cheche365.bc.enums;

import com.beust.jcommander.internal.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public enum ClaimExceptionCodeEnum {

    REPEAT_CURRENT_TEMPLATE(1, "重试当前模板"),
    REPEAT_ALL_TEMPLATE(2, "重试所有模板"),
    SKIP_TEMPLATE(3, "跳过当前模板"),
    ABORT_FLOW(4,"终止流程");

    public static final List<Integer> SECURITY_TEMPLATE_CODES = Lists.newArrayList(
            REPEAT_CURRENT_TEMPLATE.getCode(),
            REPEAT_ALL_TEMPLATE.getCode(),
            SKIP_TEMPLATE.getCode()
    );

    private final int code;
    private final String name;
}
