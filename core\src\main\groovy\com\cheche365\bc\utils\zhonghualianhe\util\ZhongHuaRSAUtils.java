package com.cheche365.bc.utils.zhonghualianhe.util;


import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import org.apache.commons.codec.binary.Base64;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 加解密签名工具
 */
public class ZhongHuaRSAUtils {

    /**
     * 默认编码
     */
    private static final String CHARSET_UTF_8 = "UTF-8";

    /**
     * 加密并签名<br>
     * 对于<b>合作方</b>，publicKey是指保险公司的公钥，privateKey是指合作方的私钥<br>
     * 对于<b>保险公司</b>，publicKey是指合作方的公钥，privateKey是指保险公司自己的私钥<br>
     *
     * @param bizContent 需要加密数据
     * @param publicKey  公钥
     * @param privateKey 私钥
     * @param charset    字符编码
     * @param isEncrypt  是否加密
     * @param isSign     是否加签
     * @return CONTENT   密文  GW_CH_SIGN 签名
     * @throws Exception
     */
    public static Map<String, String> encryptAndSign(String bizContent, String publicKey, String privateKey, String charset,
                                                     boolean isEncrypt, boolean isSign) throws Exception {
        if (isEmpty(charset)) {
            charset = CHARSET_UTF_8;
        }
        bizContent = URLEncoder.encode(bizContent, CHARSET_UTF_8);
        Map<String, String> map = new HashMap<String, String>();
        if (isEncrypt) {
            //加密
            String encrypted = rsaEncrypt(bizContent, publicKey, charset);
            map.put("CONTENT", encrypted);
            //加签
            if (isSign) {
                String sign = rsaSign(encrypted, privateKey, charset);
                map.put("GW_CH_SIGN", sign);
            }
        } else if (isSign) {
            // 不加密，但需要签名
            map.put("CONTENT", bizContent);
            String sign = rsaSign(bizContent, privateKey, charset);
            map.put("GW_CH_SIGN", sign);
        }
        return map;
    }

    /**
     * 验签并解密
     * <p>
     * 对于<b>合作方</b>，publicKey是指保险公司的公钥，privateKey是指合作方的私钥<br>
     * 对于<b>保险公司</b>，publicKey是指合作方的公钥，privateKey是指保险公司自己的私钥<br>
     * @return 解密后明文，验签失败则异常抛出
     */
    public static String checkSignAndDecrypt(String content, String sign, String publicKey, String privateKey, String charset,
                                             boolean isDecrypt, boolean isCheckSign) throws Exception {
        if (isEmpty(charset)) {
            charset = CHARSET_UTF_8;
        }
        //验签
        if (isCheckSign) {
            checkRSASign(sign, content, publicKey, charset);
        }
        //解密
        if (isDecrypt) {
            return URLDecoder.decode(rsaDecrypt(content, privateKey, charset), CHARSET_UTF_8);
        }
        return content;
    }

    /**
     * 公钥加密<br>
     * 对于<b>保险公司</b>，publicKey是指合作方的公钥，privateKey是指保险公司自己的私钥<br>
     * 对于<b>合作方</b>，publicKey是指保险公司的公钥，privateKey是指合作方的私钥<br>
     *
     * @param content   待加密内容
     * @param publicKey 公钥
     * @param charset   字符集，如UTF-8, GBK, GB2312
     * @return 密文内容
     * @throws Exception
     */
    public static String rsaEncrypt(String content, String publicKey, String charset) throws Exception {

        RSA build = RSA.builder()
                .publicKey(publicKey)
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                .build();
        return build.encrypt(content.getBytes(charset));

    }

    /**
     * 私钥解密<br>
     *
     * @param content    待解密内容
     * @param privateKey 私钥
     * @param charset    字符集，如UTF-8, GBK, GB2312
     * @return 明文内容
     * @throws Exception
     */
    public static String rsaDecrypt(String content, String privateKey, String charset) throws Exception {
        try {

            byte[] encryptedData = isEmpty(charset) ? Base64.decodeBase64(content.getBytes()) : Base64
                    .decodeBase64(content.getBytes(charset));

            RSA build = RSA.builder()
                    .privateKey(privateKey)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                    .build();
            return build.decrypt(encryptedData);
        } catch (Exception e) {
            throw new Exception("EncodeContent = " + content + ",charset = " + charset, e);
        }
    }


    /**
     * RSA签名
     *
     * @param content
     * @param privateKey
     * @param charset
     * @return
     * @throws Exception
     */
    public static String rsaSign(String content, String privateKey, String charset) throws Exception {
        try {
            RSA build = RSA.builder()
                    .privateKey(privateKey)
                    .signAlgorithm(EncryptEnum.SignAlgorithmEnum.SHA1WithRSA)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                    .build();
            return build.sign(content.getBytes(charset));

        } catch (Exception e) {
            throw new Exception("RSAcontent = " + content + "; charset = " + charset, e);
        }
    }

    /**
     * RSA签名校验<br>
     * 对于<b>保险公司</b>，publicKey是指合作方的公钥<br>
     * 对于<b>合作方</b>，publicKey是指保险公司的公钥<br>
     *
     * @param sign
     * @param content
     * @param publicKey
     * @param charset
     * @throws Exception
     */
    public static void checkRSASign(String sign, String content, String publicKey, String charset) throws Exception {
        if (!rsaCheckContent(content, sign, publicKey, charset)) {
            throw new Exception("rsaCheck failure: sign=" + sign + ", content=" + content + ", charset=" + charset);
        }
    }


    /**
     * RSA签名校验<br>
     * 对于<b>保险公司</b>，publicKey是指合作方的公钥<br>
     * 对于<b>合作方</b>，publicKey是指保险公司的公钥<br>
     *
     * @param sign
     * @param content
     * @param publicKey
     * @param charset
     * @throws Exception
     */
    public static boolean rsaCheckContent(String content, String sign, String publicKey, String charset)
            throws Exception {
        try {

            RSA build = RSA.builder()
                    .publicKey(publicKey)
                    .signAlgorithm(EncryptEnum.SignAlgorithmEnum.SHA1WithRSA)
                    .cipherAlgorithm(EncryptEnum.AlgorithmEnum.RSA)
                    .build();
            return build.verify(content, sign);
        } catch (Exception e) {
            throw new Exception("RSAcontent=" + content + ",sign=" + sign + ",charset=" + charset, e);
        }
    }

    /**
     * 判断字符串是否为空
     *
     * @param value 字符串
     * @return 空或空串返回true 否则false
     */
    public static boolean isEmpty(String value) {
        return (null == value || value.trim().equals("")) ? true : false;
    }

}
