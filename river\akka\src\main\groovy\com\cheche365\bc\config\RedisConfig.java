package com.cheche365.bc.config;

import com.cheche365.bc.cache.SubscribeListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import static com.cheche365.bc.constants.Constants.*;

@Configuration
public class RedisConfig {

    private final RedisConnectionFactory redisConnectionFactory;

    @Autowired
    public RedisConfig(RedisConnectionFactory redisConnectionFactory) {
        this.redisConnectionFactory = redisConnectionFactory;
    }

    /**
     * <h2>关闭窗口 Topic</h2>
     * */
    @Bean
    public ChannelTopic killWinTopic() {
        return new ChannelTopic("killWin");
    }


    /**
     * <h2>配置接口 的 Topic</h2>
     * */
    @Bean
    public ChannelTopic interfaceTopic() {
        return new ChannelTopic(INTERFACE_CACHE_KEY_PREFIX);
    }

    /**
     * 平台信息码表监听器
     *
     * @return
     */
    @Bean
    public ChannelTopic platformInfoCodeTopic() {
        return new ChannelTopic(PLATFORM_INFO_CODE_CACHE_KEY_PREFIX);
    }

    /**
     * 平台信息码表监听器
     *
     * @return*/
    public MessageListener listener() {
        return new SubscribeListener();
    }

    /**
     * <h2>将消息监听器绑定到消息容器</h2>
     * */
    @Bean
    public RedisMessageListenerContainer messageListenerContainer() {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);

        // 可以修改成 patternTopic, 看一看 MessageListener 中监听的数据
        container.addMessageListener(listener(), interfaceTopic());
        container.addMessageListener(listener(), killWinTopic());
        container.addMessageListener(listener(), platformInfoCodeTopic());
        container.addMessageListener(listener(), cluePush());
        return container;
    }

    /**
     * 线索主动推送 Topic
     * */
    @Bean
    public ChannelTopic cluePush() {
        return new ChannelTopic("cluePush");
    }


}
