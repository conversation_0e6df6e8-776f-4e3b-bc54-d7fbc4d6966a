package com.cheche365.bc.tools;

import lombok.extern.slf4j.Slf4j;

/**
 * 压缩工具类
 *
 * <AUTHOR>
 * @Created by austinChen on 2015/12/15 8:51.
 */
@Slf4j
public final class CompactUtil {

    /**
     * 压缩Xml进行传输使用
     *
     * @param str 需要压缩的字符串
     * @return xml去除无用的空格和空行
     */
    public static String compactXml(String str) {
        if (str != null && !str.isEmpty()) {
            return str.replaceAll("(\r\n)|(\n)", "")
                    .replaceAll("(?s)<!--.*?-->", "")
                    .replaceAll("(?<=>) \\s+(?=\\ S+)|(?<=\\S+) \\s+(?=<)", "");
        } else {
            return str;
        }
    }
}
