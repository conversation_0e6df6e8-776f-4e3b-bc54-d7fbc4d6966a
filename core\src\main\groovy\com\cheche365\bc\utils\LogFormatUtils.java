package com.cheche365.bc.utils;

import cn.hutool.core.collection.CollUtil;
import com.cheche365.bc.tools.StringUtil;
import com.google.common.collect.Lists;

import java.util.List;


/**
 * <AUTHOR>
 */
public class LogFormatUtils {

    /**
     * HTML 格式判断正则表达式
     */
    public static final String HTML_PARSER = "<\\w+\\s";

    /**
     * 二进制判断正则表达式
     */
    public static final String BINARY_PARSER = "[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F]";

    /**
     * BASE64 格式判断正则表达式
     */
    public static final String BASE64_PARSER = "[\">][\\w+-=\\\\]{1024,}?[\"<]";

    /**
     * 最长 BODY 长度
     */
    public static final Integer MAX_LENGTH = 8000;

    /**
     * 需要忽略打印的 url
     */
    private static final List<String> _IGNORE_LOG_URI_LIST;
    /**
     * BASE64, BINARY, HTML替换为以下片段
     */
    public static final String BASE64_FRAGMENT = "BASE64 String very long (more than 1024) ........";
    public static final String BINARY_FRAGMENT = "Binary String very long (more than 1024) ........";
    public static final String HTML_FRAGMENT = "HTML String very long (more than 1024) ........";
    public static final String LONG_BODY_FRAGMENT = "BODY SO LONG (more than 8000) ........";

    static {
        _IGNORE_LOG_URI_LIST = Lists.newArrayList(
                "api/order-center/internal/river/getData",
                "api/order-center/internal/river/saveData",
                "api.chetimes.com/baichuan/quote/data",
                "api.chetimes.com/callback/baichuan"
        );
    }

    public static boolean ignoreLog(String url) {
        return _IGNORE_LOG_URI_LIST.stream().anyMatch(url::contains);
    }

    public static String logFormat(String data) {
        if (StringUtil.isEmpty(data)) {
            return "";
        }
        if (checkLength(data)) {
            return LONG_BODY_FRAGMENT;
        }
        if (CollUtil.isNotEmpty(StringUtil.stringMatcher(data, BINARY_PARSER))) {
            return BINARY_FRAGMENT;
        }
        if (CollUtil.isNotEmpty(StringUtil.stringMatcher(data, HTML_PARSER))) {
            return HTML_FRAGMENT;
        }
        List<String> bodyList = StringUtil.stringMatcher(data, BASE64_PARSER);
        if (CollUtil.isNotEmpty(bodyList)) {
            return StringUtil.replaceString(data, bodyList, BASE64_FRAGMENT);
        }

        return StringUtil.deleteSpace(data);
    }

    public static boolean checkLength(String data) {
        return data.length() > MAX_LENGTH;
    }
}
