package com.cheche365.bc.river.service.callback;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.message.TaskType;
import com.cheche365.bc.tools.MapUtil;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.RedisUtil;
import com.cheche365.bc.utils.tai_bao.MessageConstants;
import com.cheche365.bc.utils.tai_bao.SignatureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class TaiPingYangCallBackHandler extends AbstractCallBackHandler {

    /**
     * 公钥
     */
    private static final String CACHE_NAME_PUBLIC_KEY = "bc:edi:2011:publicKey:";

    /**
     * 私钥
     */
    private static final String CACHE_NAME_PRIVATE_KEY = "bc:edi:2011:thirdPrivateKey:";

    @Override
    public boolean needHandle(String companyId) {
        return String.valueOf(InsCompanyEnum.CPIC.getCode()).equals(companyId);
    }

    @Override
    public String handleCallBackBody(Map param) {
        String callbackBody = (String) param.get("callbackBody");
        Map dataSource = (Map) param.get("dataSource");
        AutoTask autoTask = (AutoTask) dataSource.get("autoTask");
        if (autoTask.getTaskType().contains(TaskType.POLICY_APPROVED.code)) {
            return callbackBody;
        }
        try {
            JSONObject requestBody = JSON.parseObject(callbackBody);
            Result result = getKeys(requestBody, dataSource);
            return SignatureUtils.checkSignAndDecrypt(callbackBody, result.publicKey(), result.privateKey(), true, true);
        } catch (Exception e) {
            log.error("太平洋回调解析保司报文异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }

    }

    @Override
    public String handleResponseBody(Map param) {
        String responseContent = (String) param.get("responseContent");
        Map dataSource = (Map) param.get("dataSource");
        AutoTask autoTask = (AutoTask) dataSource.get("autoTask");
        if (autoTask.getTaskType().contains(TaskType.POLICY_APPROVED.code)) {
            return responseContent;
        }
        //太保需要加密
        JSONObject requestObject = JSONObject.parseObject(responseContent);
        String bizContent = requestObject.getString(MessageConstants.BIZ_CONTENT);
        // 加密、加签

        JSONObject requestBody = JSON.parseObject(autoTask.getApplyJson());
        Result result = getKeys(requestBody, dataSource);

        try {
            return SignatureUtils.encryptAndSign(bizContent, result.publicKey(), result.privateKey(), requestObject.toJSONString(), true, true);
        } catch (Exception e) {
            log.error("太平洋回调响应保司报文渲染异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    private static Result getKeys(JSONObject requestBody, Map dataSource) {
        String thirdCode = requestBody.getString("thirdCode");
        JSONPath.set(dataSource, "$.config.thirdCode", thirdCode);
        String publicKey = getKey(thirdCode, CACHE_NAME_PUBLIC_KEY, "config.publicKey", dataSource);
        String privateKey = getKey(thirdCode, CACHE_NAME_PRIVATE_KEY, "config.thirdPrivateKey", dataSource);
        return new Result(publicKey, privateKey);
    }

    private static String getKey(String thirdCode, String key, String defaultKey, Map dataSource) {
        if (StrUtil.isBlank(thirdCode)) {
            return (String) DataUtil.get(defaultKey, dataSource);
        }
        String rsaKey = RedisUtil.get(key + thirdCode);
        if (StrUtil.isNotBlank(rsaKey)) {
            MapUtil.putMap(dataSource, defaultKey, rsaKey);
            return rsaKey;
        }
        return (String) DataUtil.get(defaultKey, dataSource);
    }

    private record Result(String publicKey, String privateKey) {
    }
}
