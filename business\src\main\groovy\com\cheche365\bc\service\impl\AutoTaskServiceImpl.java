package com.cheche365.bc.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.dto.AutoQuoteCoreInfoSchema;
import com.cheche365.bc.dto.AutoTaskBiLogSchema;
import com.cheche365.bc.entity.AutoTask;
import com.cheche365.bc.entity.hbase.AutoTaskLog;
import com.cheche365.bc.enums.CallbackStatusEnum;
import com.cheche365.bc.hbase.AutoTaskLogRepository;
import com.cheche365.bc.mapper.AutoTaskMapper;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.sharding.OldTableSwitch;
import com.cheche365.bc.sharding.ShardingContextHolder;
import com.cheche365.bc.sharding.ShardingUtil;
import com.cheche365.bc.util.AutoQuoteCoreInfoBiLogUtil;
import com.cheche365.bc.util.AutoTaskBiLogUtil;
import com.cheche365.bi.logger.BiLoggerV2;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Service
@Slf4j
@AllArgsConstructor
public class AutoTaskServiceImpl extends ServiceImpl<AutoTaskMapper, AutoTask> implements AutoTaskService {

    private static final String CREATE_TABLE_SQL = """
        CREATE TABLE IF NOT EXISTS `{}` (
          `autoTraceId` varchar(255) NOT NULL,
          `actionLogs` text,
          `applyJson` text CHARACTER SET utf8mb4,
          `endTime` datetime DEFAULT NULL,
          `company_id` varchar(255) DEFAULT NULL,
          `plate_no` varchar(255) DEFAULT NULL,
          `resultStr` longtext CHARACTER SET utf8mb4,
          `startTime` datetime DEFAULT NULL,
          `task_status` varchar(255) DEFAULT NULL,
          `taskType` varchar(255) DEFAULT NULL,
          `traceKey` varchar(255) DEFAULT NULL,
          `feedbackJson` longtext CHARACTER SET utf8mb4,
          `biz_propose_no` varchar(255) DEFAULT NULL,
          `efc_propose_no` varchar(255) DEFAULT NULL,
          `biz_policy_no` varchar(255) DEFAULT NULL,
          `efc_policy_no` varchar(255) DEFAULT NULL,
          `failureCause` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL,
          `failureCauseCategory` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL,
          `internet_sales` tinyint(2) unsigned DEFAULT '0' COMMENT '是否网销: 0: 否; 1: 是;',
          `vin` varchar(255) DEFAULT NULL,
          `source_kind` tinyint(4) DEFAULT NULL COMMENT '1:用户手工；2:快速续保；3:续保提醒；4:批量导入执行报价；5:状态跑批；6:测试验证数据',
          `data_source_log_id` smallint(5) unsigned DEFAULT NULL COMMENT '来源归类日志ID',
          `callback_status` tinyint default null comment '回写状态;-1:无需回写,0:回写失败,1:回写成功',
          PRIMARY KEY (`autoTraceId`),
          KEY `traceKey_index` (`traceKey`,`key1`,`taskType`) USING BTREE,
          KEY `idx_startTime_taskType_vin` (`startTime`,`taskType`,`vin`) USING BTREE,
          KEY `idx_vin` (`vin`),
          KEY `idx_taskType_startTime_task_status` (`taskType`,`startTime`,`task_status`) USING BTREE,
          KEY `idx_startTime_taskType` (`startTime`,`taskType`) USING BTREE,
          KEY `idx_startTime_dataSourceLogId_taskType` (`startTime`,`data_source_log_id`,`taskType`) USING BTREE,
          KEY `idx_biz_propose_no` (`biz_propose_no`) USING BTREE,
          KEY `idx_biz_policy_no` (`biz_policy_no`) USING BTREE,
          KEY `idx_efc_propose_no` (`efc_propose_no`) USING BTREE,
          KEY `idx_plate_no_startTime_taskType` (`plate_no`,`startTime`,`taskType`) USING BTREE,
          KEY `idx_plate_no` (`plate_no`) USING BTREE
        ) ENGINE=InnoDB;
        """;

    @Resource
    private BiLoggerV2 biLogger;

    @Resource
    private AutoTaskLogRepository autoTaskLogRepository;


    @Override
    public List<AutoTask> getTaskByTaskIdComTaskType(String taskId, String com, String taskType) {
        List<AutoTask> autoTasks = lambdaQuery().eq(AutoTask::getTraceKey, taskId)
            //
            .eq(AutoTask::getCompanyId, com)
            //
            .eq(AutoTask::getTaskType, taskType)
            //
            .orderByDesc(AutoTask::getStartTime)
            .list();

        // 使用统一方法处理HBase数据合并
        mergeHBaseDataIfNeeded(autoTasks, String.format("taskId:%s,com:%s,taskType:%s", taskId, com, taskType));

        return autoTasks;
    }

    @Override
    public AutoTask getTaskByTaskTypeProposeNumOrOrderNo(String taskType, String no, int noType) {
        QueryWrapper<AutoTask> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(taskType), "taskType", taskType);
        if (noType == 1)
            wrapper.eq("biz_propose_no", no);
        else if (noType == 2)
            wrapper.eq("efc_propose_no", no);
        else
            wrapper.eq("biz_policy_no", no);
        AutoTask autoTask = getOne(wrapper, false);
        // 使用统一方法处理HBase数据合并
        mergeHBaseDataIfNeeded(autoTask, String.format("taskType:%s,no:%s,noType:%d", taskType, no, noType));

        return autoTask;
    }

    @Override
    public AutoTask getOneTask(String taskId, String com, String[] taskType, Object bizProposeNo, Object efcProposeNo) {
        LambdaQueryWrapper<AutoTask> queryWrapper = new QueryWrapper<AutoTask>().lambda();
        queryWrapper.eq(StringUtils.isNotEmpty(taskId), AutoTask::getTraceKey, taskId);
        queryWrapper.eq(StringUtils.isNotEmpty(com), AutoTask::getCompanyId, com);
        if (null != bizProposeNo) {
            queryWrapper.eq(AutoTask::getBizProposeNo, bizProposeNo.toString());
        }
        if (null == bizProposeNo && null != efcProposeNo) {
            queryWrapper.eq(AutoTask::getEfcProposeNo, efcProposeNo.toString());
        }
        if (taskType.length > 1) {
            queryWrapper.in(AutoTask::getTaskType, Arrays.asList(taskType));
        } else {
            queryWrapper.eq(AutoTask::getTaskType, taskType[0]);
            queryWrapper.orderByDesc(AutoTask::getStartTime);
        }
        AutoTask autoTask = getOne(queryWrapper.last("limit 1"), false);

        // 使用统一方法处理HBase数据合并
        mergeHBaseDataIfNeeded(autoTask, String.format("taskId:%s,com:%s,taskType:%s,bizProposeNo:%s,efcProposeNo:%s",
            taskId, com, Arrays.toString(taskType), bizProposeNo, efcProposeNo));

        return autoTask;
    }

    @Override
    public AutoTask getTaskBodyByAutoTraceId(String bodyType, String autoTraceId) {
        AutoTask autoTask = getOne(new QueryWrapper<AutoTask>().select(bodyType).lambda().eq(AutoTask::getAutoTraceId, autoTraceId));

        if (autoTask == null) {
            autoTask = new AutoTask();
        }
        if (StrUtil.isBlank(autoTask.getAutoTraceId())) {
            autoTask.setAutoTraceId(autoTraceId);
        }
        mergeHBaseDataIfNeeded(autoTask, String.format("bodyType:%s,autoTraceId:%s", bodyType, autoTraceId));

        return autoTask;
    }

    @Override
    public boolean updateSourceKindAndDataSourceLogIdById(String autoTraceId, Integer sourceKind, Integer dataSourceLogId) {
        ShardingContextHolder.setOpType("UPDATE");
        try {
            // 根据OldTableSwitch决定更新方式
            if (OldTableSwitch.isUseOldTable()) {
                // 双写模式：先更新旧表，再更新新表
                try {
                    // 操作旧表
                    ShardingContextHolder.setUseOldTable(true);
                    lambdaUpdate()
                        .eq(AutoTask::getAutoTraceId, autoTraceId)
                        .set(AutoTask::getSourceKind, sourceKind)
                        .set(AutoTask::getDataSourceLogId, dataSourceLogId)
                        .update();

                    // 操作新表
                    ShardingContextHolder.setUseOldTable(false);
                    lambdaUpdate()
                        .eq(AutoTask::getAutoTraceId, autoTraceId)
                        .set(AutoTask::getSourceKind, sourceKind)
                        .set(AutoTask::getDataSourceLogId, dataSourceLogId)
                        .update();
                } finally {
                    ShardingContextHolder.clearUseOldTable();
                }
                return true;
            } else {
                // 正常模式，直接使用lambdaUpdate方法
                return lambdaUpdate()
                    .eq(AutoTask::getAutoTraceId, autoTraceId)
                    .set(AutoTask::getSourceKind, sourceKind)
                    .set(AutoTask::getDataSourceLogId, dataSourceLogId)
                    .update();
            }
        } finally {
            ShardingContextHolder.clearOpType();
        }
    }

    @Override
    public boolean createTable(String suffixName) {
        String tableName = "auto_task_";
        if (StrUtil.isBlank(suffixName)) {
            suffixName = DateTime.now().offset(DateField.MONTH, 1).toString(DatePattern.SIMPLE_MONTH_PATTERN);
        }
        tableName = tableName + suffixName;

        return ShardingUtil.execute(StrUtil.format(CREATE_TABLE_SQL, tableName));
    }

    @Override
    public boolean updateById(AutoTask task) {
        // 处理生成 BI 日志
        try {
            com.cheche365.bc.task.AutoTask autoTaskRequest = (com.cheche365.bc.task.AutoTask) task;
            AutoTaskBiLogSchema schema = AutoTaskBiLogUtil.getAutoTaskBiLogSchema(autoTaskRequest);
            if (Objects.nonNull(schema)) {
                biLogger.info(schema);
                if (TaskStatus.QUOTE_SUCCESS.State().equals(task.getTaskStatus()) || TaskStatus.AUTO_INSURE_SUCCESS.State().equals(task.getTaskStatus())) {
                    AutoQuoteCoreInfoSchema autoQuoteCoreInfoSchema = AutoQuoteCoreInfoBiLogUtil.getAutoTaskBiLogSchema(autoTaskRequest, schema);
                    biLogger.info(autoQuoteCoreInfoSchema);
                }
            }
        } catch (Exception e) {
            log.error("BI 日志打印失败：{}", ExceptionUtils.getStackTrace(e));
        }

        try {
            ShardingContextHolder.setOpType("UPDATE");
            boolean result;

            // 根据OldTableSwitch决定更新方式
            if (OldTableSwitch.isUseOldTable()) {
                result = updateWithDoubleWrite(task);
            } else {
                result = withoutBigTextFunc(task, super::updateById);
            }

            // HBase保存逻辑：如果启用双写或者双写开关关闭（只写HBase），则保存到HBase
            if (result && (StrUtil.isNotBlank(task.getResultStr()) || StrUtil.isNotBlank(task.getFeedbackJson()) || StrUtil.isNotBlank(task.getApplyJson()))) {
                updateHBaseData(task);
            }

            return result;
        } finally {
            ShardingContextHolder.clearOpType();
        }
    }

    @Override
    public boolean save(AutoTask entity) {
        try {
            ShardingContextHolder.setOpType("INSERT");
            boolean result;
            // 根据MonthlyShardingAlgorithm的isUseOldTable方法决定保存方式
            if (OldTableSwitch.isUseOldTable()) {
                result = saveWithDoubleWrite(entity);
            } else {
                // 正常模式，直接使用父类的save方法
                result = withoutBigTextFunc(entity, super::save);
            }
            // 保存到HBase（无论双写开关状态如何，都保存到HBase）
            if (result) {
                saveToHBase(entity);
            }
            return result;
        } finally {
            ShardingContextHolder.clearOpType();
        }
    }


    /**
     * 将AutoTask转换为AutoTaskLog
     * 只转换大文本字段：applyJson、resultStr、feedbackJson
     *
     * @param autoTask AutoTask实体
     * @return AutoTaskLog实体
     */
    private AutoTaskLog convertToAutoTaskLog(AutoTask autoTask) {
        if (autoTask == null) {
            return null;
        }
        return AutoTaskLog.builder()
            .autoTraceId(autoTask.getAutoTraceId())
            .traceKey(autoTask.getTraceKey())
            .applyJson(autoTask.getApplyJson())
            .resultStr(autoTask.getResultStr())
            .feedbackJson(autoTask.getFeedbackJson())
            .build();
    }

    /**
     * 将HBase中的AutoTaskLog数据合并到AutoTask中
     * 只合并大文本字段：applyJson、resultStr、feedbackJson
     *
     * @param autoTask    AutoTask实体
     * @param autoTaskLog HBase中的AutoTaskLog数据
     * @return 合并后的AutoTask实体
     */
    private AutoTask mergeHBaseData(AutoTask autoTask, AutoTaskLog autoTaskLog) {
        if (autoTask == null || autoTaskLog == null) {
            return autoTask;
        }

        autoTask.setApplyJson(autoTaskLog.getApplyJson());
        autoTask.setResultStr(autoTaskLog.getResultStr());
        autoTask.setFeedbackJson(autoTaskLog.getFeedbackJson());

        return autoTask;
    }

    /**
     * 保存数据到HBase
     * 异步保存，失败不影响主流程
     *
     * @param autoTask AutoTask实体
     */
    private void saveToHBase(AutoTask autoTask) {
        if (autoTask == null || autoTask.getAutoTraceId() == null) {
            return;
        }

        try {
            AutoTaskLog autoTaskLog = convertToAutoTaskLog(autoTask);
            autoTaskLogRepository.save(autoTaskLog);
            log.debug("成功保存数据到HBase，autoTraceId: {}", autoTask.getAutoTraceId());
        } catch (Exception e) {
            log.error("保存数据到HBase失败，autoTraceId: {}, 错误: {}",
                autoTask.getAutoTraceId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新HBase中的数据
     * 失败不影响主流程
     *
     * @param autoTask AutoTask实体（包含要更新的字段）
     */
    private void updateHBaseData(AutoTask autoTask) {
        if (autoTask == null || autoTask.getAutoTraceId() == null) {
            return;
        }

        try {
            // 先查询现有数据
            AutoTaskLog existingLog = autoTaskLogRepository.findByAutoTraceId(autoTask.getAutoTraceId());

            if (existingLog != null) {
                // 更新现有数据
                if (autoTask.getApplyJson() != null) {
                    existingLog.setApplyJson(autoTask.getApplyJson());
                }
                if (autoTask.getResultStr() != null) {
                    existingLog.setResultStr(autoTask.getResultStr());
                }
                if (autoTask.getFeedbackJson() != null) {
                    existingLog.setFeedbackJson(autoTask.getFeedbackJson());
                }

                autoTaskLogRepository.save(existingLog);
                log.debug("成功更新HBase数据，autoTraceId: {}", autoTask.getAutoTraceId());
            } else {
                // 如果不存在，则创建新记录
                saveToHBase(autoTask);
            }
        } catch (Exception e) {
            log.error("更新HBase数据失败，autoTraceId: {}, 错误: {}",
                autoTask.getAutoTraceId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 统一处理HBase数据合并的方法
     * 如果使用新分表，从HBase获取大文本字段并合并到AutoTask中
     *
     * @param autoTask AutoTask实体
     * @param context  上下文信息，用于日志记录
     */
    private void mergeHBaseDataIfNeeded(AutoTask autoTask, String context) {
        if (autoTask != null && !OldTableSwitch.isUseOldTable()) {
            log.debug("开始处理HBase数据合并-[{}]\n{}", context, autoTask);
            try {
                AutoTaskLog autoTaskLog = autoTaskLogRepository.findByAutoTraceId(autoTask.getAutoTraceId());
                mergeHBaseData(autoTask, autoTaskLog);
            } catch (IOException e) {
                log.error("获取任务日志失败-[{}]\n{}", context, ExceptionUtils.getStackTrace(e));
            }
        }
    }

    /**
     * 统一处理HBase数据合并的方法（批量处理）
     * 如果使用新分表，从HBase获取大文本字段并合并到AutoTask列表中
     *
     * @param autoTasks AutoTask列表
     * @param context   上下文信息，用于日志记录
     */
    private void mergeHBaseDataIfNeeded(List<AutoTask> autoTasks, String context) {
        if (autoTasks != null && !autoTasks.isEmpty() && !OldTableSwitch.isUseOldTable()) {
            for (AutoTask autoTask : autoTasks) {
                try {
                    AutoTaskLog autoTaskLog = autoTaskLogRepository.findByAutoTraceId(autoTask.getAutoTraceId());
                    mergeHBaseData(autoTask, autoTaskLog);
                } catch (IOException e) {
                    log.error("获取任务日志失败-[{},autoTraceId:{}]\n{}",
                        context, autoTask.getAutoTraceId(), ExceptionUtils.getStackTrace(e));
                }
            }
        }
    }

    /**
     * 双写操作的通用wrapper方法
     *
     * @param entity        实体对象
     * @param operation     具体的数据库操作（lambda表达式）
     * @param operationName 操作名称，用于日志记录
     * @return 操作结果
     */
    private boolean executeWithDoubleWrite(AutoTask entity,
                                           Function<AutoTask, Boolean> operation,
                                           String operationName) {
        try {
            // 操作旧表
            ShardingContextHolder.setUseOldTable(true);
            // 执行数据库操作
            operation.apply(entity);

            // 操作新表
            ShardingContextHolder.setUseOldTable(false);
            // 备份原始大文本字段数据
            String originalApplyJson = entity.getApplyJson();
            String originalResultStr = entity.getResultStr();
            String originalFeedbackJson = entity.getFeedbackJson();

            // 清空大文本字段，避免保存/更新到新分表
            entity.setApplyJson(null);
            entity.setResultStr(null);
            entity.setFeedbackJson(null);

            operation.apply(entity);

            // 恢复原始数据用于HBase保存
            entity.setApplyJson(originalApplyJson);
            entity.setResultStr(originalResultStr);
            entity.setFeedbackJson(originalFeedbackJson);

            return true;
        } catch (Exception e) {
            log.error("{}双写操作失败", operationName, e);
            throw new RuntimeException(operationName + "双写操作失败", e);
        } finally {
            ShardingContextHolder.clearUseOldTable();
        }
    }

    private boolean withoutBigTextFunc(AutoTask entity, Function<AutoTask, Boolean> operation) {
        boolean flag = false;
        // 备份原始大文本字段数据
        String originalApplyJson = entity.getApplyJson();
        String originalResultStr = entity.getResultStr();
        String originalFeedbackJson = entity.getFeedbackJson();

        // 清空大文本字段，避免保存/更新到新分表
        entity.setApplyJson(null);
        entity.setResultStr(null);
        entity.setFeedbackJson(null);

        flag = operation.apply(entity);

        // 恢复原始数据用于HBase保存
        entity.setApplyJson(originalApplyJson);
        entity.setResultStr(originalResultStr);
        entity.setFeedbackJson(originalFeedbackJson);
        return flag;
    }

    private boolean saveWithDoubleWrite(AutoTask entity) {
        return executeWithDoubleWrite(entity, e -> {
            super.save(e);
            return true;
        }, "保存");
    }

    private boolean updateWithDoubleWrite(AutoTask entity) {
        return executeWithDoubleWrite(entity, e -> {
            super.updateById(e);
            return true;
        }, "更新");
    }

}
