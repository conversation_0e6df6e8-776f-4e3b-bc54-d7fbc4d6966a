package com.cheche365.bc.river.service.callback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.utils.tai_bao.MessageConstants;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.cheche365.bc.handler.edi.BoHaiHandler.INSURER_CODE;
import static com.cheche365.bc.handler.edi.BoHaiHandler.SYS_SOURCE_CODE;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
@SuppressWarnings("unchecked")
public class BoHaiCallBackHandler extends AbstractCallBackHandler {

    @Override
    public boolean needHandle(String companyId) {
        return String.valueOf(InsCompanyEnum.BPIC.getCode()).equals(companyId);
    }

    @Override
    public String handleCallBackBody(Map param) {
        try {
            log.info("处理保司回调报文原始param：{}", JSON.toJSONString(param));
            String callbackBody = (String) param.get("callbackBody");
            JSONObject jsonObject = JSONObject.parseObject(callbackBody);
            RSA rsa = createRsa(param);
            return rsa.decrypt(jsonObject.getString(MessageConstants.BIZ_CONTENT));
        } catch (Exception e) {
            log.error("渤海回调解析保司报文异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    @Override
    public String handleResponseBody(Map param) {
        try {
            log.info("渤海回调响应保司报文原始param：{}", JSON.toJSONString(param));
            Map<String, Object> config = (Map<String, Object>) ((Map) param.get("dataSource")).get("config");
            Map<String, Object> responseMap = Maps.newHashMap();
            responseMap.put(SYS_SOURCE_CODE, config.get(SYS_SOURCE_CODE));
            responseMap.put(INSURER_CODE, config.get(INSURER_CODE));
            String responseContent = (String) param.get("responseContent");
            RSA rsa = createRsa(param);
            responseMap.put(MessageConstants.BIZ_CONTENT, rsa.encrypt(responseContent));
            return JSONObject.toJSONString(responseMap);
        } catch (Exception e) {
            log.error("渤海回调响应保司报文渲染异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    private RSA createRsa(Map param) throws Exception {
        Map<String, Object> config = (Map<String, Object>) ((Map) param.get("dataSource")).get("config");
        return RSA.createRsa(config);
    }

}
