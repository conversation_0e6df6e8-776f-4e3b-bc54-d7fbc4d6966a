package com.cheche365.bc.config;

import ch.qos.logback.core.status.Status;
import ch.qos.logback.core.status.StatusListener;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.utils.IMUtil;
import org.apache.commons.compress.utils.Lists;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.FileSystemException;
import java.util.List;

public class CustomStatusListener implements StatusListener {

    private static final String HOSTNAME = getHostName();

    private static final String MESSAGE_TEMPLATE = "message:{}\nhostname:{}\ntimestamp:{}\nstacktrace:{}";

    private static final String MESSAGE_TEMPLATE_START = "hostname:{}\ntimestamp:{}\nmessage:服务器启动";

    private static final List<String> ERROR_KEYWORDS = Lists.newArrayList();

    static {
        //获取环境变量
        String message = createStartText();
        IMUtil.sendFeiShuMessage(message);
        ERROR_KEYWORDS.add("No space");
        ERROR_KEYWORDS.add("Disk quota exceeded");
    }

    @Override
    public void addStatusEvent(Status status) {
        if (status.getLevel() == Status.ERROR
            && needSend(status.getThrowable())) {
            String message = createErrorText(status);
            IMUtil.sendFeiShuMessage(message);
        }
    }

    private boolean needSend(Throwable throwable) {
        if (throwable instanceof FileSystemException) {
            return true;
        }
        if (throwable instanceof IOException) {
            String message = throwable.getMessage();
            return message != null && ERROR_KEYWORDS.stream().anyMatch(message::contains);
        }
        return false;
    }

    private static String createStartText() {
        //时间
        String datetime = DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN);
        //错误信息
        return StrUtil.format(MESSAGE_TEMPLATE_START, HOSTNAME, datetime);
    }

    private String createErrorText(Status status) {
        //时间
        String datetime = DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN);
        //错误信息
        String message = status.getMessage();
        Throwable throwable = status.getThrowable();
        //堆栈信息
        String stacktraceStr = ExceptionUtil.stacktraceToString(throwable);
        return StrUtil.format(MESSAGE_TEMPLATE, message, HOSTNAME, datetime, stacktraceStr);
    }

    private static String getHostName() {
        //主机名
        String hostName = "";
        try {
            InetAddress ia = InetAddress.getLocalHost();
            hostName = ia.getHostName();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return hostName;
    }
}
