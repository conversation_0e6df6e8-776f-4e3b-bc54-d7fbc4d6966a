package com.cheche365.bc.utils.encrypt.hulianhutong.zhonghang;

import java.util.HashMap;


public class RequestParametersHolder {
    private HashMap protocalMustParams;

    private HashMap protocalOptParams;

    private HashMap applicationParams;

    public HashMap getProtocalMustParams() {
        return this.protocalMustParams;
    }

    public void setProtocalMustParams(HashMap protocalMustParams) {
        this.protocalMustParams = protocalMustParams;
    }

    public HashMap getProtocalOptParams() {
        return this.protocalOptParams;
    }

    public void setProtocalOptParams(HashMap protocalOptParams) {
        this.protocalOptParams = protocalOptParams;
    }

    public HashMap getApplicationParams() {
        return this.applicationParams;
    }

    public void setApplicationParams(HashMap applicationParams) {
        this.applicationParams = applicationParams;
    }
}

