package robotwin;

import akka.actor.ActorRef;
import akka.actor.ActorSelection;
import akka.actor.ActorSystem;
import akka.actor.Props;
import akka.pattern.Patterns;
import com.alibaba.fastjson.JSON;
import com.cheche365.bc.actor.BaseActor;
import com.cheche365.bc.actor.InsActor;
import com.cheche365.bc.actor.msg.StatusMsg;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.task.AutoTask;
import org.junit.Before;
import org.junit.Test;
import scala.concurrent.Await;
import scala.concurrent.Future;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 精灵窗口化服务的测试用例
 *
 * <AUTHOR>
 * @Created by austinChen on 2017/4/19 18:21.
 */
public class RobotWinTest {


    private ActorSystem actorSystem;


    public final static ConcurrentMap<String, String> topActors = new ConcurrentHashMap<>();


    @Before
    public void init() {
        actorSystem = ActorSystem.create("sys");

    }

    @Test
    public void testAccountAssign() throws Exception {
        // accept(gen("粤A"+0,"2005","人保","rbaccount"));
        // Thread.sleep(5*1000L);
        for (int i = 0; i < 100; i++) {
            accept(gen("粤A" + i, "2005", "人保", "rbaccount"));
        }
        for (int i = 0; i < 100; i++) {
            accept(gen("粤A" + i, "2004", "太保", "tbaccount"));
        }
        String accountKeyRb="2005-rbaccount";
        String accountKeyTb="2004-tbaccount";
        Thread.sleep(10*1000L);
        writeState(accountKeyRb);
        writeState(accountKeyTb);
        Thread.sleep(60 * 1000L);
        writeState(accountKeyRb);
        writeState(accountKeyTb);

        writeState("abbdsdf");


    }

    public void writeState(String key) throws  Exception
    {
        if(BaseActor.allActors.containsKey(key)) {
            ActorSelection actor = actorSystem.actorSelection(BaseActor.allActors.get(key));
            Future<Object> future = Patterns.ask(actor, StatusMsg.getInstance(), BaseActor.timeout);
            Object result = Await.result(future, BaseActor.timeout.duration());
            System.out.println(JSON.toJSONString(result));
        }
        else
        {

        }
    }

    /**
     * 使用这必须保证key的Actor存在，要不消息会丢失
     *
     * @param key     路径key
     * @param clazz   需要新建的Actor类对象
     * @param objects clazz对象构建的参数列表
     * @param msg     消息实体
     */
    public void tell(String key, Object msg, Class clazz, Object... objects) {
        synchronized (key) {
//            log.info("开始给{}发送任务", key);
            if (topActors.containsKey(key)) {
                ActorSelection actorSelection = actorSystem.actorSelection("akka://sys/user/" + key);
                actorSelection.tell(msg, null);
            } else if (clazz != null) {
                ActorRef actor = actorSystem.actorOf(Props.create(clazz, objects), key);
                System.out.println("path:" + actor.path().toString());
                actor.tell(msg, null);
                topActors.put(key, actor.path().toString());
            }
//            log.info("完成给{}发送任务", key);
        }
    }

    public void accept(TransTaskMsg taskMsg) {
        tell(taskMsg.getComId(), taskMsg, InsActor.class, taskMsg.getComId(), taskMsg.getComName(),null);
    }

    /**
     * @param traceKey 关键字，车牌。或者vin
     * @param comId    保险公司id
     * @param comName  保险公司名字
     * @param account  使用的账号
     * @return
     */
    public TransTaskMsg gen(String traceKey, String comId, String comName, String account) {
        AutoTask autoTask = new AutoTask();
        autoTask.setTraceKey(traceKey);
        TransTaskMsg taskMsg = new TransTaskMsg();
        taskMsg.setAutoTask(autoTask);
        taskMsg.setComId(comId);
        taskMsg.setComName(comName);
        taskMsg.setAccount(account);
        return taskMsg;
    }
}
