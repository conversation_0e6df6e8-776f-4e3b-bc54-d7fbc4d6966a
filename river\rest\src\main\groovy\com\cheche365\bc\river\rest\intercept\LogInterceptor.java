package com.cheche365.bc.river.rest.intercept;

import com.cheche365.bc.utils.web.IpUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

/**
 * 获取请求方的 IP 地址并添加在 log 日志中
 * Created by xcl on 2020-6-8.
 */
@Component
@Slf4j
public class LogInterceptor implements HandlerInterceptor {

    private ThreadLocal<Long> startTime = new ThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        startTime.set(System.currentTimeMillis());
        MDC.put("ip", IpUtil.getRemoteIp(request));
        MDC.put("flowId", UUID.randomUUID().toString());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        MDC.remove("ip");
        MDC.remove("flowId");
        if (log.isDebugEnabled()) {
            long spentTime = System.currentTimeMillis() - startTime.get();
            if (spentTime > 500) {
                log.debug("found slow request {}, it spent {} milliseconds.", request.getRequestURL(), spentTime);
            }
        }
        startTime.remove();
    }
}
