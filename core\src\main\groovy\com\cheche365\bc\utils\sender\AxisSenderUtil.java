package com.cheche365.bc.utils.sender;

import org.apache.axis2.addressing.EndpointReference;
import org.apache.axis2.client.Options;
import org.apache.axis2.rpc.client.RPCServiceClient;

import javax.xml.namespace.QName;


public class AxisSenderUtil {

    public static String send(String url, String body, QName qName, long timeout) throws Exception {
        RPCServiceClient client = new RPCServiceClient();
        Options options = client.getOptions();
        EndpointReference end = new EndpointReference(url);
        options.setTo(end);
        options.setTimeOutInMilliSeconds(timeout);
        Object[] obj = new Object[]{body};
        Class<?>[] classes = new Class[]{String.class};
        return (String) client.invokeBlocking(qName, obj, classes)[0];
    }
}
