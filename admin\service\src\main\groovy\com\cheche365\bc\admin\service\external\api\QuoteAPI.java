package com.cheche365.bc.admin.service.external.api;

import com.cheche365.bc.admin.service.external.dto.QuoteRequest;
import com.cheche365.bc.admin.service.external.dto.QuoteResp;
import org.springframework.stereotype.Service;

import javax.ws.rs.HttpMethod;

import static cn.hutool.json.JSONUtil.toJsonStr;

@Service
public class QuoteAPI extends BaseQuotePlatformAPI<QuoteResp> {

    @Override
    public Class<?> responseType() {
        return QuoteResp.class;
    }

    @Override
    public String path() {
        return "/partners/quote";
    }

    @Override
    public String method() {
        return HttpMethod.POST;
    }

    public QuoteResp doQuote(QuoteRequest quoteRequest) {
        return super.call(toJsonStr(quoteRequest));
    }

}
