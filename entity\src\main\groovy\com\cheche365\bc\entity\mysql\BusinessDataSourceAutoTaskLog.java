package com.cheche365.bc.entity.mysql;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href="<EMAIL>">蒋昌宝<a/>
 */

@TableName(value = "tb_data_source_log")
@Data
@Accessors(chain = true)
public class BusinessDataSourceAutoTaskLog {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 上级渠道 ID
     */
    @TableField(value = "parent_id")
    private Integer parentId;

}
