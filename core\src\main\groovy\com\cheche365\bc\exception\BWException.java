package com.cheche365.bc.exception;

public class BWException extends Exception {


    protected transient Object returnValue;

    protected Integer errorCode;

    public BWException(Integer errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public Object getReturnValue() {
        return returnValue;
    }

    public void setReturnValue(Object returnValue) {
        this.returnValue = returnValue;
    }

    public BWException() {
        super();
    }

    public BWException(String message) {
        super(message);
    }

    public BWException(Throwable cause) {
        super(cause);
    }

    public BWException(String message, Throwable cause) {
        super(message, cause);
    }

    protected BWException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
