package com.cheche365.bc.dto;

import com.cheche365.bc.entity.mysql.MenuInfo;
import com.cheche365.bc.entity.mysql.PermissionInfo;
import lombok.Data;

import java.util.List;

@Data
public class MenuPermission {

    /**
     * id
     */
    private Long id;
    /**
     * code
     */
    private String code;
    /**
     * name
     */
    private String name;
    /**
     * 类型
     */
    private MenuPermissionType type;

    /**
     * 子列表
     */
    private List<MenuPermission> subList;

    /**
     * 获取菜单id
     *
     * @return
     */
    public Long realMenuId() {
        return id - MENU_DEFAULT_BASE_ID;
    }

    /**
     * 菜单默认id，避免与权限id冲突
     */
    public final static Long MENU_DEFAULT_BASE_ID = 100000L;

    /**
     * 菜单 权限
     */
    public enum MenuPermissionType {
        /**
         * 菜单 权限
         */
        MENU, PERMISSION,
        ;
    }

    /**
     * 菜单
     *
     * @param menuInfo
     * @return
     */
    public static MenuPermission of(MenuInfo menuInfo) {
        if (menuInfo == null) {
            return null;
        }
        MenuPermission mp = new MenuPermission();
        mp.setType(MenuPermissionType.MENU);
        mp.setId(MENU_DEFAULT_BASE_ID + menuInfo.getId());
        mp.setCode(menuInfo.getMenuCode());
        mp.setName(menuInfo.getName());
        return mp;
    }

    /**
     * 权限
     *
     * @param permissionInfo
     * @return
     */
    public static MenuPermission of(PermissionInfo permissionInfo) {
        if (permissionInfo == null) {
            return null;
        }
        MenuPermission mp = new MenuPermission();
        mp.setType(MenuPermissionType.PERMISSION);
        mp.setId(permissionInfo.getId());
        mp.setCode(permissionInfo.getPermissionCode());
        mp.setName(permissionInfo.getName());
        return mp;
    }
}
