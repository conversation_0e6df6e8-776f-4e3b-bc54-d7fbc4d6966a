package com.cheche365.bc.admin.service.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
public class BaseQuoteDto {

    @ExcelProperty("configId")
    @Setter
    private String configId;
    @ExcelProperty("城市Id")
    @Setter
    private String cityId;
    @ExcelProperty("车主证件类型")
    private String ownerIdCardType;
    @ExcelProperty("车主身份证")
    @Setter
    private String ownerIdCardId;
    @ExcelProperty("车主手机号")
    @Setter
    private String ownerMobile;
    @ExcelProperty("车主姓名")
    @Setter
    private String ownerName;
    @ExcelProperty("投保人证件类型")
    private String applicantIdCardType;
    @ExcelProperty("投保人身份证")
    @Setter
    private String applicantIdCardId;
    @ExcelProperty("投保人手机")
    @Setter
    private String applicantMobile;
    @ExcelProperty("投保人姓名")
    @Setter
    private String applicantName;
    @ExcelProperty("被保人证件类型")
    private String insuredIdCardType;
    @ExcelProperty("被保人身份证")
    @Setter
    private String insuredIdCardId;
    @ExcelProperty("被保人手机")
    @Setter
    private String insuredMobile;
    @ExcelProperty("被保人姓名")
    @Setter
    private String insuredName;
    @ExcelProperty("车牌号")
    @Setter
    private String licensePlateNo;
    @ExcelProperty("车架号")
    @Setter
    private String vinNo;
    @ExcelProperty("发动机号")
    @Setter
    private String engineNo;
    @ExcelProperty("是否新车")
    @Setter
    private String newCarFlag;
    @ExcelProperty("使用性质")
    private String useCharacter;
    @ExcelProperty("燃料类型")
    private String fuelType;
    @ExcelProperty("新车购置价")
    @Setter
    private BigDecimal purchasePrice;
    @ExcelProperty("初等日期")
    private String enrollDate;
    @ExcelProperty("车型Id")
    @Setter
    private String vehicleId;
    @ExcelProperty("过户日期")
    @Setter
    private String transferDate;
    @ExcelProperty("非车code")
    @Setter
    private String nonAutoProductCode;
    @ExcelProperty("商业险起保日期")
    @Setter
    private String bizStartDate;
    @ExcelProperty("交强险起保日期")
    @Setter
    private String efcStartDate;

    public void setUseCharacter(String useCharacter) {
        this.useCharacter = useCharacter.split("-")[1];
    }

    public void setFuelType(String fuelType) {
        this.fuelType = fuelType.split("-")[1];
    }

    public void setEnrollDate(String enrollDate) {
        this.enrollDate = enrollDate.substring(0, 10);
    }

    public void setOwnerIdCardType(String ownerIdCardType) {
        this.ownerIdCardType = ownerIdCardType.split("-")[1];
    }

    public void setApplicantIdCardType(String applicantIdCardType) {
        this.applicantIdCardType = applicantIdCardType.split("-")[1];
    }

    public void setInsuredIdCardType(String insuredIdCardType) {
        this.insuredIdCardType = insuredIdCardType.split("-")[1];
    }
}
