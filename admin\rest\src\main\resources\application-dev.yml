spring:
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:sharding-jdbc.yml?placeholder-type=environment
  #  datasource:
  #    dynamic:
  #      datasource:
  #        bcs:
  #          username: river_zzb
  #          password: Prfc65_fkzqU
  #          url: ****************************************************************************************************************************************************************
  data:
    mongodb:
      database: river_zzb
      host: *************
      port: 27017
      username: admin
      password: admin123
    redis:
      host: *************
      port: 6379
      password: cheche
      database: 0

export:
  file-path: /data/tmp

#api-interface
api:
  sign-val: 8f0e85d2-b128-6aa3-a377-dcf0f879078c

schedule:
  xxl:
    job:
      admin:
        addresses: http://************:8081/xxl-job-admin
      executor:
        ip: ************
        port: 8989

app:
  key: f87b8ba2
cjy:
  password: zzb2018#
  soft_id: 895193
  username: zhangzhongbao
cos:
  appID: ********
  bucketName: newzzbdev
  secretID: AKIDhvjglwX43Fokcrwf2Tcbxn0qrFP086xt
  secretKey: z09FxlCheYxirVlW90pgMG6QKnFrlwi1
ff:
  app_id: 303046
  app_key: j0KUUhetOuV4sskHG45C3YbbhTH4VAkc
  pd_id: 102846
  pd_key: vyzqgJu9iFV19yohAYeE5okbWhv2MGJG
pull:
  accounts:
    url: https://i.bedrock.chetimes.com
sdas:
  app:
    key: 17e209f5-7a8c-4ad9-8e90-2e8e4232a99f
  secret:
    key: abfff95c-4bcb-4f40-ade6-29bd113cf738
  url: http://sdas.h.bedrock.chetimes.com/api/assets
secret:
  key: 790e-43da-9078-af838d0fd3c0
system:
  akkaPath: /opt/deps/tomcat/webapps/ins-auto-service/WEB-INF/classes/akka.conf
  dataTransFormPath: /msgParseRule.json

ep:
  domain: 127.0.0.1:8081
