<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- Define the status listener -->
    <statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener"/>
    <define name="hostname" class="com.cheche365.bc.config.CanonicalHostNamePropertyDefiner"/>
    <springProperty scope="context" name="LOG_PATH" source="logging.file.path" defaultValue="/log"/>

    <property name="LOG_PATTERN"
              value="[%d{yyyy/MM/dd HH:mm:ss}][%X{tid}][%X{flowId}][%thread][%X{ip}][%-5level][%class{10}:%M:%line][%msg]%n"/>

    <!-- Define the console appender -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="file-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${hostname}/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${hostname}/%d{yyyy-MM-dd}/app.%i.log.gz</fileNamePattern>
            <MaxHistory>50</MaxHistory>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${LOG_PATTERN}</pattern>
            </layout>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="console"/>
        <SpringProfile name="!dev">
            <appender-ref ref="file-log"/>
        </SpringProfile>
    </root>
</configuration>