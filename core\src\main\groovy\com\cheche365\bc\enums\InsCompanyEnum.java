package com.cheche365.bc.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum InsCompanyEnum {

    /**
     * 保险公司
     */
    GPIC(2002, "guoshou","国寿财险"),
    PICC(2005, "renbao","人保财险"),
    PAIC(2007, "pingan","平安财险"),
    CPIC(2011, "taipingyang","太平洋财险"),
    TPIC(2016, "taiping","太平财险"),
    SMIC(2019, "yangguang","阳光财险"),
    CCIC(2021, "dadi","大地财险"),
    AICS(2022, "yongcheng","永诚财险"),
    HTIC(2023, "huatai","华泰财险"),
    DAIC(2024, "dajia","大家财险"),
    TPBX(2026, "tianping","安盛天平财险"),
    CICP(2027, "zhonghua","中华联合财险"),
    BPIC(2041, "bohai","渤海财险"),
    DBIC(2042, "dubang","都邦财险"),
    HAIC(2043, "huaan","华安财险"),
    UTIC(2044, "zhongcheng","众诚财险"),
    TAIC(2045, "tianan","天安财险"),
    YAIC(2046, "yongan","永安财险"),
    JTIC(2050, "jintai","锦泰财险"),
    ACIC(2058, "ancheng","安诚财险"),
    HBIC(2062, "hengbang","恒邦财险"),
    HNIC(2064, "huanong","华农财险"),
    CHAC(2065, "chengtai","诚泰财险"),
    YTIC(2066, "yatai","亚太财险"),
    BWBX(2072, "beibuwan","北部湾财险"),
    ZMBX(2076, "zhongmei","中煤财险"),
    CRIC(2077, "fude","富德财险"),
    LIHI(2085, "libao","利宝财险"),
    DHIC(2088, "dinghe","鼎和财险"),
    ZKIC(2095, "zijin","紫金财险"),
    XDCX(2096, "guoren","国任财险"),
    TKCX(4002, "taikang","泰康财险"),
    AHIC(2068, "anhua","安华农业"),
    CJCX(2051, "changjiang","长江财险"),
    YZIC(2063, "yanzhao","燕赵财险"),
    ALIC(2049, "anlian","京东安联"),
    ULIC(2059, "hezhong","合众财险");

    @EnumValue
    private final int code;

    private final String text;

    private final String desc;


    public static InsCompanyEnum get(int value) {
        return Arrays.stream(values()).filter(it -> it.getCode() == value).findAny().orElse(null);
    }

    public static InsCompanyEnum get(String text) {
        return Arrays.stream(values()).filter(it -> it.getText().equals(text)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }

}
