package com.cheche365.bc.admin.service.dto.base;

import lombok.Builder;
import lombok.Data;

import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMAT;
import static cn.hutool.core.date.DateUtil.format;
import static cn.hutool.core.util.IdcardUtil.getBirthDate;
import static cn.hutool.core.util.IdcardUtil.getGenderByIdCard;
import static com.cheche365.bc.enums.IdentityTypeEnum.ID_CARD;

@Data
@Builder
public class PersonInfo {

    private Integer idCardType;
    private String idCard;
    private String name;
    private String mobile;
    private Integer sex;
    private String birthday;

    public static PersonInfo createPersonInfo(String idCardType, String idCard, String mobile, String name) {
        return PersonInfo.builder()
            .idCard(idCard)
            .mobile(mobile)
            .name(name)
            .idCardType(Integer.valueOf(idCardType))
            .sex(ID_CARD.getId().equals(idCardType) ? getGenderByIdCard(idCard) ^ 1 : null)
            .birthday(ID_CARD.getId().equals(idCardType) ? format(getBirthDate(idCard), NORM_DATE_FORMAT) : null)
            .build();
    }

}
