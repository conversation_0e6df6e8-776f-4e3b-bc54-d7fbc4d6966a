package com.cheche365.bc.river.rest.controller;

import akka.actor.ActorRef;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.actor.TaskActor;
import com.cheche365.bc.entity.mysql.BusinessDataSource;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.message.Reply;
import com.cheche365.bc.river.rest.dto.request.AutoRequest;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.BusinessDataSourceService;
import com.cheche365.bc.service.InsWorkerTimeService;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.MetaData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 自动任务控制器
 * Created by austin on 16/5/19.
 */
@Slf4j
@RestController
@Lazy
public class AutoController {

    @Resource
    private TaskActor taskActor;
    @Resource
    private AutoTaskService taskService;
    @Resource
    private InsWorkerTimeService workerTimeService;
    @Resource
    private InterfaceService interfaceService;
    @Resource
    private BusinessDataSourceService businessDataSourceService;

    @RequestMapping(value = "/auto", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Object auto(@RequestBody @Validated AutoRequest autoRequest, @RequestHeader(value = "Is-Source-Cheche", required = false) String isSourceCheche) {
        String requestBody = JSON.toJSONString(autoRequest);
        log.info("auto 收到请求:{}", requestBody);
        AutoTask task = initAutoTask(autoRequest, requestBody);

        // 内外网标识
        if (StringUtils.isNotBlank(isSourceCheche)) {
            autoRequest.setIsSourceCheche(isSourceCheche);
        }

        //校验请求来源
        if (initRequestSource(task, autoRequest)) {
            return Reply.reply(autoRequest.getEnquiryId().split("@")[0], false, true, autoRequest.getTaskStatus(), "sourceProduct 与 sourceScenario 大类未配置");
        }
        //如果是精灵任务，需要根据时间段进行校验，是否是工作时间
        if (StrUtil.equals(ProcessTypeEnum.ROBOT.getKey(), autoRequest.getProcessType())) {
            String comId = StringUtils.left(autoRequest.getCompanyId(), 4);
            String checkResult = workerTimeService.checkWorkTime(comId);
            if (StringUtils.isNoneEmpty(checkResult)) {
                return Reply.reply(autoRequest.getEnquiryId().split("@")[0], false, true, autoRequest.getTaskStatus(), checkResult);
            }
        }

        if (!interfaceService.hasInterfaceAbility(task.getTaskType())) {
            task.setConcatResultStr("自动化服务暂无能力处理该任务");
            taskService.save(task);
            return Reply.reply(task.getTraceKey(), false, true, autoRequest.getTaskStatus(), "自动化服务暂无能力处理该任务");
        }

        try {
            taskService.save(task);
        } catch (Exception e) {
            log.error("任务：{} 存储任务日志异常：{}", autoRequest.getEnquiryId(), ExceptionUtils.getStackTrace(e));
            return Reply.reply(task.getTraceKey(), false, false, autoRequest.getTaskStatus(), "任务执行异常");
        }
        taskActor.self().tell(task, ActorRef.noSender());
        Map reply = Reply.reply(task.getTraceKey(), true, true, autoRequest.getTaskStatus());
        log.info("Response: {}", reply);
        return reply;
    }

    /**
     * 初始化任务对象
     *
     * @param request     请求参数
     * @param requestBody 请求参数的json串
     * @return AutoTask
     */
    private AutoTask initAutoTask(AutoRequest request, String requestBody) {
        String autoTraceId = IdUtil.randomUUID();

        request.setAutoTraceId(autoTraceId);
        //创建任务对象
        AutoTask task = new AutoTask();
        var metaData = MetaData.buildByMDC();
        task.setMetaData(metaData);
        task.setConcatResultStr("flowId:" + metaData.getFlowId());
        task.setConcatResultStr(requestBody);
        task.setStartTime(LocalDateTime.now());
        task.setAutoTraceId(autoTraceId);

        task.setTraceKey(request.getEnquiryId().split("@")[0]);
        task.setCallBackUrl(request.getCallBackUrl());
        task.setWriteBackUrl(request.getWriteBackUrl());
        task.setCompanyId(request.getCompanyId());

        //taskType定义 约定{robot/edi}-{comId}-{taskType}
        task.setTaskType(String.format("%s-%s-%s", request.getProcessType(), StringUtils.left(request.getCompanyId(), 4), request.getTaskType()));
        task.setRequestUrl(request.getRequestDataUrl());
        task.setPlatFormSaveUrl(request.getPlatformSaveUrl());
        task.setRuleUrl(request.getRuleUrl());
        task.setRuleId(request.getRuleid());
        // 临时变量
        Map<String, Object> tempValues = task.getTempValues();
        tempValues.putAll(JSONObject.parseObject(requestBody));
        tempValues.put("monitorid", request.getMonitorid());
        tempValues.put("buybusitype", request.getBuybusitype());
        tempValues.put("processType", request.getProcessType());
        tempValues.put("isReservedRes", "");

        return task;
    }

    /**
     * 判断是否是允许的来源
     *
     * @param task    任务对象
     * @param request 请求对象
     * @return true: 不允许 false: 允许
     */
    private boolean initRequestSource(AutoTask task, AutoRequest request) {
        if (Objects.nonNull(request.getRequestSource())) {
            AutoRequest.RequestSource requestSource = request.getRequestSource();
            if (StringUtils.isAnyBlank(requestSource.getSourceProduct(), requestSource.getSourceScenario(), requestSource.getSourceChannel())) {
                return true;
            }
            AutoTask.RequestSource tRequestSource = new AutoTask.RequestSource();
            BeanUtil.copyProperties(requestSource, tRequestSource);
            task.setRequestSource(tRequestSource);
            List<BusinessDataSource> dataSourceList = businessDataSourceService.getDataSourceWithProductAndScenario(requestSource.getSourceProduct(), requestSource.getSourceScenario());
            return dataSourceList.isEmpty();
        }
        return false;
    }
}
