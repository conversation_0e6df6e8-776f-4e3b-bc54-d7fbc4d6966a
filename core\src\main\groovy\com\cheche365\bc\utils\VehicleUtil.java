package com.cheche365.bc.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;
import org.apache.commons.lang.exception.ExceptionUtils;

/**
 * 车辆号码校验等工具类
 *
 * <AUTHOR>
 * Created by austinChen on 2016/1/20 11:04.
 */
@Slf4j
public final class VehicleUtil {
    
    private static final DualHashBidiMap<String, String> map = new DualHashBidiMap<>();

    static {
        map.put("110000", "京");
        map.put("120000", "津");
        map.put("130000", "冀");
        map.put("140000", "晋");
        map.put("150000", "蒙");
        map.put("210000", "辽");
        map.put("220000", "吉");
        map.put("230000", "黑");
        map.put("310000", "沪");
        map.put("320000", "苏");
        map.put("330000", "浙");
        map.put("340000", "皖");
        map.put("350000", "闽");
        map.put("360000", "赣");
        map.put("370000", "鲁");
        map.put("410000", "豫");
        map.put("420000", "鄂");
        map.put("430000", "湘");
        map.put("440000", "粤");
        map.put("450000", "桂");
        map.put("460000", "琼");
        map.put("500000", "渝");
        map.put("510000", "川");
        map.put("520000", "贵");
        map.put("530000", "云");
        map.put("540000", "藏");
        map.put("610000", "陕");
        map.put("620000", "甘");
        map.put("630000", "青");
        map.put("640000", "宁");
        map.put("650000", "新");
        map.put("810000", "港");
        map.put("820000", "澳");
        map.put("710000", "台");
    }

    public static String getAlias(String prvCode) {
        try {
            return map.get(prvCode);
        } catch (Exception ex) {
            log.error("根据省份代码获取车牌前缀出错:{},{}", prvCode, ExceptionUtils.getStackTrace(ex));
            throw ex;
        }
    }
}
