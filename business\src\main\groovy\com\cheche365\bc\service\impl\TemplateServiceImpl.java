package com.cheche365.bc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.entity.mysql.Template;
import com.cheche365.bc.mapper.TemplateNewMapper;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.TemplateService;
import com.cheche365.bc.task.AutoTask;
import groovy.lang.Script;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-10
 */
@Slf4j
@Service
@AllArgsConstructor
public class TemplateServiceImpl extends ServiceImpl<TemplateNewMapper, Template> implements TemplateService {

    private InterfaceService interfaceService;

    public void loadTemplate(Interface itf, AutoTask t) {
        Object taskEntity = t.getTaskEntity() instanceof Map ? (Map) (t.getTaskEntity()) : t;
        List<Template> templates = findTemplates(t.getTaskType(), taskEntity);
        if (CollectionUtil.isNotEmpty(templates)) {
            if (CollectionUtil.isNotEmpty(itf.getTemplates())) {
                itf.getTemplates().clear();
            }
            templates.forEach(template ->
                    itf.getTemplates().add(template)
            );
        }
    }

    @Override
    public List<Template> findTemplates(String taskType, Object autoTask) {

        try {
            Script script = interfaceService.getInterfaceScriptByIntType(taskType);
            String templateGroups = (String) script.invokeMethod("getTemplateGroup", autoTask);
            if (StrUtil.isBlank(templateGroups)) {
                return null;
            }
            log.info("任务接口实际加载的流程为:{}", templateGroups);

            return Arrays.stream(templateGroups.trim().split(","))
                    .map(name ->
                            getOne(new QueryWrapper<Template>().lambda().eq(Template::getName, name)))
                    .toList();
        } catch (Exception e) {
            log.error("接口{}根据条件分析对应模板序列时发生异常", taskType, e);
            throw e;
        }
    }

    @Override
    public Template findTemplateByName(String name) {
        return getOne(new QueryWrapper<Template>().lambda().eq(Template::getName, name));
    }
}
