package com.cheche365.bc.utils.sdas;

import com.cheche365.cheche.signature.spi.PreSignRequest;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @Description:
 * @Date: 2021/1/4 11:19
 */
public class ApiRequest implements PreSignRequest {
    private Map<String, ArrayList<String>> headers = new HashMap<>();

    private Map<String, String> params = new HashMap<>();

    private String requestMethod;

    private String requestURL;

    private Object entity;

    public ApiRequest() {
    }

    @Override
    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String method) {
        requestMethod = method;
    }

    public ApiRequest requestMethod(String method) {
        setRequestMethod(method);
        return this;
    }

    @Override
    public URL getRequestURL() {
        try {
            return new URL(requestURL);
        } catch (MalformedURLException ex) {
            Logger.getLogger(ApiRequest.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public void setRequestURL(String url) {
        requestURL = url;
    }

    public ApiRequest requestURL(String url) {
        setRequestURL(url);
        return this;
    }

    @Override
    public List<String> getHeaderValues(String name) {
        return headers.get(name);
    }

    @Override
    public void addHeaderValue(String name, String value) {
        ArrayList<String> values = headers.get(name);
        if (values == null) {
            values = new ArrayList<String>();
            headers.put(name, values);
        }
        values.add(value);
    }

    @Override
    public String getEntityText() {
        return this.entity.toString();
    }

    public ApiRequest headerValue(String name, String value) {
        addHeaderValue(name, value);
        return this;
    }

    @Override
    public Set<String> getParameterNames() {
        return params.keySet();
    }

    @Override
    public String getParameterValue(String name) {
        return params.get(name);
    }

    @Override
    public Object getEntity() {
        return this.entity;
    }

    public ApiRequest setEntity(Object entity) {
        this.entity = entity;
        return this;
    }

    public synchronized void addParameterValue(String name, String value) {
        params.put(name, value);
    }

    public ApiRequest parameterValue(String name, String value) {
        addParameterValue(name, value);
        return this;
    }


    public void setParams(Map<String, String> params) {
        this.params = params;
    }
}
