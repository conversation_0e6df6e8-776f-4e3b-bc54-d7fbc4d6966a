package com.cheche365.bc.admin.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cheche365.bc.entity.mysql.MenuInfo;
import com.cheche365.bc.dto.MenuPermission;
import com.cheche365.bc.entity.enums.MenuInfoStatus;

import java.util.List;

/**
 * <p>
 * 菜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-15
 */
public interface MenuInfoService extends IService<MenuInfo> {

    List<MenuInfo> listMenuByUserId(Long userId, MenuInfoStatus menuInfoStatus);

    /**
     * 获取所有子菜单以及权限列表
     *
     * @param menuPermission 当前菜单
     */
    void childListByMenu(MenuPermission menuPermission);

    List<MenuPermission> allMenuAndPermission();

}
