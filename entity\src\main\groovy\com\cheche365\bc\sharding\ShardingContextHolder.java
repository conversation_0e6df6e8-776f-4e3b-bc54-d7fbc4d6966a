package com.cheche365.bc.sharding;

/**
 * ThreadLocal工具类，用于在线程内传递操作类型和表选择信息
 * 替代AutoTask实体中的opType字段
 */
public class ShardingContextHolder {

    /**
     * 存储操作类型的ThreadLocal变量
     */
    private static final ThreadLocal<String> OP_TYPE_HOLDER = new ThreadLocal<>();

    /**
     * 存储是否使用旧表的ThreadLocal变量（用于双写控制）
     */
    private static final ThreadLocal<Boolean> USE_OLD_TABLE = new ThreadLocal<>();

    /**
     * 存储是否强制使用新表进行查询的ThreadLocal变量
     */
    private static final ThreadLocal<Boolean> FORCE_QUERY_NEW_TABLE = new ThreadLocal<>();

    /**
     * 设置操作类型
     *
     * @param opType 操作类型（INSERT、UPDATE等）
     */
    public static void setOpType(String opType) {
        OP_TYPE_HOLDER.set(opType);
    }

    /**
     * 获取操作类型
     *
     * @return 操作类型
     */
    public static String getOpType() {
        return OP_TYPE_HOLDER.get();
    }

    /**
     * 清除操作类型
     * 建议在操作完成后调用，避免内存泄漏
     */
    public static void clearOpType() {
        OP_TYPE_HOLDER.remove();
    }

    /**
     *
     */
    public static void setUseOldTable(Boolean useOldTable) {
        USE_OLD_TABLE.set(useOldTable);
    }

    public static Boolean getUseOldTable() {
        return USE_OLD_TABLE.get();
    }

    /**
     * 清除是否使用旧表的标识
     */
    public static void clearUseOldTable() {
        USE_OLD_TABLE.remove();
    }

    /**
     * 设置是否强制使用新表进行查询
     *
     * @param forceQueryNewTable true表示强制使用新表查询，false表示按正常逻辑
     */
    public static void setForceQueryNewTable(Boolean forceQueryNewTable) {
        FORCE_QUERY_NEW_TABLE.set(forceQueryNewTable);
    }

    /**
     * 获取是否强制使用新表进行查询
     *
     * @return true表示强制使用新表查询，false或null表示按正常逻辑
     */
    public static Boolean getForceQueryNewTable() {
        return FORCE_QUERY_NEW_TABLE.get();
    }

    /**
     * 清除强制使用新表查询的标识
     */
    public static void clearForceQueryNewTable() {
        FORCE_QUERY_NEW_TABLE.remove();
    }

    /**
     * 清除所有ThreadLocal变量，防止内存泄漏
     */
    public static void clearAll() {
        clearOpType();
        clearUseOldTable();
        clearForceQueryNewTable();
    }
}
