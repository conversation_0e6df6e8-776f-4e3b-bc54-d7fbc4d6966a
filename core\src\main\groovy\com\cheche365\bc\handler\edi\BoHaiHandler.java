package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.RSA;
import com.cheche365.bc.utils.sender.HttpSender;
import com.cheche365.bc.utils.tai_bao.MessageConstants;
import com.cheche365.bc.utils.tai_bao.SignatureUtils;
import com.google.common.collect.Maps;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
public class BoHaiHandler implements BaseHandler {


    public static final String SYS_SOURCE_CODE = "sysSourceCode";
    public static final String INSURER_CODE = "insurerCode";

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.BPIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        RSA rsa = RSA.createRsa(autoTask);
        Map<String, Object> requestMap = requestMap(autoTask, requestBody, rsa);
        Map<String, String> headers = requestHeader(requestMap, rsa);
        String responseBody = HttpSender.doPostWithRetry(2, closeableHttpClient, true, url, JSON.toJSONString(requestMap), autoTask.getParams(), headers, charSet, TaskUtil.buildReqConfig(autoTask), null);
        JSONObject responseObj = JSONObject.parseObject(responseBody);
        return rsa.decrypt(responseObj.getString(MessageConstants.BIZ_CONTENT));
    }

    private Map<String, Object> requestMap(AutoTask autoTask, String requestBody, RSA rsa) throws Exception {
        Map<String, Object> requestMap = Maps.newHashMap();
        requestMap.put(SYS_SOURCE_CODE, autoTask.getConfigs().get(SYS_SOURCE_CODE));
        requestMap.put(INSURER_CODE, autoTask.getConfigs().get(INSURER_CODE));
        requestMap.put(MessageConstants.BIZ_CONTENT, rsa.encrypt(requestBody));
        return requestMap;
    }

    private Map<String, String> requestHeader(Map<String, Object> requestMap, RSA rsa) throws Exception {
        Map<String, String> headers = new HashMap<>(HttpSender.JSON_HEADERS);
        String signContent = SignatureUtils.getSignContent(requestMap);
        String signStr = rsa.sign(signContent);
        headers.put("Signature", signStr);
        return headers;
    }
}
