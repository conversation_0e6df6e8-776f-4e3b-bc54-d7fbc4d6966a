package com.cheche365.bc.model;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2017/8/30.
 */
public class RiskItem {
    private static Map<String , String> ItemsName = new HashMap<String , String>();
    private static Map<String , String> ItemsCode = new HashMap<String , String>();
    public static Map<String , String> getItemsName(){
        ItemsName.put("VehicleDemageIns","车辆损失险");
        ItemsName.put("ThirdPartyIns","第三者责任险");
        ItemsName.put("DriverIns","司机责任险");
        ItemsName.put("PassengerIns","乘客责任险");
        ItemsName.put("TheftIns","全车盗抢险");
        ItemsName.put("GlassIns","玻璃单独破碎险");
        ItemsName.put("CombustionIns","自燃损失险");
        ItemsName.put("ScratchIns","车身划痕险");
        ItemsName.put("WadingIns","涉水损失险");
        ItemsName.put("SpecifyingPlantCla","指定专修厂");
        ItemsName.put("MirrorLightIns", "倒车镜车灯损坏险");
        ItemsName.put("CarToCarDamageIns","车碰车损失险");
        ItemsName.put("CombustionExclusionCla","自燃免除特约");
        ItemsName.put("WadingExclusionCla","涉水免除特约");
        ItemsName.put("OptionalDeductiblesCla","可选免赔额特约");
        ItemsName.put("AccidentDeductiblesCla","多次事故免赔特约");
        ItemsName.put("NewEquipmentIns","新增设备损失险");
        ItemsName.put("GoodsOnVehicleIns","车上货物责任险");
        ItemsName.put("LossOfBaggageIns","随车行李物品损失保险");
        ItemsName.put("TrainnigCarCla","教练车特约");
        ItemsName.put("VehicleSuspendedIns","机动车停驶损失险");
        ItemsName.put("AccidentfranchiseCla", "事故责任免赔率特约条款");
        ItemsName.put("NcfVehicleDemageIns","附加车辆损失险不计免赔");
        ItemsName.put("NcfThirdPartyIns","附加第三者责任险不计免赔");
        ItemsName.put("NcfDriverIns","附加司机责任险不计免赔");
        ItemsName.put("NcfPassengerIns","附加乘客责任险不计免赔");
        ItemsName.put("NcfTheftIns","附加全车盗抢险不计免赔");
        ItemsName.put("NcfScratchIns","附加车身划痕险不计免赔");
        ItemsName.put("NcfDriverPassengerIns","附加车上人员责任险不计免赔");
        ItemsName.put("NcfCombustionIns","附加自燃损失险不计免赔");
        ItemsName.put("NcfWadingIns","附加涉水损失险不计免赔");
        ItemsName.put("NcfGoodsOnVehicleIns","附加车上货物责任险不计免赔");
        ItemsName.put("NcfNewEquipmentIns","附加新增设备损失险不计免赔	");
        ItemsName.put("NcfBasicClause", "基本险不计免赔");
        ItemsName.put("Ncfaddtionalclause", "附加险不计免赔");
        ItemsName.put("NcfClause", "不计免赔险");
        ItemsName.put("VehicleCompulsoryIns","交强险");
        ItemsName.put("VehicleTax","车船税");

        ItemsName.put("VehicleDemageMissedThirdPartyCla","机动车损失保险无法找到第三方特约险");
        ItemsName.put("CompensationForMentalDistressIns","精神损害抚慰金责任险");
        ItemsName.put("NcfCompensationForMentalDistressIns","附加精神损害抚慰金责任险不计免赔");
        ItemsName.put("CompensationDuringRepairIns","修理期间费用补偿险");
        ItemsName.put("NcfMirrorLightIns","倒车镜不计免赔");
        ItemsName.put("SpecialVehicleExtensionIns","特种车扩展险");
        ItemsName.put("VehicleTaxOverdueFine","车船税滞纳金");

        return ItemsName;
    }

    public static Map<String , String> getItemsCode(){
        ItemsCode.put("VehicleDemageIns","VehicleDemageIns");
        ItemsCode.put("ThirdPartyIns","ThirdPartyIns");
        ItemsCode.put("DriverIns","DriverIns");
        ItemsCode.put("PassengerIns","PassengerIns");
        ItemsCode.put("TheftIns","TheftIns");
        ItemsCode.put("GlassIns","GlassIns");
        ItemsCode.put("CombustionIns","CombustionIns");
        ItemsCode.put("ScratchIns","ScratchIns");
        ItemsCode.put("WadingIns","WadingIns");
        ItemsCode.put("SpecifyingPlantCla","SpecifyingPlantCla");
        ItemsCode.put("MirrorLightIns", "MirrorLightIns");
        ItemsCode.put("CarToCarDamageIns","CarToCarDamageIns");
        ItemsCode.put("CombustionExclusionCla","CombustionExclusionCla");
        ItemsCode.put("WadingExclusionCla","WadingExclusionCla");
        ItemsCode.put("OptionalDeductiblesCla","OptionalDeductiblesCla");
        ItemsCode.put("AccidentDeductiblesCla","AccidentDeductiblesCla");
        ItemsCode.put("NewEquipmentIns","NewEquipmentIns");
        ItemsCode.put("GoodsOnVehicleIns","GoodsOnVehicleIns");
        ItemsCode.put("LossOfBaggageIns","LossOfBaggageIns");
        ItemsCode.put("TrainnigCarCla","TrainnigCarCla");
        ItemsCode.put("VehicleSuspendedIns","VehicleSuspendedIns");
        ItemsCode.put("AccidentfranchiseCla", "AccidentfranchiseCla");
        ItemsCode.put("NcfVehicleDemageIns","NcfVehicleDemageIns");
        ItemsCode.put("NcfThirdPartyIns","NcfThirdPartyIns");
        ItemsCode.put("NcfDriverIns","NcfDriverIns");
        ItemsCode.put("NcfPassengerIns","NcfPassengerIns");
        ItemsCode.put("NcfTheftIns","NcfTheftIns");
        ItemsCode.put("NcfScratchIns","NcfScratchIns");
        ItemsCode.put("NcfDriverPassengerIns","NcfDriverPassengerIns");
        ItemsCode.put("NcfCombustionIns","NcfCombustionIns");
        ItemsCode.put("NcfWadingIns","NcfWadingIns");
        ItemsCode.put("NcfGoodsOnVehicleIns","NcfGoodsOnVehicleIns");
        ItemsCode.put("NcfNewEquipmentIns","NcfNewEquipmentIns	");
        ItemsCode.put("NcfBasicClause", "NcfBasicClause");
        ItemsCode.put("Ncfaddtionalclause", "Ncfaddtionalclause");
        ItemsCode.put("NcfClause", "NcfClause");
        ItemsCode.put("VehicleCompulsoryIns","VehicleCompulsoryIns");
        ItemsCode.put("VehicleTax","VehicleTax");

        ItemsCode.put("VehicleDemageMissedThirdPartyCla","VehicleDemageMissedThirdPartyCla");
        ItemsCode.put("CompensationForMentalDistressIns","CompensationForMentalDistressIns");
        ItemsCode.put("NcfCompensationForMentalDistressIns","NcfCompensationForMentalDistressIns");
        ItemsCode.put("CompensationDuringRepairIns","CompensationDuringRepairIns");
        ItemsCode.put("NcfMirrorLightIns","NcfMirrorLightIns");
        ItemsCode.put("SpecialVehicleExtensionIns","SpecialVehicleExtensionIns");
        ItemsCode.put("VehicleTaxOverdueFine","VehicleTaxOverdueFine");
        return ItemsCode;
    }
}
