dependencies {

    // Other Projects
    implementation project(':claim:claim-service')

    // Spring Frameworks
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.apache.axis:axis:$apache_axis_version"
    runtimeOnly("org.springframework.boot:spring-boot-starter-undertow") {
        exclude group: "jakarta.servlet", module: "jakarta.servlet-api"
    }

}

tasks.register('bootRunClaim') {
    finalizedBy("bootRunDev")
}
