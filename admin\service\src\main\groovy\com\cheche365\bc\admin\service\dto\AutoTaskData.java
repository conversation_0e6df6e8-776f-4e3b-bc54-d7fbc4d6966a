package com.cheche365.bc.admin.service.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.admin.service.dto.base.*;
import com.cheche365.bc.admin.service.dto.excel.BaseQuoteDto;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;

import static com.cheche365.bc.admin.service.dto.base.AreaInfo.createByCityId;
import static com.cheche365.bc.admin.service.dto.base.BaseSuiteInfo.createBaseSuiteInfo;
import static com.cheche365.bc.admin.service.dto.base.NonAutoProduct.createNonAutoProduct;
import static com.cheche365.bc.admin.service.dto.base.PersonInfo.createPersonInfo;

/**
 * <AUTHOR> shanxf
 */
@Data
@Builder
public class AutoTaskData {

    private String configId;
    private AreaInfo insArea;
    private CarInfo carInfo;
    private PersonInfo carOwnerInfo;
    private PersonInfo applicantPersonInfo;
    private PersonInfo insuredPersonInfo;
    private NonAutoProduct nonAutoProduct;
    private BaseSuiteInfo baseSuiteInfo;

    public static AutoTaskData create(BaseQuoteDto baseQuoteDto) {
        return AutoTaskData.builder()
            .configId(baseQuoteDto.getConfigId())
            .insArea(createByCityId(baseQuoteDto.getCityId()))
            .carInfo(copyCarInfo(baseQuoteDto))
            .carOwnerInfo(createPersonInfo(
                baseQuoteDto.getOwnerIdCardType(),
                baseQuoteDto.getOwnerIdCardId(),
                baseQuoteDto.getOwnerMobile(),
                baseQuoteDto.getOwnerName())
            )
            .applicantPersonInfo(createPersonInfo(
                baseQuoteDto.getApplicantIdCardType(),
                baseQuoteDto.getApplicantIdCardId(),
                baseQuoteDto.getApplicantMobile(),
                baseQuoteDto.getApplicantName())
            )
            .insuredPersonInfo(createPersonInfo(
                baseQuoteDto.getInsuredIdCardType(),
                baseQuoteDto.getInsuredIdCardId(),
                baseQuoteDto.getInsuredMobile(),
                baseQuoteDto.getInsuredName())
            )
            .nonAutoProduct(createNonAutoProduct(baseQuoteDto.getNonAutoProductCode()))
            .baseSuiteInfo(createBaseSuiteInfo(baseQuoteDto.getBizStartDate(), baseQuoteDto.getEfcStartDate()))
            .build();
    }

    private static CarInfo copyCarInfo(BaseQuoteDto baseQuoteDto) {
        CarInfo copied = BeanUtil.copyProperties(baseQuoteDto, CarInfo.class, "newCarFlag");
        copied.setNewCarFlag("是".equals(baseQuoteDto.getNewCarFlag()));
        return copied;
    }

    public void check() {
        Assert.notNull(baseSuiteInfo, "险种不能为空");

        BizSuiteInfo bizSuiteInfo = baseSuiteInfo.getBizSuiteInfo();
        if (Objects.nonNull(bizSuiteInfo) && Objects.nonNull(bizSuiteInfo.getSuites())) {
            Assert.notNull(bizSuiteInfo.getStart(), "商业险起保日期不能为空");
        }else {
            baseSuiteInfo.setBizSuiteInfo(null);
        }
        EfcSuiteInfo efcSuiteInfo = baseSuiteInfo.getEfcSuiteInfo();
        if (Objects.nonNull(efcSuiteInfo) && Objects.nonNull(efcSuiteInfo.getAmount())) {
            Assert.notNull(efcSuiteInfo.getStart(), "交强险起保日期不能为空");
        }else {
            baseSuiteInfo.setEfcSuiteInfo(null);
        }
    }
}
