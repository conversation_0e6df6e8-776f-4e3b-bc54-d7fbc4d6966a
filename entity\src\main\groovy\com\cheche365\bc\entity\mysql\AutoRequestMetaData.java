package com.cheche365.bc.entity.mysql;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * 线索推送接口 mongo 实体
 */
@Data
@ToString
@Document(collection = "auto_request_metadata")
public class AutoRequestMetaData {

    /**
     * enquiryId
     */
    @Indexed
    private String enquiryId;

    /**
     * 主动回推url
     */
    private String writeBackUrl;

    /**
     * config 当前 enquiryId 下的 config 配置参数
     */
    private Object config;

    /**
     * createDate 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * taskType 任务类型
     */
    private String taskType;
}
