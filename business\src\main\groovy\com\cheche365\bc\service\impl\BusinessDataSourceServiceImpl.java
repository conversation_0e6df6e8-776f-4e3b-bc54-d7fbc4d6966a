package com.cheche365.bc.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cheche365.bc.entity.mysql.BusinessDataSource;
import com.cheche365.bc.mapper.BusinessDataSourceMapper;
import com.cheche365.bc.service.BusinessDataSourceService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> href="<EMAIL>">蒋昌宝<a/>
 */

@Service
@AllArgsConstructor
public class BusinessDataSourceServiceImpl extends ServiceImpl<BusinessDataSourceMapper, BusinessDataSource> implements BusinessDataSourceService {

    @Override
    public BusinessDataSource getDataSource(String product, String scenario, String channel) {
        return getOne(Wrappers.<BusinessDataSource>lambdaQuery()
                .select(BusinessDataSource::getId, BusinessDataSource::getSourceKind)
                .eq(BusinessDataSource::getSourceProduct, product)
                .eq(BusinessDataSource::getSourceScenario, scenario)
                .eq(StringUtils.isNotBlank(channel), BusinessDataSource::getSourceChannel, channel)
            , false);
    }

    @Override
    public List<BusinessDataSource> getDataSourceWithProductAndScenario(String product, String scenario) {
        return lambdaQuery()
            .select(BusinessDataSource::getId)
            .eq(BusinessDataSource::getSourceProduct, product)
            .eq(BusinessDataSource::getSourceScenario, scenario)
            .list();
    }

    @Override
    public void insertDataSource(String product, String scenario, String channel, int sourceKind, String desc) {
        BusinessDataSource businessDataSource = new BusinessDataSource()
            .setSourceProduct(product)
            .setSourceScenario(scenario)
            .setSourceChannel(channel)
            .setSourceKind(sourceKind)
            .setDescription(desc);
        save(businessDataSource);
    }
}
