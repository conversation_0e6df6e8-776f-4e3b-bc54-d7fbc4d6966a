package com.cheche365.bc.river.rest.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.constants.RequestSourceConstants;
import com.cheche365.bc.entity.mysql.BusinessDataSource;
import com.cheche365.bc.river.rest.dto.request.AutoRequest;
import com.cheche365.bc.service.AutoTaskService;
import com.cheche365.bc.service.BusinessDataSourceAutoTaskLogService;
import com.cheche365.bc.service.BusinessDataSourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.cheche365.bc.constants.Constants.REDIS_LOCK_KEY_PREFIX;
import static com.cheche365.bc.constants.RequestSourceConstants.*;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessDataSourceAspect {

    private final BusinessDataSourceService businessDataSourceService;

    private final BusinessDataSourceAutoTaskLogService businessDataSourceAutoTaskLogService;

    private final AutoTaskService autoTaskService;

    private final RedissonClient redissonClient;

    @AfterReturning(value = "execution(* com.cheche365.bc.river.rest.controller.AutoController.auto(..))", returning = "result")
    public void afterReturning(JoinPoint point, Object result) {
        Map autoMap = (Map) result;
        boolean accept = (boolean) autoMap.get("accept");
        // 若请求未被接受，则不处理
        if (!accept) {
            return;
        }

        RLock lock = null;
        try {
            Object[] args = point.getArgs();
            AutoRequest autoRequest = (AutoRequest) args[0];
            //请求数据，深拷贝
            JSONObject taskApply = JSONObject.parseObject(JSON.toJSONString(autoRequest));
            // 任务ID
            String autoTraceId = autoRequest.getAutoTraceId();

            BusinessDataSource dataSourceResult;
            int dataSourceLogId;

            var requestSource = taskApply.getJSONObject(Keys.REQUEST);
            if (Objects.nonNull(requestSource)) {
                String lockName = REDIS_LOCK_KEY_PREFIX + requestSource.toJSONString();
                lock = redissonClient.getLock(lockName);
                if (!lock.tryLock(0, 300, TimeUnit.MILLISECONDS)) {
                    log.info("lockName 重复，已由其他线程加锁写入数据，enquiryId:{}", taskApply.get("enquiryId"));
                    return;
                }

                // 若请求包含 requestSource，则拿取 requestSource 的字段，通过这三个字段获取此条配置
                //产品
                String product = getValueByKey(requestSource, Keys.PRODUCT);
                //场景
                String scenario = getValueByKey(requestSource, Keys.SCENARIO);
                //渠道
                String channel = getValueByKey(requestSource, Keys.CHANNEL);
                //描述
                String desc = getValueByKey(requestSource, Keys.DESC);

                if (RequestSourceConstants.PRODUCT_OF_CHE_ECO.equals(product) && StringUtils.isBlank(channel)) {
                    log.warn("车生态sourceChannel字段为空，任务号：{}", taskApply.get("enquiryId"));
                    return;
                }

                // 通过 sourceProduct, sourceScenario, sourceChannel 获取配置
                dataSourceResult = businessDataSourceService.getDataSource(product, scenario, channel);

                // 若此配置为空，则新增一条此配置
                if (Objects.isNull(dataSourceResult)) {
                    businessDataSourceService.insertDataSource(product, scenario, channel, 1, desc);
                }

                // 设置日志界面数据来源下拉框 id
                dataSourceLogId = businessDataSourceAutoTaskLogService.getDataSourceLogId(product, scenario, channel);

            } else {
                // 若请求不包含 requestSource，则给予默认 "ps" 标识
                dataSourceResult = businessDataSourceService.getDataSource(DEFAULT_PRODUCT, DEFAULT_SCENARIO, null);
                dataSourceLogId = businessDataSourceAutoTaskLogService.getDataSourceLogId(DEFAULT_PRODUCT, DEFAULT_SCENARIO, null);
            }

            // 判断请求是否来源于内网，若来源于内网，则设置来源归类为6，若不来源于内网，则设置来源归类为 dataSourceResult 此条配置的 sourceKind 字段
            String isSourceCheche = taskApply.containsKey("isSourceCheche") ? taskApply.getString("isSourceCheche") : null;
            int sourceKind = getSourceKind(isSourceCheche, dataSourceResult);

            if (-1 != dataSourceLogId) {
                autoTaskService.updateSourceKindAndDataSourceLogIdById(autoTraceId, sourceKind, dataSourceLogId);
            }

        } catch (Exception e) {
            log.error("任务单号:{} 切面数据新增失败：{}", autoMap.get("taskId"), ExceptionUtils.getStackTrace(e));
        } finally {
            releaseLockSafely(lock, autoMap.get("taskId"));
        }
    }

    private static int getSourceKind(String isSourceCheche, BusinessDataSource dataSourceResult) {
        return "0".equals(isSourceCheche) ?
            Objects.isNull(dataSourceResult) ? 1 : dataSourceResult.getSourceKind() : 6;
    }

    public String getValueByKey(JSONObject requestSource, String key) {
        if (requestSource.containsKey(key)) {
            return requestSource.getString(key);
        }

        return null;
    }

    /**
     * 释放锁
     *
     * @param lock
     * @param taskId
     */
    private void releaseLockSafely(RLock lock, Object taskId) {
        if (lock != null && lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.error("任务单号:{} 释放锁失败：{}", taskId, ExceptionUtils.getStackTrace(e));
            }
        }
    }

}
