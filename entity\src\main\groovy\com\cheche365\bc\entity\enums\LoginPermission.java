package com.cheche365.bc.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

/**
 * author:<PERSON><PERSON><PERSON>liang
 * Date:2019/12/10 16:02
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum LoginPermission {

    ONLINE("1", "线上"),
    OFFLINE("2", "线下"),
    ON_OFF_LINE("1,2", "线上/线下"),
    ;

    @EnumValue
    private final String code;

    private final String text;

    LoginPermission(String code, String text) {
        this.code = code;
        this.text = text;
    }

    @JsonCreator
    public static LoginPermission get(String code) {
        return Arrays.stream(values()).filter(it -> it.getCode().equals(code)).findAny().orElse(null);
    }

    @Override
    public String toString() {
        return text;
    }

}
