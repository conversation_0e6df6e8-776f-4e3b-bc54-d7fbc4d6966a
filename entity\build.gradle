configurations.all {
    resolutionStrategy {
        force 'org.codehaus.groovy:groovy:3.0.9'
        force 'org.codehaus.groovy:groovy-all:3.0.9'
    }
}
dependencies {
    api project(':core')

    // Spring Frameworks
//    api "com.baomidou:dynamic-datasource-spring-boot3-starter:$dynamic_datasource_starter_version"
    api "org.springframework.boot:spring-boot-starter-aop"
    api "org.springframework.boot:spring-boot-starter-actuator"

    testImplementation "com.baomidou:mybatis-plus-generator:$mybatis_plus_version"

    // DB
    implementation "mysql:mysql-connector-java:$mysql_connector_java_version",
        "com.alibaba:druid:$alibaba_druid_starter_version"

    implementation "org.apache.velocity:velocity-engine-core:$apache_velocity_engine_version"
    implementation "org.hibernate.validator:hibernate-validator:$hibernate_validator_version"
    api "commons-io:commons-io:$apache_commons_io_version",
        "com.fasterxml.jackson.core:jackson-annotations:$jackson_annotations_version"
    implementation("org.apache.shardingsphere:shardingsphere-jdbc:$shardingsphere_jdbc_version") {
        exclude group: 'org.apache.groovy', module: 'groovy'
    }
    api "org.apache.hbase:hbase-client:$hbase_client_version"
    api "com.volcengine:hbase-security:$hbase_security_version"
}
