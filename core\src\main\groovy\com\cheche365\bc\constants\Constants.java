package com.cheche365.bc.constants;

public class Constants {

    public static final String INTERFACE_CACHE_KEY_PREFIX = "interface:";
    public static final String PLATFORM_INFO_CODE_CACHE_KEY_PREFIX = "platformInfoCode:";
    public static final String SESSION_CACHE_KEY_PREFIX = "sessionKey:";
    public static final String REDIS_LOCK_KEY_PREFIX = "redisLockKey:";
    public static final String COOKIE_STRING_CACHE_KEY_PREFIX = "cookieStrKey:";
    public static final String AUTHORIZATION_CACHE_KEY_PREFIX = "authorizationKey:";
    public static final String HTTPCLIENT_CHANNEL_GET = "http_get";
    public static final String HTTPCLIENT_CHANNEL_POST = "http_post";
    public static final String HTTPCLIENT_CHANNEL_DELETE = "http_delete";
    public static final String HTTPCLIENT_CHANNEL_PUT = "http_put";
    public static final String AXIS2_CHANNEL = "axis2";
    public static final String TCP_CHANNEL = "tcp";
    public static final String NO_MOTOR = "nonMotor";

    // 多个非车险节点
    public static final String NON_MOTORS = "nonMotors";

    public static final String SECURE_APP_KEY = "appKey";
    //edi token缓存
    public static final String EDI_TOKEN = "ediToken:";
    public final static String LOGIN_FAIL_ACCOUNTS = "auto:account:illegal:";
    public static final String WorkerId = "workerId";
    public static final String OBS_POLICY_PATH_PREFIX = "river_attachment";

    public static final String EMAIL_HOST_KEY = "mail.imap.host";
    public static final String EMAIL_PORT_KEY = "mail.imap.port";
    public static final String EMAIL_PROTOCOL_KEY = "mail.store.protocol";
    public static final String EMAIL_SSL_ENABLE_KEY = "mail.imap.ssl.enable";
    public static final String EMAIL_FETCHSIZE_KEY = "mail.imap.fetchsize";

    public static final String PDF_CONTENT_TYPE = "application/pdf";

    /**
     * 交强险上传的数字资产 ID
     */
    public static final String POLICY_SOURCE_EFC = "efcId";

    /**
     * 商业险上传的数字资产 ID
     */
    public static final String POLICY_SOURCE_BIZ = "bizId";

    /**
     * 非车险上传的数字资产 ID
     */
    public static final String POLICY_SOURCE_NON_MOTOR = "nonMotorId";

    public static final String CLUE_PUSH = "cluePush";
    public static final String CLUE_PUSH_ENQUIRY_ID = "cluePush-enquiryId:";
    public static final String KILL_WIN = "killWin";


    /**
     * 回写业务端保司数据节点名称
     */
    public static final String DEFINITION = "definition";

    public static Integer convertPolicyType(String policySource) {
        return switch (policySource) {
            case POLICY_SOURCE_EFC:
                yield 1;
            case POLICY_SOURCE_BIZ:
                yield 2;
            case POLICY_SOURCE_NON_MOTOR:
                yield 3;
            default:
                throw new RuntimeException("无效的policySource");
        };
    }

}
