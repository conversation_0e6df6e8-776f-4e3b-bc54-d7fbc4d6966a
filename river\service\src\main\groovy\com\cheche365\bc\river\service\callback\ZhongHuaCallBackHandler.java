package com.cheche365.bc.river.service.callback;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.zhonghualianhe.util.ZhongHuaRSAUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class ZhongHuaCallBackHandler extends AbstractCallBackHandler {

    @Override
    public boolean needHandle(String companyId) {
        return String.valueOf(InsCompanyEnum.CICP.getCode()).equals(companyId);
    }

    @Override
    public String handleCallBackBody(Map param) {
        String callbackBody = (String) param.get("callbackBody");
        if (!callbackBody.contains(",")) {
            throw new RuntimeException("中华回调报文格式异常");
        }
        Map dataSource = (Map) param.get("dataSource");
        try {
            return ZhongHuaRSAUtils.checkSignAndDecrypt(callbackBody.split(",")[0], callbackBody.split(",")[1], (String) DataUtil.get("config.publicKey", dataSource), (String) DataUtil.get("config.thirdPrivateKey", dataSource), "UTF-8", true, true);
        } catch (Exception e) {
            log.error("中华回调解析保司报文异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }

    }

    @Override
    public String handleResponseBody(Map param) {
        String responseContent = (String) param.get("responseContent");
        Map dataSource = (Map) param.get("dataSource");
        try {
            Map<String, String> content = ZhongHuaRSAUtils.encryptAndSign(responseContent, (String) DataUtil.get("config.publicKey", dataSource), (String) DataUtil.get("config.thirdPrivateKey", dataSource), "UTF-8", true, true);
            return  JSONObject.toJSONString(content);
        } catch (Exception e) {
            log.error("中华回调响应保司报文渲染异常：{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }

    }
}
